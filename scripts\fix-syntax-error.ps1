#!/usr/bin/env pwsh
# Fix syntax error by clearing Next.js cache and rebuilding

Write-Host "🔧 Fixing syntax error in layout.js..." -ForegroundColor Yellow

# Stop any running Node processes on port 3000
Write-Host "Stopping any processes on port 3000..." -ForegroundColor Cyan
try {
    $process = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess -Unique
    if ($process) {
        Stop-Process -Id $process -Force
        Write-Host "✅ Stopped process on port 3000" -ForegroundColor Green
    }
} catch {
    Write-Host "No process found on port 3000" -ForegroundColor Gray
}

# Clear Next.js cache
Write-Host "`n📦 Clearing Next.js cache..." -ForegroundColor Cyan
if (Test-Path ".next") {
    Remove-Item -Path ".next" -Recurse -Force
    Write-Host "✅ Cleared .next directory" -ForegroundColor Green
}

# Clear node_modules cache
Write-Host "`n🧹 Clearing module cache..." -ForegroundColor Cyan
if (Test-Path "node_modules/.cache") {
    Remove-Item -Path "node_modules/.cache" -Recurse -Force
    Write-Host "✅ Cleared node_modules cache" -ForegroundColor Green
}

# Ensure all dependencies are installed
Write-Host "`n📥 Verifying dependencies..." -ForegroundColor Cyan
npm install

# Start the development server
Write-Host "`n🚀 Starting development server..." -ForegroundColor Yellow
Write-Host "Please wait for the server to start, then:" -ForegroundColor White
Write-Host "1. Open your browser to http://localhost:3000" -ForegroundColor White
Write-Host "2. Press Ctrl+Shift+R (or Cmd+Shift+R on Mac) to hard refresh" -ForegroundColor White
Write-Host "3. Check the browser console for any remaining errors" -ForegroundColor White
Write-Host "`nPress Ctrl+C to stop the server when done." -ForegroundColor Gray

npm run dev
