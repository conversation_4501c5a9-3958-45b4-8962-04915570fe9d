# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AI-powered construction management platform combining features from industry leaders (Procore, OpenSpace, ALICE Technologies, etc.) into a unified solution.

## Essential Commands

```bash
# Development
npm run dev          # Start development server on port 3000

# Code Quality - ALWAYS run before completing tasks
npm run lint         # Run ESLint checks
npm run type-check   # TypeScript type checking

# Testing
npm run test         # Run Jest tests
npm run test:watch   # Run tests in watch mode

# Production
npm run build        # Build for production
npm run start        # Start production server
```

## Architecture Overview

### Tech Stack
- **Frontend**: Next.js 14 (App Router), React 18, TypeScript
- **UI**: Tailwind CSS, shadcn/ui (Radix UI), Framer Motion
- **AI**: Google Gemini AI (gemini-2.0-flash model) with construction-specific contexts
- **State**: <PERSON>ustand (client), React Query (server), React Hook Form + Zod
- **3D/Viz**: Three.js, React Three Fiber, Mapbox GL, Recharts
- **Real-time**: Socket.io, WebRTC

### Key Architectural Patterns

1. **AI Integration Pattern** (src/lib/gemini.ts)
   - Singleton service with company-specific contexts
   - 20+ contractor specializations with tailored AI responses
   - Structured prompts for construction analysis (progress, safety, schedule, cost, quality)

2. **Type System** (src/types/, src/lib/company-types.ts)
   - Comprehensive TypeScript types for all entities
   - Company-specific details mapping
   - Zod validation schemas

3. **Component Architecture**
   - Feature-based organization in src/features/
   - Server/Client component separation
   - Compound component patterns

### Directory Structure
```
src/app/dashboard/     # Main application routes
├── projects/         # Multi-project management
├── scheduling/       # AI-powered scheduling
├── progress/         # Progress tracking with BIM
├── safety/          # Predictive safety monitoring
├── estimating/      # AI takeoff & estimating
├── field/           # Field operations & robotics
├── analytics/       # KPI & predictive analytics
├── ai-assistant/    # Gemini-powered chat
└── settings/        # Configuration

src/lib/             # Core services
├── gemini.ts       # AI service singleton
├── company-types.ts # Company specializations
└── services/       # Business logic
```

### Environment Configuration

Required environment variables:
- `GEMINI_API_KEY`: Google Gemini API key
- `GEMINI_MODEL`: AI model (default: gemini-2.0-flash)
- `DATABASE_URL`: PostgreSQL connection
- `JWT_SECRET`: Authentication secret
- `NEXT_PUBLIC_MAPBOX_TOKEN`: Mapbox for geographic features

### Critical Implementation Details

1. **AI Context Enhancement**: All AI interactions automatically include project context (budget, schedule, safety metrics) when available

2. **Company-Specific AI**: The system adapts AI responses based on contractor type (General, Electrical, Plumbing, etc.) with specialized knowledge

3. **Error Handling**: Gemini service includes graceful fallbacks for API limits, timeouts, and configuration issues

4. **Real-time Features**: Socket.io integration for collaborative features across project teams

5. **Type Safety**: Strict TypeScript mode with comprehensive types for all construction entities

### Development Workflow

1. Always check existing patterns before implementing new features
2. Use path aliases (@/) for imports
3. Follow existing component structure and naming conventions
4. Run lint and type-check before considering tasks complete
5. Test with multiple contractor types to ensure AI responses are appropriate