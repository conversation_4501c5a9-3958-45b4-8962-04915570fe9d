import { create } from 'zustand'
import { BIMModel, BIMElement, BIMViewerState } from '@/types'
import { ViewerTools, SavedView, MeasurementData, ViewerAnnotation } from '../types/bim.types'

interface ViewerStore extends BIMViewerState {
  // Model
  model: BIMModel | null
  setModel: (model: BIMModel | null) => void
  
  // Selection
  selectElement: (elementId: string, multiSelect?: boolean) => void
  clearSelection: () => void
  
  // Visibility
  hideElement: (elementId: string) => void
  showElement: (elementId: string) => void
  setElementsVisibility: (elementIds: string[], visible: boolean) => void
  isolateElements: (elementIds: string[]) => void
  showAllElements: () => void
  
  // View mode
  setViewMode: (mode: BIMViewerState['viewMode']) => void
  
  // Camera
  setCameraPosition: (position: { x: number; y: number; z: number }) => void
  setCameraTarget: (target: { x: number; y: number; z: number }) => void
  
  // Floor management
  setActiveFloor: (floor: number | undefined) => void
  
  // Tools
  tools: ViewerTools
  setTool: (tool: keyof ViewerTools, active: boolean) => void
  
  // Saved views
  savedViews: SavedView[]
  saveView: (name: string) => void
  loadView: (view: SavedView) => void
  deleteView: (viewId: string) => void
  
  // Measurements
  measurements: MeasurementData[]
  addMeasurement: (measurement: MeasurementData) => void
  removeMeasurement: (measurementId: string) => void
  clearMeasurements: () => void
  
  // Annotations
  annotations: ViewerAnnotation[]
  addAnnotation: (annotation: ViewerAnnotation) => void
  updateAnnotation: (annotationId: string, updates: Partial<ViewerAnnotation>) => void
  removeAnnotation: (annotationId: string) => void
  
  // Progress tracking
  updateElementProgress: (elementId: string, progress: number) => void
  
  // Issues
  addElementIssue: (elementId: string, issue: string) => void
  resolveElementIssue: (elementId: string, issueIndex: number) => void
}

export const useViewerState = create<ViewerStore>((set, get) => ({
  // Initial state
  model: null,
  selectedElements: [],
  hiddenElements: [],
  viewMode: '3D',
  cameraPosition: { x: 20, y: 20, z: 20 },
  cameraTarget: { x: 0, y: 0, z: 0 },
  activeFloor: undefined,
  clippingPlanes: [],
  tools: {
    select: true,
    measure: false,
    section: false,
    annotate: false,
    isolate: false,
    explode: false,
  },
  savedViews: [],
  measurements: [],
  annotations: [],
  
  // Model actions
  setModel: (model) => set({ model }),
  
  // Selection actions
  selectElement: (elementId, multiSelect = false) => set((state) => {
    if (multiSelect) {
      const isSelected = state.selectedElements.includes(elementId)
      return {
        selectedElements: isSelected
          ? state.selectedElements.filter(id => id !== elementId)
          : [...state.selectedElements, elementId]
      }
    }
    return { selectedElements: [elementId] }
  }),
  
  clearSelection: () => set({ selectedElements: [] }),
  
  // Visibility actions
  hideElement: (elementId) => set((state) => ({
    hiddenElements: [...state.hiddenElements, elementId]
  })),
  
  showElement: (elementId) => set((state) => ({
    hiddenElements: state.hiddenElements.filter(id => id !== elementId)
  })),
  
  setElementsVisibility: (elementIds, visible) => set((state) => {
    if (visible) {
      return {
        hiddenElements: state.hiddenElements.filter(id => !elementIds.includes(id))
      }
    }
    return {
      hiddenElements: Array.from(new Set([...state.hiddenElements, ...elementIds]))
    }
  }),
  
  isolateElements: (elementIds) => set((state) => {
    if (!state.model) return state
    const allIds = state.model.elements.map(e => e.id)
    const toHide = allIds.filter(id => !elementIds.includes(id))
    return { hiddenElements: toHide }
  }),
  
  showAllElements: () => set({ hiddenElements: [] }),
  
  // View actions
  setViewMode: (viewMode) => set({ viewMode }),
  setCameraPosition: (cameraPosition) => set({ cameraPosition }),
  setCameraTarget: (cameraTarget) => set({ cameraTarget }),
  setActiveFloor: (activeFloor) => set({ activeFloor }),
  
  // Tool actions
  setTool: (tool, active) => set((state) => ({
    tools: { ...state.tools, [tool]: active }
  })),
  
  // Saved views actions
  saveView: (name) => set((state) => {
    const view: SavedView = {
      id: crypto.randomUUID(),
      name,
      camera: {
        position: state.cameraPosition,
        target: state.cameraTarget,
        fov: 45,
        near: 0.1,
        far: 1000,
      },
      hiddenElements: [...state.hiddenElements],
      clippingPlanes: state.clippingPlanes?.map(p => ({
        normal: p.normal,
        position: p.position,
      })) || [],
      timestamp: new Date(),
    }
    return { savedViews: [...state.savedViews, view] }
  }),
  
  loadView: (view) => set({
    cameraPosition: view.camera.position,
    cameraTarget: view.camera.target,
    hiddenElements: [...view.hiddenElements],
    clippingPlanes: view.clippingPlanes.map(p => ({
      id: crypto.randomUUID(),
      ...p,
      active: true,
    })),
  }),
  
  deleteView: (viewId) => set((state) => ({
    savedViews: state.savedViews.filter(v => v.id !== viewId)
  })),
  
  // Measurement actions
  addMeasurement: (measurement) => set((state) => ({
    measurements: [...state.measurements, measurement]
  })),
  
  removeMeasurement: (measurementId) => set((state) => ({
    measurements: state.measurements.filter(m => m.id !== measurementId)
  })),
  
  clearMeasurements: () => set({ measurements: [] }),
  
  // Annotation actions
  addAnnotation: (annotation) => set((state) => ({
    annotations: [...state.annotations, annotation]
  })),
  
  updateAnnotation: (annotationId, updates) => set((state) => ({
    annotations: state.annotations.map(a =>
      a.id === annotationId ? { ...a, ...updates, updatedAt: new Date() } : a
    )
  })),
  
  removeAnnotation: (annotationId) => set((state) => ({
    annotations: state.annotations.filter(a => a.id !== annotationId)
  })),
  
  // Progress actions
  updateElementProgress: (elementId, progress) => set((state) => {
    if (!state.model) return state
    return {
      model: {
        ...state.model,
        elements: state.model.elements.map(e =>
          e.id === elementId ? { ...e, progress } : e
        )
      }
    }
  }),
  
  // Issue actions
  addElementIssue: (elementId, issue) => set((state) => {
    if (!state.model) return state
    return {
      model: {
        ...state.model,
        elements: state.model.elements.map(e =>
          e.id === elementId
            ? { ...e, issues: [...(e.issues || []), issue] }
            : e
        )
      }
    }
  }),
  
  resolveElementIssue: (elementId, issueIndex) => set((state) => {
    if (!state.model) return state
    return {
      model: {
        ...state.model,
        elements: state.model.elements.map(e =>
          e.id === elementId
            ? {
                ...e,
                issues: e.issues?.filter((_, i) => i !== issueIndex) || []
              }
            : e
        )
      }
    }
  }),
}))