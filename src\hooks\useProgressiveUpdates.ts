import { useState, useEffect, useCallback, useRef } from 'react'

interface ProgressUpdate {
  step: string
  progress: number
  timestamp: number
  data?: any
}

interface UseProgressiveUpdatesOptions {
  sessionId: string
  onProgress?: (update: ProgressUpdate) => void
  onComplete?: (finalUpdate: ProgressUpdate) => void
  onError?: (error: Error) => void
  autoConnect?: boolean
}

export function useProgressiveUpdates({
  sessionId,
  onProgress,
  onComplete,
  onError,
  autoConnect = true
}: UseProgressiveUpdatesOptions) {
  const [isConnected, setIsConnected] = useState(false)
  const [currentProgress, setCurrentProgress] = useState<ProgressUpdate | null>(null)
  const [error, setError] = useState<Error | null>(null)
  const eventSourceRef = useRef<EventSource | null>(null)

  const connect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
    }

    try {
      const eventSource = new EventSource(`/api/takeoff/progress?sessionId=${sessionId}`)
      eventSourceRef.current = eventSource

      eventSource.onopen = () => {
        setIsConnected(true)
        setError(null)
      }

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          
          if (data.type === 'connected') {
            console.log('Progress stream connected:', data)
            return
          }
          
          if (data.type === 'progress') {
            const update: ProgressUpdate = {
              step: data.step,
              progress: data.progress,
              timestamp: data.timestamp,
              data: data.data
            }
            
            setCurrentProgress(update)
            onProgress?.(update)
            
            // Check if complete
            if (update.progress >= 100) {
              onComplete?.(update)
              // Use ref to avoid dependency cycle
              if (eventSourceRef.current) {
                eventSourceRef.current.close()
                eventSourceRef.current = null
              }
              setIsConnected(false)
            }
          }
        } catch (parseError) {
          console.error('Failed to parse progress update:', parseError)
        }
      }

      eventSource.onerror = (event) => {
        const error = new Error('Progress stream error')
        setError(error)
        setIsConnected(false)
        onError?.(error)
      }
    } catch (connectionError) {
      const error = connectionError instanceof Error ? connectionError : new Error('Failed to connect')
      setError(error)
      onError?.(error)
    }
  }, [sessionId, onProgress, onComplete, onError])

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }
    setIsConnected(false)
  }, [])

  const updateProgress = useCallback(async (step: string, progress: number, data?: any) => {
    try {
      await fetch('/api/takeoff/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          step,
          progress,
          data
        })
      })
    } catch (error) {
      console.error('Failed to update progress:', error)
    }
  }, [sessionId])

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (autoConnect && sessionId) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [autoConnect, sessionId, connect, disconnect])

  return {
    isConnected,
    currentProgress,
    error,
    connect,
    disconnect,
    updateProgress
  }
}

// Utility hook for generating session IDs
export function useSessionId() {
  const [sessionId] = useState(() => `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
  return sessionId
}

// Hook for takeoff progress specifically
export function useTakeoffProgress(sessionId: string) {
  const [progressHistory, setProgressHistory] = useState<ProgressUpdate[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null)

  const { isConnected, currentProgress, error, connect, disconnect } = useProgressiveUpdates({
    sessionId,
    onProgress: (update) => {
      setProgressHistory(prev => [...prev, update])
      setIsProcessing(update.progress < 100)
      
      // Calculate estimated time remaining
      if (progressHistory.length > 1 && update.progress > 0 && update.progress < 100) {
        const firstUpdate = progressHistory[0]
        const timeElapsed = update.timestamp - firstUpdate.timestamp
        const progressMade = update.progress - (firstUpdate.progress || 0)
        
        if (progressMade > 0) {
          const timePerPercent = timeElapsed / progressMade
          const remainingProgress = 100 - update.progress
          const estimatedRemaining = remainingProgress * timePerPercent
          setEstimatedTimeRemaining(Math.round(estimatedRemaining / 1000)) // Convert to seconds
        }
      }
    },
    onComplete: () => {
      setIsProcessing(false)
      setEstimatedTimeRemaining(null)
    },
    onError: () => {
      setIsProcessing(false)
      setEstimatedTimeRemaining(null)
    }
  })

  const reset = useCallback(() => {
    setProgressHistory([])
    setIsProcessing(false)
    setEstimatedTimeRemaining(null)
  }, [])

  return {
    isConnected,
    currentProgress,
    progressHistory,
    isProcessing,
    estimatedTimeRemaining,
    error,
    connect,
    disconnect,
    reset
  }
}
