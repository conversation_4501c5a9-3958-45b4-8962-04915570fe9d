# Fix Database Connection for Windows
Write-Host "AI Construction Management - Database Setup Helper" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""

# Check if PostgreSQL is installed
$pgVersion = psql --version 2>$null
if (-not $pgVersion) {
    Write-Host "PostgreSQL is not installed or not in PATH!" -ForegroundColor Red
    Write-Host "Please install PostgreSQL from: https://www.postgresql.org/download/windows/" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "After installation, add PostgreSQL bin folder to your PATH:" -ForegroundColor Yellow
    Write-Host "Usually located at: C:\Program Files\PostgreSQL\[version]\bin" -ForegroundColor Yellow
    exit 1
}

Write-Host "PostgreSQL found: $pgVersion" -ForegroundColor Green
Write-Host ""

# Database configuration
$dbName = "ai_construction_db"
$dbUser = "postgres"
$dbHost = "localhost"
$dbPort = "5432"

Write-Host "Database Configuration:" -ForegroundColor Yellow
Write-Host "  Database: $dbName"
Write-Host "  User: $dbUser"
Write-Host "  Host: $dbHost"
Write-Host "  Port: $dbPort"
Write-Host ""

# Try to create database with Windows authentication
Write-Host "Attempting to create database..." -ForegroundColor Yellow
$env:PGPASSWORD = Read-Host -Prompt "Enter PostgreSQL password for user 'postgres'" -AsSecureString | ConvertFrom-SecureString -AsPlainText

# Check if we can connect
$testConnection = psql -U $dbUser -h $dbHost -p $dbPort -c "SELECT 1;" postgres 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to connect to PostgreSQL!" -ForegroundColor Red
    Write-Host "Error: $testConnection" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Ensure PostgreSQL service is running (check Windows Services)" -ForegroundColor White
    Write-Host "2. Check if the password is correct" -ForegroundColor White
    Write-Host "3. Verify PostgreSQL is listening on localhost:5432" -ForegroundColor White
    Write-Host "4. Check pg_hba.conf for authentication settings" -ForegroundColor White
    exit 1
}

Write-Host "Successfully connected to PostgreSQL!" -ForegroundColor Green

# Create database
Write-Host "Creating database '$dbName'..." -ForegroundColor Yellow
$createDb = psql -U $dbUser -h $dbHost -p $dbPort -c "CREATE DATABASE $dbName;" postgres 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "Database created successfully!" -ForegroundColor Green
} else {
    if ($createDb -match "already exists") {
        Write-Host "Database already exists, continuing..." -ForegroundColor Yellow
    } else {
        Write-Host "Failed to create database!" -ForegroundColor Red
        Write-Host "Error: $createDb" -ForegroundColor Red
        exit 1
    }
}

# Update .env.local with the correct password
Write-Host ""
Write-Host "Updating .env.local with database configuration..." -ForegroundColor Yellow
$envPath = ".env.local"
if (Test-Path $envPath) {
    $envContent = Get-Content $envPath
    $password = $env:PGPASSWORD
    $newDbUrl = "postgresql://${dbUser}:${password}@${dbHost}:${dbPort}/${dbName}"
    
    $envContent = $envContent -replace 'DATABASE_URL=.*', "DATABASE_URL=$newDbUrl"
    Set-Content -Path $envPath -Value $envContent
    Write-Host ".env.local updated successfully!" -ForegroundColor Green
}

# Run Prisma commands
Write-Host ""
Write-Host "Running Prisma setup..." -ForegroundColor Yellow

Write-Host "1. Generating Prisma Client..." -ForegroundColor Cyan
npm run db:generate
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to generate Prisma Client!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "2. Pushing database schema..." -ForegroundColor Cyan
npm run db:push
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to push database schema!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "3. Seeding database (optional)..." -ForegroundColor Cyan
npm run db:seed
if ($LASTEXITCODE -ne 0) {
    Write-Host "Database seeding failed (this is optional)" -ForegroundColor Yellow
} else {
    Write-Host "Database seeded successfully!" -ForegroundColor Green
}

Write-Host ""
Write-Host "=================================================" -ForegroundColor Green
Write-Host "Database setup completed successfully!" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green
Write-Host ""
Write-Host "You can now run 'npm run dev' to start the application" -ForegroundColor Cyan
Write-Host ""
Write-Host "Demo credentials:" -ForegroundColor Yellow
Write-Host "  Admin: <EMAIL> / demo123456" -ForegroundColor White
Write-Host "  Contractor: <EMAIL> / demo123456" -ForegroundColor White
Write-Host ""