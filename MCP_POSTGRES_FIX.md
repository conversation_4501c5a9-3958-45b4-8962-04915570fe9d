# PostgreSQL MCP Server Configuration Fix

## Issue
The PostgreSQL MCP server is failing to connect because it's configured with incorrect credentials. The current configuration uses `postgres:postgres` but your actual password is `itsMike818!`.

## Solution Steps

### 1. Test PostgreSQL Connection
First, verify your PostgreSQL password and database:
```bash
./test-postgres-connection.sh
```

### 2. Fix MCP Configuration
Run the fix script from your local terminal (where Claude <PERSON> is available):
```bash
./fix-postgres-mcp.sh
```

### 3. Alternative Manual Fix
If the script doesn't work, manually reconfigure:
```bash
# Remove existing server
claude mcp remove postgres

# Add with correct credentials
claude mcp add postgres env DATABASE_URL=postgresql://postgres:itsMike818!@localhost:5432/ai_construction_db npx -y @modelcontextprotocol/server-postgres
```

### 4. Verify Configuration
Check that the server is properly configured:
```bash
claude mcp list
```

## Important Notes

1. **Security**: The GitHub token in `setup-mcp-servers.sh` (****************************************) is exposed and should be regenerated and stored securely.

2. **Database Name**: The MCP server is now configured to use `ai_construction_db` (matching your .env.example) instead of `aceserve`.

3. **Environment Variables**: Consider using a `.env` file for MCP configuration to avoid hardcoding sensitive credentials.

## Updated Files
- `fix-postgres-mcp.sh` - Script to fix the PostgreSQL MCP configuration
- `test-postgres-connection.sh` - Script to test PostgreSQL connectivity
- `setup-mcp-servers.sh` - Updated to use environment variables for DATABASE_URL