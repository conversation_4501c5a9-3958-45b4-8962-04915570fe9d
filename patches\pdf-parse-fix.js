const fs = require('fs');
const path = require('path');

// Fix pdf-parse module that tries to load test data on import
const pdfParsePath = path.join(__dirname, '..', 'node_modules', 'pdf-parse', 'index.js');

if (fs.existsSync(pdfParsePath)) {
  let content = fs.readFileSync(pdfParsePath, 'utf8');
  
  // Replace the problematic test code
  const problematicCode = `if(PDFJS && !module.parent) {
    // bundled test data
    let PDF_FILE = './test/data/05-versions-space.pdf';
    fs.readFile(PDF_FILE, (err, data) => {
        if(err) return console.log(err);
        pdf_parse(data).then(function(ret) {
            console.log(ret);
        });
    });
}`;

  const fixedCode = `// Test code disabled to prevent startup issues
// The module was trying to load './test/data/05-versions-space.pdf' on import`;

  if (content.includes('./test/data/05-versions-space.pdf')) {
    content = content.replace(problematicCode, fixedCode);
    fs.writeFileSync(pdfParsePath, content);
    console.log('✅ Fixed pdf-parse module test code');
  } else {
    console.log('ℹ️ pdf-parse module already fixed or different version');
  }
} else {
  console.log('⚠️ pdf-parse module not found');
}