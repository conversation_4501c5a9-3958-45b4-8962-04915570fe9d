// Centralized auth configuration to ensure consistency
// This file ensures JWT_SECRET is consistently accessed across all environments

// IMPORTANT: Due to Next.js Edge Runtime limitations in middleware,
// we need to use a consistent secret across all environments
const HARDCODED_JWT_SECRET = 'your-super-secret-jwt-key-change-this-in-production';

export const AUTH_CONFIG = {
  JWT_SECRET: process.env.JWT_SECRET || HARDCODED_JWT_SECRET,
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
  SESSION_COOKIE_NAME: 'session',
  // For middleware compatibility, expose the hardcoded secret
  MIDDLEWARE_JWT_SECRET: process.env.JWT_SECRET || HARDCODED_JWT_SECRET,
} as const;

// Debug function
export function getAuthConfig() {
  console.log('Auth Config Debug:');
  console.log('- JWT_SECRET from env:', process.env.JWT_SECRET ? 'Set' : 'Not set');
  console.log('- Using JWT_SECRET:', AUTH_CONFIG.JWT_SECRET.substring(0, 20) + '...');
  return AUTH_CONFIG;
}