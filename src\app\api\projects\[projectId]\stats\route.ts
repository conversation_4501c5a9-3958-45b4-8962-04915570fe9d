import { NextRequest, NextResponse } from 'next/server'
import { getProjectStats } from '@/lib/db/projects'

// GET /api/projects/[projectId]/stats - Get project statistics
export async function GET(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const stats = await getProjectStats(params.projectId)
    
    return NextResponse.json({ stats })
  } catch (error) {
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    if (error instanceof Error && error.message === 'Project not found') {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      )
    }
    
    console.error('Get project stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch project statistics' },
      { status: 500 }
    )
  }
}