/**
 * Pricing Database Service
 * Provides realistic construction material and labor pricing based on RSMeans data
 */

import type { CompanyType } from '@/lib/company-types'

export interface MaterialPrice {
  category: string
  description: string
  unit: string
  materialCost: number
  laborCost: number
  equipmentCost: number
  totalCost: number
  source: 'rsmeans' | 'market' | 'estimated'
  lastUpdated: Date
  location: string
  notes?: string
}

export interface LaborRate {
  trade: string
  classification: string
  baseRate: number // per hour
  benefits: number // per hour
  totalRate: number // per hour
  location: string
}

// RSMeans-based pricing data (2024 National Average)
// Actual implementation would connect to RSMeans API or database
const BASE_MATERIAL_PRICES: Record<string, MaterialPrice[]> = {
  // General Contractor Materials
  'Concrete': [
    {
      category: 'Concrete',
      description: 'Concrete 3000 PSI',
      unit: 'CY',
      materialCost: 130,
      laborCost: 35,
      equipmentCost: 10,
      totalCost: 175,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Concrete',
      description: 'Concrete 4000 PSI',
      unit: 'CY',
      materialCost: 140,
      laborCost: 35,
      equipmentCost: 10,
      totalCost: 185,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Concrete',
      description: 'Concrete 5000 PSI',
      unit: 'CY',
      materialCost: 155,
      laborCost: 35,
      equipmentCost: 10,
      totalCost: 200,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Concrete',
      description: 'Rebar #4',
      unit: 'LB',
      materialCost: 0.85,
      laborCost: 0.65,
      equipmentCost: 0.05,
      totalCost: 1.55,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Concrete',
      description: 'Rebar #5',
      unit: 'LB',
      materialCost: 0.85,
      laborCost: 0.60,
      equipmentCost: 0.05,
      totalCost: 1.50,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    }
  ],
  
  'Steel': [
    {
      category: 'Steel',
      description: 'Structural Steel',
      unit: 'TON',
      materialCost: 1800,
      laborCost: 900,
      equipmentCost: 300,
      totalCost: 3000,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Steel',
      description: 'Structural Steel Beam W8x24',
      unit: 'LF',
      materialCost: 75,
      laborCost: 45,
      equipmentCost: 15,
      totalCost: 135,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Steel',
      description: 'Structural Steel Beam W12x26',
      unit: 'LF',
      materialCost: 85,
      laborCost: 50,
      equipmentCost: 15,
      totalCost: 150,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Steel',
      description: 'Metal Deck 1.5" Type B',
      unit: 'SF',
      materialCost: 3.25,
      laborCost: 1.85,
      equipmentCost: 0.40,
      totalCost: 5.50,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    }
  ],
  
  'Drywall': [
    {
      category: 'Drywall',
      description: '5/8" Type X Gypsum Board',
      unit: 'SF',
      materialCost: 0.48,
      laborCost: 0.95,
      equipmentCost: 0.12,
      totalCost: 1.55,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Drywall',
      description: '1/2" Regular Gypsum Board',
      unit: 'SF',
      materialCost: 0.42,
      laborCost: 0.95,
      equipmentCost: 0.12,
      totalCost: 1.49,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Drywall',
      description: 'Metal Studs 3-5/8" 20 GA',
      unit: 'LF',
      materialCost: 1.85,
      laborCost: 2.10,
      equipmentCost: 0.15,
      totalCost: 4.10,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    }
  ],
  
  // Electrical Contractor Materials
  'Electrical': [
    {
      category: 'Electrical',
      description: 'EMT Conduit 1"',
      unit: 'LF',
      materialCost: 4.25,
      laborCost: 6.50,
      equipmentCost: 0.25,
      totalCost: 11.00,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'THHN Wire #12 AWG',
      unit: 'LF',
      materialCost: 0.28,
      laborCost: 0.12,
      equipmentCost: 0.02,
      totalCost: 0.42,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'THHN Wire #10 AWG',
      unit: 'LF',
      materialCost: 0.42,
      laborCost: 0.14,
      equipmentCost: 0.02,
      totalCost: 0.58,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: '2x4 LED Troffer 4000K',
      unit: 'EA',
      materialCost: 125,
      laborCost: 65,
      equipmentCost: 10,
      totalCost: 200,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Duplex Receptacle 20A Commercial',
      unit: 'EA',
      materialCost: 15,
      laborCost: 35,
      equipmentCost: 5,
      totalCost: 55,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Panel 400A 3-Phase',
      unit: 'EA',
      materialCost: 2800,
      laborCost: 1200,
      equipmentCost: 200,
      totalCost: 4200,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    // Lighting Controls
    {
      category: 'Electrical',
      description: 'Occupancy Sensor Ceiling Mount',
      unit: 'EA',
      materialCost: 85,
      laborCost: 55,
      equipmentCost: 10,
      totalCost: 150,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Dimmer Switch 1000W',
      unit: 'EA',
      materialCost: 125,
      laborCost: 45,
      equipmentCost: 5,
      totalCost: 175,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Lighting Control Panel 8 Zone',
      unit: 'EA',
      materialCost: 2400,
      laborCost: 850,
      equipmentCost: 150,
      totalCost: 3400,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Daylight Sensor',
      unit: 'EA',
      materialCost: 145,
      laborCost: 65,
      equipmentCost: 10,
      totalCost: 220,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Time Clock Control',
      unit: 'EA',
      materialCost: 275,
      laborCost: 125,
      equipmentCost: 25,
      totalCost: 425,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Emergency Lighting Unit LED',
      unit: 'EA',
      materialCost: 165,
      laborCost: 85,
      equipmentCost: 15,
      totalCost: 265,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Exit Sign LED Edge Lit',
      unit: 'EA',
      materialCost: 125,
      laborCost: 65,
      equipmentCost: 10,
      totalCost: 200,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Addressable Relay Module',
      unit: 'EA',
      materialCost: 95,
      laborCost: 45,
      equipmentCost: 10,
      totalCost: 150,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Low Voltage Control Wire 18/2',
      unit: 'LF',
      materialCost: 0.45,
      laborCost: 0.25,
      equipmentCost: 0.05,
      totalCost: 0.75,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Electrical',
      description: 'Junction Box 4x4',
      unit: 'EA',
      materialCost: 12,
      laborCost: 28,
      equipmentCost: 5,
      totalCost: 45,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    }
  ],
  
  // Plumbing Contractor Materials
  'Plumbing': [
    {
      category: 'Plumbing',
      description: 'Copper Pipe Type L 1"',
      unit: 'LF',
      materialCost: 12.50,
      laborCost: 8.75,
      equipmentCost: 1.25,
      totalCost: 22.50,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Plumbing',
      description: 'PVC Pipe Schedule 40 4"',
      unit: 'LF',
      materialCost: 8.25,
      laborCost: 7.50,
      equipmentCost: 0.75,
      totalCost: 16.50,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Plumbing',
      description: 'Water Closet Commercial Grade',
      unit: 'EA',
      materialCost: 385,
      laborCost: 215,
      equipmentCost: 25,
      totalCost: 625,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Plumbing',
      description: 'Lavatory Wall Mount ADA',
      unit: 'EA',
      materialCost: 425,
      laborCost: 185,
      equipmentCost: 15,
      totalCost: 625,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'Plumbing',
      description: 'Water Heater 80 Gal Commercial',
      unit: 'EA',
      materialCost: 2850,
      laborCost: 650,
      equipmentCost: 100,
      totalCost: 3600,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    }
  ],
  
  // HVAC Contractor Materials
  'HVAC': [
    {
      category: 'HVAC',
      description: 'Rooftop Unit 10 Ton',
      unit: 'EA',
      materialCost: 12500,
      laborCost: 3500,
      equipmentCost: 1000,
      totalCost: 17000,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'HVAC',
      description: 'Sheet Metal Ductwork',
      unit: 'LB',
      materialCost: 3.85,
      laborCost: 4.25,
      equipmentCost: 0.40,
      totalCost: 8.50,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'HVAC',
      description: 'VAV Box with Reheat',
      unit: 'EA',
      materialCost: 875,
      laborCost: 425,
      equipmentCost: 50,
      totalCost: 1350,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    },
    {
      category: 'HVAC',
      description: 'Diffuser 24x24 Lay-in',
      unit: 'EA',
      materialCost: 125,
      laborCost: 65,
      equipmentCost: 10,
      totalCost: 200,
      source: 'rsmeans',
      lastUpdated: new Date(),
      location: 'National Average'
    }
  ]
}

// Labor rates by trade (per hour, including benefits)
const LABOR_RATES: Record<string, LaborRate> = {
  'Electrician Journeyman': {
    trade: 'Electrical',
    classification: 'Journeyman',
    baseRate: 45,
    benefits: 25,
    totalRate: 70,
    location: 'National Average'
  },
  'Plumber Journeyman': {
    trade: 'Plumbing',
    classification: 'Journeyman',
    baseRate: 48,
    benefits: 27,
    totalRate: 75,
    location: 'National Average'
  },
  'HVAC Mechanic': {
    trade: 'HVAC',
    classification: 'Journeyman',
    baseRate: 46,
    benefits: 26,
    totalRate: 72,
    location: 'National Average'
  },
  'Carpenter': {
    trade: 'Carpentry',
    classification: 'Journeyman',
    baseRate: 38,
    benefits: 22,
    totalRate: 60,
    location: 'National Average'
  },
  'Ironworker': {
    trade: 'Steel',
    classification: 'Journeyman',
    baseRate: 52,
    benefits: 28,
    totalRate: 80,
    location: 'National Average'
  },
  'Concrete Finisher': {
    trade: 'Concrete',
    classification: 'Journeyman',
    baseRate: 40,
    benefits: 23,
    totalRate: 63,
    location: 'National Average'
  }
}

// Location adjustment factors (relative to national average)
const LOCATION_FACTORS: Record<string, number> = {
  'New York, NY': 1.35,
  'San Francisco, CA': 1.42,
  'Los Angeles, CA': 1.18,
  'Chicago, IL': 1.15,
  'Houston, TX': 0.95,
  'Phoenix, AZ': 0.92,
  'Philadelphia, PA': 1.12,
  'San Antonio, TX': 0.88,
  'San Diego, CA': 1.15,
  'Dallas, TX': 0.94,
  'National Average': 1.00
}

// Cache configuration
const PRICE_CACHE_MAX_SIZE = 5000 // Maximum cached prices
const PRICE_CACHE_TTL = 12 * 60 * 60 * 1000 // 12 hours

export class PricingDatabase {
  private priceCache: Map<string, { price: MaterialPrice; timestamp: number }> = new Map()
  private cleanupInterval?: NodeJS.Timeout
  
  constructor() {
    // Set up periodic cache cleanup (every 2 hours)
    if (typeof window === 'undefined') {
      this.cleanupInterval = setInterval(() => {
        this.cleanupExpiredEntries()
      }, 2 * 60 * 60 * 1000) // 2 hours
    }
  }
  
  /**
   * Clean up resources
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.priceCache.clear()
  }
  
  /**
   * Get price for a material
   */
  getMaterialPrice(
    description: string,
    category: string,
    location: string = 'National Average',
    companyType?: CompanyType
  ): MaterialPrice | undefined {
    // Check cache first
    const cacheKey = `${category}-${description}-${location}`
    const cached = this.priceCache.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < PRICE_CACHE_TTL) {
      return cached.price
    }
    
    // Search in database
    const categoryPrices = BASE_MATERIAL_PRICES[category] || []
    let price = categoryPrices.find(p => 
      p.description.toLowerCase() === description.toLowerCase() ||
      description.toLowerCase().includes(p.description.toLowerCase())
    )
    
    if (!price) {
      // Try fuzzy matching
      price = this.fuzzyMatch(description, category)
    }
    
    if (price) {
      // Apply location factor
      const locationFactor = this.getLocationFactor(location)
      const adjustedPrice = {
        ...price,
        materialCost: price.materialCost * locationFactor,
        laborCost: price.laborCost * locationFactor,
        equipmentCost: price.equipmentCost * locationFactor,
        totalCost: price.totalCost * locationFactor,
        location
      }
      
      // Cache the result with LRU eviction
      this.addToCache(cacheKey, adjustedPrice)
      return adjustedPrice
    }
    
    return undefined
  }
  
  /**
   * Add price to cache with LRU eviction
   */
  private addToCache(key: string, price: MaterialPrice): void {
    // Clean up expired entries first
    this.cleanupExpiredEntries()
    
    // Check if we need to evict entries
    if (this.priceCache.size >= PRICE_CACHE_MAX_SIZE) {
      // Find and remove oldest entry
      let oldestKey: string | null = null
      let oldestTimestamp = Date.now()
      
      this.priceCache.forEach((value, k) => {
        if (value.timestamp < oldestTimestamp) {
          oldestTimestamp = value.timestamp
          oldestKey = k
        }
      })
      
      if (oldestKey) {
        this.priceCache.delete(oldestKey)
      }
    }
    
    this.priceCache.set(key, {
      price,
      timestamp: Date.now()
    })
  }
  
  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now()
    const expiredKeys: string[] = []
    
    this.priceCache.forEach((value, key) => {
      if (now - value.timestamp > PRICE_CACHE_TTL) {
        expiredKeys.push(key)
      }
    })
    
    expiredKeys.forEach(key => this.priceCache.delete(key))
  }
  
  /**
   * Get labor rate for a trade
   */
  getLaborRate(
    trade: string,
    location: string = 'National Average'
  ): LaborRate | undefined {
    // Find matching labor rate
    const rate = Object.values(LABOR_RATES).find(r => 
      r.trade.toLowerCase() === trade.toLowerCase()
    )
    
    if (rate) {
      const locationFactor = this.getLocationFactor(location)
      return {
        ...rate,
        baseRate: rate.baseRate * locationFactor,
        benefits: rate.benefits * locationFactor,
        totalRate: rate.totalRate * locationFactor,
        location
      }
    }
    
    return undefined
  }
  
  /**
   * Estimate price based on similar materials
   */
  estimatePrice(
    description: string,
    category: string,
    unit: string,
    location: string = 'National Average'
  ): MaterialPrice {
    // Find similar items in the category
    const categoryPrices = BASE_MATERIAL_PRICES[category] || []
    const similarItems = categoryPrices.filter(p => p.unit === unit)
    
    if (similarItems.length > 0) {
      // Average of similar items
      const avgMaterial = similarItems.reduce((sum, p) => sum + p.materialCost, 0) / similarItems.length
      const avgLabor = similarItems.reduce((sum, p) => sum + p.laborCost, 0) / similarItems.length
      const avgEquipment = similarItems.reduce((sum, p) => sum + p.equipmentCost, 0) / similarItems.length
      
      const locationFactor = this.getLocationFactor(location)
      
      return {
        category,
        description,
        unit,
        materialCost: avgMaterial * locationFactor,
        laborCost: avgLabor * locationFactor,
        equipmentCost: avgEquipment * locationFactor,
        totalCost: (avgMaterial + avgLabor + avgEquipment) * locationFactor,
        source: 'estimated',
        lastUpdated: new Date(),
        location,
        notes: 'Estimated based on similar materials'
      }
    }
    
    // Fallback pricing by category
    const fallbackPrices: Record<string, number> = {
      'Concrete': 200,
      'Steel': 150,
      'Electrical': 50,
      'Plumbing': 75,
      'HVAC': 100,
      'Drywall': 15,
      'Masonry': 85,
      'Roofing': 65
    }
    
    const basePrice = fallbackPrices[category] || 100
    const locationFactor = this.getLocationFactor(location)
    
    return {
      category,
      description,
      unit,
      materialCost: basePrice * 0.6 * locationFactor,
      laborCost: basePrice * 0.3 * locationFactor,
      equipmentCost: basePrice * 0.1 * locationFactor,
      totalCost: basePrice * locationFactor,
      source: 'estimated',
      lastUpdated: new Date(),
      location,
      notes: 'Estimated using category average'
    }
  }
  
  /**
   * Fuzzy match for material descriptions
   */
  private fuzzyMatch(description: string, category: string): MaterialPrice | undefined {
    const categoryPrices = BASE_MATERIAL_PRICES[category] || []
    const descLower = description.toLowerCase()
    
    // Score each item based on keyword matches
    let bestMatch: MaterialPrice | undefined = undefined
    let bestScore = 0
    
    categoryPrices.forEach(price => {
      const priceLower = price.description.toLowerCase()
      const keywords = priceLower.split(/\s+/)
      let score = 0
      
      keywords.forEach(keyword => {
        if (descLower.includes(keyword)) {
          score += keyword.length // Longer matches score higher
        }
      })
      
      if (score > bestScore) {
        bestScore = score
        bestMatch = price
      }
    })
    
    return bestScore > 0 ? bestMatch : undefined
  }
  
  /**
   * Get location adjustment factor
   */
  private getLocationFactor(location: string): number {
    // Extract city from location if it's a zip code
    if (/^\d{5}/.test(location)) {
      // Map zip to city (simplified - real implementation would use zip database)
      if (location.startsWith('100')) return LOCATION_FACTORS['New York, NY']
      if (location.startsWith('941')) return LOCATION_FACTORS['San Francisco, CA']
      if (location.startsWith('900')) return LOCATION_FACTORS['Los Angeles, CA']
      if (location.startsWith('606')) return LOCATION_FACTORS['Chicago, IL']
      if (location.startsWith('770')) return LOCATION_FACTORS['Houston, TX']
    }
    
    // Check if location matches any known city
    for (const [city, factor] of Object.entries(LOCATION_FACTORS)) {
      if (location.includes(city.split(',')[0])) {
        return factor
      }
    }
    
    return 1.0 // Default to national average
  }
  
  /**
   * Get all available materials for a category
   */
  getCategoryMaterials(category: string): MaterialPrice[] {
    return BASE_MATERIAL_PRICES[category] || []
  }
  
  /**
   * Validate if a price seems reasonable
   */
  validatePrice(
    price: number,
    category: string,
    unit: string
  ): { isValid: boolean; reason?: string } {
    // Define reasonable price ranges by category and unit
    const priceRanges: Record<string, Record<string, { min: number; max: number }>> = {
      'Concrete': {
        'CY': { min: 50, max: 500 },
        'SF': { min: 1, max: 20 }
      },
      'Steel': {
        'TON': { min: 1000, max: 5000 },
        'LB': { min: 0.5, max: 5 },
        'LF': { min: 10, max: 500 }
      },
      'Electrical': {
        'EA': { min: 5, max: 10000 },
        'LF': { min: 0.1, max: 100 }
      },
      'Plumbing': {
        'EA': { min: 10, max: 5000 },
        'LF': { min: 1, max: 100 }
      }
    }
    
    const range = priceRanges[category]?.[unit]
    if (range) {
      if (price < range.min) {
        return { isValid: false, reason: `Price below typical minimum of $${range.min}/${unit}` }
      }
      if (price > range.max) {
        return { isValid: false, reason: `Price above typical maximum of $${range.max}/${unit}` }
      }
    }
    
    return { isValid: true }
  }
}

// Export singleton instance
export const pricingDatabase = new PricingDatabase()