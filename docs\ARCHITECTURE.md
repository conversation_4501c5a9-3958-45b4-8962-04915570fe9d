# Architecture Documentation

## System Overview

The AI Construction Management platform is built on a modern, scalable architecture that combines Next.js for full-stack capabilities, AI integration with Google Gemini, real-time collaboration features, and comprehensive construction management tools.

```
┌─────────────────────────────────────────────────────────────────────┐
│                           Client Layer                               │
├─────────────────┬─────────────────┬─────────────────┬──────────────┤
│   Web App       │   Mobile Web    │   Progressive   │   Third      │
│   (Next.js)     │   (Responsive)  │   Web App       │   Party      │
│                 │                 │                 │   Integrations│
└────────┬────────┴────────┬────────┴────────┬────────┴──────┬───────┘
         │                 │                 │                │
         └─────────────────┴─────────────────┴────────────────┘
                                    │
                           ┌────────▼────────┐
                           │   API Gateway   │
                           │  (Next.js API)  │
                           └────────┬────────┘
                                    │
        ┌───────────────────────────┼───────────────────────────┐
        │                           │                           │
┌───────▼────────┐        ┌────────▼────────┐        ┌─────────▼────────┐
│  Auth Service  │        │  Business Logic │        │   AI Service     │
│     (JWT)      │        │    Services     │        │ (Google Gemini)  │
└────────────────┘        └─────────────────┘        └──────────────────┘
        │                           │                           │
        └───────────────────────────┼───────────────────────────┘
                                    │
                           ┌────────▼────────┐
                           │   Data Layer    │
                           ├─────────────────┤
                           │   PostgreSQL    │
                           │   Prisma ORM    │
                           └─────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
           ┌────────▼────────┐ ┌───▼────┐ ┌───────▼────────┐
           │  File Storage   │ │ Cache  │ │  Message Queue │
           │  (Local/S3)     │ │ (Redis)│ │  (In-Memory)   │
           └─────────────────┘ └────────┘ └────────────────┘
```

## Core Components

### 1. Frontend Architecture

#### Technology Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **UI Components**: React 18 + shadcn/ui (Radix UI based)
- **Styling**: Tailwind CSS
- **State Management**: 
  - Client State: Zustand
  - Server State: React Query (TanStack Query)
- **Forms**: React Hook Form + Zod validation
- **Real-time**: Socket.io client
- **3D Visualization**: Three.js + React Three Fiber

#### Component Structure

```
src/
├── app/                      # Next.js App Router
│   ├── (auth)/              # Authentication pages
│   ├── api/                 # API routes
│   └── dashboard/           # Main application
│       ├── layout.tsx       # Dashboard layout
│       ├── page.tsx         # Dashboard home
│       ├── projects/        # Project management
│       ├── scheduling/      # AI scheduling
│       ├── progress/        # Progress tracking
│       ├── safety/          # Safety monitoring
│       ├── estimating/      # Takeoff & estimating
│       ├── field/           # Field operations
│       ├── analytics/       # Analytics
│       └── ai-assistant/    # AI chat interface
├── components/              # Shared components
│   ├── ui/                 # Basic UI components
│   ├── layout/             # Layout components
│   └── common/             # Common components
├── features/               # Feature-specific components
│   ├── projects/          # Project components
│   ├── safety/            # Safety components
│   ├── scheduling/        # Scheduling components
│   └── analytics/         # Analytics components
├── hooks/                 # Custom React hooks
├── lib/                   # Core libraries
│   ├── api/              # API client functions
│   ├── services/         # Business logic
│   ├── utils/            # Utility functions
│   └── gemini.ts         # AI service
└── types/                # TypeScript types
```

#### Design Patterns

1. **Server Components by Default**
   ```typescript
   // Server Component (default)
   export default async function ProjectList() {
     const projects = await getProjects();
     return <ProjectGrid projects={projects} />;
   }
   ```

2. **Client Components for Interactivity**
   ```typescript
   'use client';
   
   export function InteractiveChart({ data }) {
     // Client-side interactivity
     return <ResponsiveChart data={data} />;
   }
   ```

3. **Compound Components**
   ```typescript
   <Card>
     <Card.Header>
       <Card.Title>Project Status</Card.Title>
     </Card.Header>
     <Card.Content>
       <ProjectMetrics />
     </Card.Content>
   </Card>
   ```

### 2. Backend Architecture

#### API Design

The backend follows RESTful principles with Next.js API routes:

```
/api/
├── auth/
│   ├── signin/
│   ├── signup/
│   └── verify/
├── projects/
│   ├── [id]/
│   │   ├── schedule/
│   │   ├── analytics/
│   │   └── team/
│   └── route.ts
├── documents/
│   ├── upload/
│   └── [id]/
│       └── analyze/
├── takeoff/
│   ├── route.ts
│   └── jobs/
│       └── [jobId]/
├── safety/
│   ├── incidents/
│   └── metrics/
├── ai/
│   ├── chat/
│   └── reports/
└── ws/                    # WebSocket endpoints
```

#### Service Layer Architecture

```typescript
// Service Pattern Example
export class ProjectService {
  private prisma: PrismaClient;
  private aiService: GeminiService;
  
  async createProject(data: CreateProjectDTO): Promise<Project> {
    // Business logic
    const project = await this.prisma.project.create({ data });
    
    // AI enhancement
    await this.aiService.analyzeProject(project);
    
    return project;
  }
  
  async optimizeSchedule(projectId: string): Promise<ScheduleOptimization> {
    const project = await this.prisma.project.findUnique({
      where: { id: projectId },
      include: { tasks: true }
    });
    
    // AI-powered optimization
    return this.aiService.optimizeSchedule(project);
  }
}
```

### 3. AI Integration Architecture

#### Gemini Service Design

```typescript
// Singleton pattern for AI service
class GeminiService {
  private static instance: GeminiService;
  private model: GenerativeModel;
  private companyContexts: Map<CompanyType, string>;
  
  private constructor() {
    this.model = genAI.getGenerativeModel({ 
      model: process.env.GEMINI_MODEL || "gemini-2.0-flash" 
    });
    this.initializeCompanyContexts();
  }
  
  static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService();
    }
    return GeminiService.instance;
  }
  
  async analyzeWithContext(
    prompt: string, 
    companyType: CompanyType,
    projectContext?: ProjectContext
  ): Promise<AIResponse> {
    const enhancedPrompt = this.buildEnhancedPrompt(
      prompt, 
      companyType, 
      projectContext
    );
    
    const result = await this.model.generateContent(enhancedPrompt);
    return this.parseResponse(result);
  }
}
```

#### AI Context Enhancement

```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│   User Query    │────▶│ Context Builder  │────▶│ Enhanced Prompt │
└─────────────────┘     └──────────────────┘     └─────────────────┘
                               │                           │
                               ▼                           ▼
                        ┌──────────────┐           ┌─────────────┐
                        │Company Type  │           │   Gemini    │
                        │   Context    │           │     API     │
                        └──────────────┘           └─────────────┘
                               │                           │
                               ▼                           ▼
                        ┌──────────────┐           ┌─────────────┐
                        │Project Data  │           │ AI Response │
                        │   Context    │           └─────────────┘
                        └──────────────┘
```

### 4. Database Architecture

#### Schema Design

```prisma
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String
  role          UserRole
  company       Company?  @relation(fields: [companyId], references: [id])
  companyId     String?
  projects      ProjectUser[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model Project {
  id            String    @id @default(cuid())
  name          String
  description   String?
  status        ProjectStatus
  startDate     DateTime
  endDate       DateTime
  budget        Float
  spentBudget   Float     @default(0)
  progress      Float     @default(0)
  
  // Relations
  company       Company   @relation(fields: [companyId], references: [id])
  companyId     String
  team          ProjectUser[]
  documents     Document[]
  tasks         Task[]
  safetyRecords SafetyRecord[]
  
  // Metadata
  location      Json?
  metrics       Json?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  @@index([companyId, status])
}

model Task {
  id            String    @id @default(cuid())
  name          String
  description   String?
  startDate     DateTime
  endDate       DateTime
  duration      Int
  progress      Float     @default(0)
  
  // Relations
  project       Project   @relation(fields: [projectId], references: [id])
  projectId     String
  dependencies  Task[]    @relation("TaskDependencies")
  dependents    Task[]    @relation("TaskDependencies")
  resources     Resource[]
  
  // Scheduling
  isCriticalPath Boolean  @default(false)
  earlyStart    DateTime?
  earlyFinish   DateTime?
  lateStart     DateTime?
  lateFinish    DateTime?
  slack         Int?
  
  @@index([projectId, startDate])
}
```

#### Database Optimization Strategies

1. **Indexing Strategy**
   - Composite indexes for common queries
   - Partial indexes for filtered queries
   - GIN indexes for JSON fields

2. **Query Optimization**
   ```typescript
   // Efficient query with includes
   const project = await prisma.project.findUnique({
     where: { id },
     include: {
       team: {
         include: { user: true }
       },
       tasks: {
         where: { isCriticalPath: true },
         orderBy: { startDate: 'asc' }
       },
       _count: {
         select: { 
           documents: true,
           safetyRecords: true 
         }
       }
     }
   });
   ```

### 5. Real-time Architecture

#### Socket.io Integration

```typescript
// Server setup
export class SocketService {
  private io: Server;
  
  constructor(httpServer: HttpServer) {
    this.io = new Server(httpServer, {
      cors: { origin: process.env.CLIENT_URL }
    });
    
    this.setupMiddleware();
    this.setupEventHandlers();
  }
  
  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      // Join project room
      socket.on('join:project', async (projectId) => {
        const hasAccess = await this.verifyProjectAccess(
          socket.userId, 
          projectId
        );
        if (hasAccess) {
          socket.join(`project:${projectId}`);
        }
      });
      
      // Real-time updates
      socket.on('task:update', async (data) => {
        await this.handleTaskUpdate(socket, data);
        socket.to(`project:${data.projectId}`).emit('task:updated', data);
      });
    });
  }
}
```

#### Event Flow

```
┌────────────┐     ┌──────────────┐     ┌────────────┐
│   Client   │────▶│ Socket.io    │────▶│   Server   │
│   (React)  │     │   Client     │     │  Handler   │
└────────────┘     └──────────────┘     └────────────┘
      ▲                                         │
      │                                         ▼
      │                                  ┌────────────┐
      │                                  │  Database  │
      │                                  └────────────┘
      │                                         │
      │            ┌──────────────┐            │
      └────────────│  Broadcast   │◀────────────┘
                   │  to Clients  │
                   └──────────────┘
```

### 6. Security Architecture

#### Authentication Flow

```
┌──────────┐     ┌──────────┐     ┌───────────┐     ┌──────────┐
│  Client  │────▶│   API    │────▶│   Auth    │────▶│   JWT    │
│          │     │ Gateway  │     │  Service  │     │  Verify  │
└──────────┘     └──────────┘     └───────────┘     └──────────┘
     ▲                                    │                 │
     │                                    ▼                 ▼
     │                             ┌───────────┐     ┌──────────┐
     │                             │   User    │     │  Token   │
     └─────────────────────────────│   Store   │     │  Store   │
            Authenticated          └───────────┘     └──────────┘
```

#### Security Layers

1. **API Security**
   - JWT authentication
   - Rate limiting
   - CORS configuration
   - Input validation with Zod

2. **Data Security**
   - Encrypted connections (TLS)
   - Encrypted storage for sensitive data
   - Row-level security in database
   - Audit logging

3. **Application Security**
   - CSRF protection
   - XSS prevention
   - SQL injection prevention (Prisma)
   - Content Security Policy

### 7. Performance Architecture

#### Optimization Strategies

1. **Frontend Performance**
   ```typescript
   // Dynamic imports for code splitting
   const BIMViewer = dynamic(() => import('@/components/BIMViewer'), {
     loading: () => <BIMViewerSkeleton />,
     ssr: false
   });
   
   // Image optimization
   <Image
     src="/project-photo.jpg"
     alt="Project"
     width={800}
     height={600}
     priority
     placeholder="blur"
   />
   ```

2. **API Performance**
   - Response caching
   - Database connection pooling
   - Pagination for large datasets
   - Selective field queries

3. **Caching Strategy**
   ```
   ┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
   │  Client  │────▶│   CDN    │────▶│   API    │────▶│   DB     │
   │  Cache   │     │  Cache   │     │  Cache   │     │          │
   └──────────┘     └──────────┘     └──────────┘     └──────────┘
   ```

### 8. Deployment Architecture

#### Container Architecture

```dockerfile
# Multi-stage build
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
CMD ["node", "server.js"]
```

#### Deployment Options

```
┌─────────────────────────────────────────────────────────────┐
│                    Load Balancer (nginx)                     │
└──────────────────────┬─────────────────────────────────────┘
                       │
        ┌──────────────┴──────────────┐
        │                             │
┌───────▼────────┐            ┌──────▼────────┐
│   App Server   │            │  App Server   │
│   Instance 1   │            │  Instance 2   │
│   (Next.js)    │            │  (Next.js)    │
└───────┬────────┘            └──────┬────────┘
        │                             │
        └──────────┬──────────────────┘
                   │
          ┌────────▼────────┐
          │   PostgreSQL    │
          │    Primary      │
          └────────┬────────┘
                   │
          ┌────────▼────────┐
          │   PostgreSQL    │
          │    Replica      │
          └─────────────────┘
```

### 9. Monitoring & Observability

#### Logging Architecture

```typescript
// Structured logging
export const logger = {
  info: (message: string, meta?: any) => {
    console.log(JSON.stringify({
      level: 'info',
      timestamp: new Date().toISOString(),
      message,
      ...meta
    }));
  },
  
  error: (message: string, error?: Error, meta?: any) => {
    console.error(JSON.stringify({
      level: 'error',
      timestamp: new Date().toISOString(),
      message,
      error: error?.stack,
      ...meta
    }));
  }
};
```

#### Metrics Collection

```
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│     App      │────▶│   Metrics    │────▶│  Monitoring  │
│   Metrics    │     │  Collector   │     │  Dashboard   │
└──────────────┘     └──────────────┘     └──────────────┘
       │                                           │
       ▼                                           ▼
┌──────────────┐                          ┌──────────────┐
│   Custom     │                          │    Alerts    │
│   Metrics    │                          │   System     │
└──────────────┘                          └──────────────┘
```

## Scalability Considerations

### Horizontal Scaling

1. **Stateless Application Servers**
   - All state stored in database or cache
   - Session data in Redis
   - File uploads to S3

2. **Database Scaling**
   - Read replicas for analytics
   - Connection pooling
   - Query optimization
   - Partitioning for large tables

3. **Caching Strategy**
   - CDN for static assets
   - Redis for session data
   - API response caching
   - Database query caching

### Vertical Scaling

1. **Resource Optimization**
   - Efficient algorithms for AI processing
   - Lazy loading of heavy components
   - Image optimization
   - Code splitting

2. **Background Processing**
   - Queue system for heavy tasks
   - Async processing for AI analysis
   - Batch operations for bulk updates

## Future Architecture Enhancements

### Microservices Migration Path

```
Current Monolithic Architecture
            │
            ▼
    Extract AI Service
            │
            ▼
   Extract Analytics Service
            │
            ▼
   Extract Document Service
            │
            ▼
Full Microservices Architecture
```

### Technology Roadmap

1. **Short Term (3-6 months)**
   - GraphQL API layer
   - Redis caching implementation
   - Enhanced monitoring with OpenTelemetry

2. **Medium Term (6-12 months)**
   - Kubernetes deployment
   - Event-driven architecture with Kafka
   - Multi-region deployment

3. **Long Term (12+ months)**
   - Full microservices architecture
   - Machine learning pipeline
   - Edge computing for IoT devices