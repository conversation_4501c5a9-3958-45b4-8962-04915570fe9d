/**
 * Utility functions to map between abbreviated company type values (used in forms)
 * and full CompanyType values (used throughout the system).
 * 
 * This ensures consistency between frontend form values and the CompanyType
 * used in the database and AI context generation.
 */

import { CompanyType } from './company-types'

/**
 * Abbreviated company type values used in forms and database
 */
export type AbbreviatedCompanyType = 
  | 'GENERAL'
  | 'ELECTRICAL'
  | 'PLUMBING'
  | 'HVAC'
  | 'ROOFING'
  | 'CONCRETE'
  | 'STEEL_METAL'
  | 'MASONRY'
  | 'PAINTING'
  | 'FLOORING'
  | 'LANDSCAPING'
  | 'DEMOLITION'
  | 'EXCAVATION'
  | 'GLASS_GLAZING'
  | 'INSULATION'
  | 'DRYWALL'
  | 'FIRE_PROTECTION'
  | 'ELEVATOR'
  | 'SOLAR_RENEWABLE'
  | 'MARINE_UNDERWATER'

/**
 * Mapping from abbreviated company type values to full CompanyType names
 */
export const ABBREVIATED_TO_FULL_MAP: Record<AbbreviatedCompanyType, CompanyType> = {
  'GENERAL': 'General Contractor',
  'ELECTRICAL': 'Electrical Contractor',
  'PLUMBING': 'Plumbing Contractor',
  'HVAC': 'HVAC Contractor',
  'ROOFING': 'Roofing Contractor',
  'CONCRETE': 'Concrete Contractor',
  'STEEL_METAL': 'Steel/Metal Contractor',
  'MASONRY': 'Masonry Contractor',
  'PAINTING': 'Painting Contractor',
  'FLOORING': 'Flooring Contractor',
  'LANDSCAPING': 'Landscaping Contractor',
  'DEMOLITION': 'Demolition Contractor',
  'EXCAVATION': 'Excavation/Earthwork Contractor',
  'GLASS_GLAZING': 'Glass & Glazing Contractor',
  'INSULATION': 'Insulation Contractor',
  'DRYWALL': 'Drywall Contractor',
  'FIRE_PROTECTION': 'Fire Protection Contractor',
  'ELEVATOR': 'Elevator Contractor',
  'SOLAR_RENEWABLE': 'Solar/Renewable Energy Contractor',
  'MARINE_UNDERWATER': 'Marine/Underwater Contractor',
}

/**
 * Mapping from full CompanyType names to abbreviated values
 */
export const FULL_TO_ABBREVIATED_MAP: Record<CompanyType, AbbreviatedCompanyType> = {
  'General Contractor': 'GENERAL',
  'Electrical Contractor': 'ELECTRICAL',
  'Plumbing Contractor': 'PLUMBING',
  'HVAC Contractor': 'HVAC',
  'Roofing Contractor': 'ROOFING',
  'Concrete Contractor': 'CONCRETE',
  'Steel/Metal Contractor': 'STEEL_METAL',
  'Masonry Contractor': 'MASONRY',
  'Painting Contractor': 'PAINTING',
  'Flooring Contractor': 'FLOORING',
  'Landscaping Contractor': 'LANDSCAPING',
  'Demolition Contractor': 'DEMOLITION',
  'Excavation/Earthwork Contractor': 'EXCAVATION',
  'Glass & Glazing Contractor': 'GLASS_GLAZING',
  'Insulation Contractor': 'INSULATION',
  'Drywall Contractor': 'DRYWALL',
  'Fire Protection Contractor': 'FIRE_PROTECTION',
  'Elevator Contractor': 'ELEVATOR',
  'Solar/Renewable Energy Contractor': 'SOLAR_RENEWABLE',
  'Marine/Underwater Contractor': 'MARINE_UNDERWATER',
}

/**
 * Convert an abbreviated company type value to its full CompanyType name.
 * Returns null if the abbreviated value is not recognized.
 * 
 * @param abbreviated - The abbreviated company type value (e.g., 'GENERAL', 'ELECTRICAL')
 * @returns The full CompanyType name or null if not found
 * 
 * @example
 * ```typescript
 * const fullType = abbreviatedToFull('GENERAL'); // 'General Contractor'
 * const invalid = abbreviatedToFull('INVALID'); // null
 * ```
 */
export function abbreviatedToFull(abbreviated: string): CompanyType | null {
  return ABBREVIATED_TO_FULL_MAP[abbreviated as AbbreviatedCompanyType] || null
}

/**
 * Convert a full CompanyType name to its abbreviated value.
 * Returns null if the full name is not recognized.
 * 
 * @param fullName - The full CompanyType name (e.g., 'General Contractor')
 * @returns The abbreviated value or null if not found
 * 
 * @example
 * ```typescript
 * const abbrev = fullToAbbreviated('General Contractor'); // 'GENERAL'
 * const invalid = fullToAbbreviated('Invalid Type'); // null
 * ```
 */
export function fullToAbbreviated(fullName: string): AbbreviatedCompanyType | null {
  return FULL_TO_ABBREVIATED_MAP[fullName as CompanyType] || null
}

/**
 * Check if a string is a valid abbreviated company type
 * 
 * @param value - The value to check
 * @returns True if the value is a valid abbreviated company type
 */
export function isValidAbbreviatedType(value: string): value is AbbreviatedCompanyType {
  return value in ABBREVIATED_TO_FULL_MAP
}

/**
 * Check if a string is a valid full company type
 * 
 * @param value - The value to check
 * @returns True if the value is a valid full company type
 */
export function isValidFullType(value: string): value is CompanyType {
  return value in FULL_TO_ABBREVIATED_MAP
}

/**
 * Get all available abbreviated company types
 * 
 * @returns Array of all abbreviated company type values
 */
export function getAllAbbreviatedTypes(): AbbreviatedCompanyType[] {
  return Object.keys(ABBREVIATED_TO_FULL_MAP) as AbbreviatedCompanyType[]
}

/**
 * Get all available full company types
 * 
 * @returns Array of all full company type names
 */
export function getAllFullTypes(): CompanyType[] {
  return Object.keys(FULL_TO_ABBREVIATED_MAP) as CompanyType[]
}