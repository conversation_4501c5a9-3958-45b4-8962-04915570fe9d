import { ProjectContext } from '../company-types'

export interface JobResponse {
  jobId: string
  status: string
  totalFiles: number
  message: string
}

export interface JobStatus {
  id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  totalFiles: number
  processedFiles: number
  failedFiles: number
  totalItems: number
  totalCost: number
  files: Array<{
    id: string
    fileName: string
    status: string
    progress: number
    itemsDetected: number
    errorMessage?: string
  }>
  takeoffs?: Array<{
    id: string
    totalCost: number
    items: any[]
  }>
}

export interface JobProgress {
  jobId: string
  status: string
  totalFiles: number
  processedFiles: number
  failedFiles: number
  progress: number
  currentFile?: string
}

// Create a new processing job for multiple files
export async function createTakeoffJob(
  projectContext: ProjectContext,
  files: File[]
): Promise<JobResponse> {
  const formData = new FormData()
  formData.append('projectContext', JSON.stringify(projectContext))
  formData.append('fileCount', files.length.toString())

  // Add each file
  files.forEach((file, index) => {
    formData.append(`file_${index}`, file)
    formData.append(`fileName_${index}`, file.name)
  })

  const response = await fetch('/api/takeoff/jobs', {
    method: 'POST',
    body: formData,
    credentials: 'include'
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to create job')
  }

  const result = await response.json()
  return result.data
}

// Get job status
export async function getJobStatus(jobId: string): Promise<JobStatus> {
  const response = await fetch(`/api/takeoff/jobs?jobId=${jobId}`, {
    method: 'GET',
    credentials: 'include'
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to get job status')
  }

  const result = await response.json()
  return result.data
}

// Cancel a job
export async function cancelJob(jobId: string): Promise<boolean> {
  const response = await fetch(`/api/takeoff/jobs?jobId=${jobId}`, {
    method: 'DELETE',
    credentials: 'include'
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to cancel job')
  }

  return true
}

// Subscribe to job progress updates via Server-Sent Events
export function subscribeToJobProgress(
  jobId: string,
  onProgress: (progress: JobProgress) => void,
  onComplete?: () => void,
  onError?: (error: Error) => void
): () => void {
  const eventSource = new EventSource(`/api/takeoff/jobs/progress?jobId=${jobId}`)
  
  eventSource.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      
      if (data.type === 'progress') {
        onProgress(data)
        
        if (data.status === 'completed' || data.status === 'failed') {
          eventSource.close()
          if (onComplete) onComplete()
        }
      }
    } catch (error) {
      console.error('Failed to parse progress event:', error)
    }
  }

  eventSource.onerror = (error) => {
    console.error('SSE error:', error)
    eventSource.close()
    if (onError) onError(new Error('Connection lost'))
  }

  // Return cleanup function
  return () => {
    eventSource.close()
  }
}

// Helper to poll job status (fallback if SSE is not available)
export async function pollJobStatus(
  jobId: string,
  onUpdate: (status: JobStatus) => void,
  interval: number = 2000
): Promise<JobStatus> {
  return new Promise((resolve, reject) => {
    const pollInterval = setInterval(async () => {
      try {
        const status = await getJobStatus(jobId)
        onUpdate(status)

        if (status.status === 'completed' || status.status === 'failed') {
          clearInterval(pollInterval)
          resolve(status)
        }
      } catch (error) {
        clearInterval(pollInterval)
        reject(error)
      }
    }, interval)
  })
}