#!/usr/bin/env node

/**
 * Database setup script that properly loads environment variables
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

console.log('🚀 AI Construction Management - Database Setup');
console.log('==============================================\n');

// Check if DATABASE_URL is loaded
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL not found in environment variables!');
  console.log('\n📝 Creating default .env.local from .env.example...');
  
  // Copy .env.example to .env.local if it doesn't exist
  if (!fs.existsSync('.env.local') && fs.existsSync('.env.example')) {
    fs.copyFileSync('.env.example', '.env.local');
    console.log('✅ Created .env.local from .env.example');
    console.log('\n⚠️  Please update the DATABASE_URL in .env.local with your PostgreSQL credentials');
    console.log('   Default: postgresql://postgres:postgres@localhost:5432/ai_construction_db');
    process.exit(1);
  }
  
  console.error('\n❌ Please check your .env.local file and ensure DATABASE_URL is set correctly');
  process.exit(1);
}

console.log('✅ Environment variables loaded');
console.log(`📊 Database URL: ${process.env.DATABASE_URL.replace(/:[^:@]+@/, ':****@')}\n`);

// Function to run command with environment
function runCommand(command, description) {
  console.log(`\n🔧 ${description}...`);
  try {
    execSync(command, { 
      stdio: 'inherit',
      env: { ...process.env }
    });
    console.log(`✅ ${description} completed`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed`);
    return false;
  }
}

// Step 1: Generate Prisma Client
if (!runCommand('npx prisma generate', 'Generating Prisma Client')) {
  process.exit(1);
}

// Step 2: Push schema to database
if (!runCommand('npx prisma db push --skip-generate', 'Pushing database schema')) {
  console.log('\n❓ Database push failed. Possible reasons:');
  console.log('   1. PostgreSQL is not running');
  console.log('   2. Database credentials are incorrect');
  console.log('   3. Database does not exist');
  console.log('\n💡 Try running: npm run setup:sqlite for a quick SQLite setup');
  process.exit(1);
}

// Step 3: Seed database (optional)
console.log('\n🌱 Seeding database with demo data...');
runCommand('npx tsx prisma/seed.ts', 'Seeding database');

console.log('\n==============================================');
console.log('✨ Database setup completed successfully!');
console.log('==============================================\n');
console.log('📝 Demo credentials:');
console.log('   Admin: <EMAIL> / demo123456');
console.log('   Contractor: <EMAIL> / demo123456\n');
console.log('🚀 You can now run: npm run dev');
console.log('🔍 Or explore your database: npm run db:studio\n');