// React hooks for Socket.io BIM collaboration

import { useEffect, useState, useCallback, useRef } from 'react'
import { 
  BIMCollaborationClient, 
  getCollaborationClient,
  CollaborationCallbacks 
} from './client'
import {
  UserPresence,
  Cursor3D,
  ElementSelection,
  Annotation,
  ModelChange,
  CollaborationRoom,
  SocketError,
  AnnotationReply
} from './types'
import { BIMViewerState } from '@/types'
import { useToast } from '@/hooks/use-toast'

// Main collaboration hook
export function useBIMCollaboration(
  roomId: string,
  userId: string,
  userName: string,
  userRole: string
) {
  const [isConnected, setIsConnected] = useState(false)
  const [room, setRoom] = useState<CollaborationRoom | null>(null)
  const [users, setUsers] = useState<UserPresence[]>([])
  const [cursors, setCursors] = useState<Map<string, Cursor3D>>(new Map())
  const [selections, setSelections] = useState<Map<string, ElementSelection>>(new Map())
  const [annotations, setAnnotations] = useState<Annotation[]>([])
  const [modelChanges, setModelChanges] = useState<ModelChange[]>([])
  const [viewStates, setViewStates] = useState<Record<string, BIMViewerState>>({})
  const [error, setError] = useState<SocketError | null>(null)

  const clientRef = useRef<BIMCollaborationClient | null>(null)
  const { toast } = useToast()

  // Initialize client and connect
  useEffect(() => {
    const client = getCollaborationClient()
    clientRef.current = client

    // Setup callbacks
    const callbacks: CollaborationCallbacks = {
      onRoomJoined: (joinedRoom) => {
        setRoom(joinedRoom)
        setUsers(joinedRoom.users)
        setAnnotations(joinedRoom.annotations)
        toast({
          title: 'Connected',
          description: `Joined collaboration room: ${joinedRoom.roomId}`
        })
      },
      onRoomLeft: () => {
        setRoom(null)
        setUsers([])
        setCursors(new Map())
        setSelections(new Map())
        setAnnotations([])
      },
      onUserJoined: (user) => {
        setUsers(prev => [...prev, user])
        toast({
          title: 'User Joined',
          description: `${user.userName} joined the session`
        })
      },
      onUserLeft: (leftUserId) => {
        setUsers(prev => prev.filter(u => u.userId !== leftUserId))
        setCursors(prev => {
          const updated = new Map(prev)
          updated.delete(leftUserId)
          return updated
        })
        setSelections(prev => {
          const updated = new Map(prev)
          updated.delete(leftUserId)
          return updated
        })
      },
      onUsersUpdate: (updatedUsers) => {
        setUsers(updatedUsers)
      },
      onCursorUpdate: (cursorUpdates) => {
        setCursors(prev => {
          const updated = new Map(prev)
          cursorUpdates.forEach(cursor => {
            updated.set(cursor.userId, cursor)
          })
          return updated
        })
      },
      onSelectionUpdate: (selection) => {
        setSelections(prev => {
          const updated = new Map(prev)
          updated.set(selection.userId, selection)
          return updated
        })
      },
      onSelectionClear: (clearedUserId) => {
        setSelections(prev => {
          const updated = new Map(prev)
          updated.delete(clearedUserId)
          return updated
        })
      },
      onAnnotationsSync: (syncedAnnotations) => {
        setAnnotations(syncedAnnotations)
      },
      onModelSync: (changes) => {
        setModelChanges(prev => [...prev, ...changes])
      },
      onModelConflict: (conflict, resolution) => {
        toast({
          title: 'Model Conflict',
          description: 'A conflict was detected and resolved',
          variant: 'destructive'
        })
        if (resolution) {
          setModelChanges(prev => [...prev, resolution])
        }
      },
      onViewStateSync: (states) => {
        setViewStates(states)
      },
      onError: (err) => {
        setError(err)
        toast({
          title: 'Collaboration Error',
          description: err.message,
          variant: 'destructive'
        })
      }
    }

    client.setCallbacks(callbacks)

    // Connect and join room
    const initialize = async () => {
      try {
        await client.connect()
        setIsConnected(true)
        client.joinRoom(roomId, userId, userName, userRole)
      } catch (err) {
        console.error('Failed to connect:', err)
        setError({
          code: 'CONNECTION_ERROR',
          message: 'Failed to connect to collaboration server'
        })
      }
    }

    initialize()

    // Cleanup
    return () => {
      if (clientRef.current?.isConnected()) {
        clientRef.current.leaveRoom(roomId, userId)
      }
    }
  }, [roomId, userId, userName, userRole, toast])

  // Cursor position update
  const updateCursor = useCallback((position: { x: number; y: number; z: number }, direction: { x: number; y: number; z: number }) => {
    if (!clientRef.current?.isConnected()) return

    const cursor: Cursor3D = {
      userId,
      position,
      direction
    }

    clientRef.current.sendCursorPosition(cursor)
  }, [userId])

  // Element selection
  const selectElements = useCallback((elementIds: string[], mode: 'single' | 'multiple' | 'box' = 'single') => {
    if (!clientRef.current?.isConnected()) return
    clientRef.current.selectElements(elementIds, mode)
  }, [])

  const deselectElements = useCallback((elementIds: string[]) => {
    if (!clientRef.current?.isConnected()) return
    clientRef.current.deselectElements(elementIds)
  }, [])

  // Annotations
  const createAnnotation = useCallback((
    content: string,
    type: Annotation['type'],
    position: { x: number; y: number; z: number },
    elementId?: string
  ) => {
    if (!clientRef.current?.isConnected()) return

    const annotation: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'> = {
      userId,
      userName,
      content,
      type,
      position,
      elementId,
      status: 'open'
    }

    clientRef.current.createAnnotation(annotation)
  }, [userId, userName])

  const updateAnnotation = useCallback((id: string, updates: Partial<Annotation>) => {
    if (!clientRef.current?.isConnected()) return
    clientRef.current.updateAnnotation(id, updates)
  }, [])

  const deleteAnnotation = useCallback((id: string) => {
    if (!clientRef.current?.isConnected()) return
    clientRef.current.deleteAnnotation(id)
  }, [])

  const replyToAnnotation = useCallback((annotationId: string, content: string) => {
    if (!clientRef.current?.isConnected()) return
    clientRef.current.replyToAnnotation(annotationId, content)
  }, [])

  // Model changes
  const sendModelChange = useCallback((change: Omit<ModelChange, 'id' | 'timestamp' | 'userId'>) => {
    if (!clientRef.current?.isConnected()) return
    clientRef.current.sendModelChange({ ...change, userId })
  }, [userId])

  // View state
  const updateViewState = useCallback((viewState: Partial<BIMViewerState>) => {
    if (!clientRef.current?.isConnected()) return
    clientRef.current.updateViewState(viewState)
  }, [])

  return {
    // Connection state
    isConnected,
    error,
    
    // Room state
    room,
    users,
    
    // Collaboration data
    cursors: Array.from(cursors.values()),
    selections: Array.from(selections.values()),
    annotations,
    modelChanges,
    viewStates,
    
    // Actions
    updateCursor,
    selectElements,
    deselectElements,
    createAnnotation,
    updateAnnotation,
    deleteAnnotation,
    replyToAnnotation,
    sendModelChange,
    updateViewState
  }
}

// Hook for cursor tracking
export function useCursorTracking(
  isEnabled: boolean,
  onCursorUpdate: (position: { x: number; y: number; z: number }, direction: { x: number; y: number; z: number }) => void
) {
  const lastUpdateRef = useRef<number>(0)
  const throttleDelay = 50 // ms

  const trackCursor = useCallback((event: MouseEvent, camera: any, scene: any) => {
    if (!isEnabled) return

    const now = Date.now()
    if (now - lastUpdateRef.current < throttleDelay) return
    lastUpdateRef.current = now

    // Convert screen coordinates to 3D position
    const rect = (event.target as HTMLElement).getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    const y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    // Raycasting to get 3D position
    const raycaster = new (window as any).THREE.Raycaster()
    raycaster.setFromCamera({ x, y }, camera)

    const intersects = raycaster.intersectObjects(scene.children, true)
    
    if (intersects.length > 0) {
      const position = intersects[0].point
      const direction = raycaster.ray.direction

      onCursorUpdate(
        { x: position.x, y: position.y, z: position.z },
        { x: direction.x, y: direction.y, z: direction.z }
      )
    }
  }, [isEnabled, onCursorUpdate, throttleDelay])

  return { trackCursor }
}

// Hook for managing user presence indicators
export function useUserPresence(users: UserPresence[]) {
  const [activeUsers, setActiveUsers] = useState<UserPresence[]>([])

  useEffect(() => {
    setActiveUsers(users.filter(u => u.isActive))
  }, [users])

  const getUserColor = useCallback((userId: string) => {
    const user = activeUsers.find(u => u.userId === userId)
    return user?.color || '#999999'
  }, [activeUsers])

  const getUserName = useCallback((userId: string) => {
    const user = activeUsers.find(u => u.userId === userId)
    return user?.userName || 'Unknown User'
  }, [activeUsers])

  return {
    activeUsers,
    getUserColor,
    getUserName
  }
}

// Hook for annotation management
export function useAnnotations(
  annotations: Annotation[],
  createAnnotation: (content: string, type: Annotation['type'], position: { x: number; y: number; z: number }, elementId?: string) => void,
  updateAnnotation: (id: string, updates: Partial<Annotation>) => void,
  deleteAnnotation: (id: string) => void,
  replyToAnnotation: (annotationId: string, content: string) => void
) {
  const [selectedAnnotation, setSelectedAnnotation] = useState<Annotation | null>(null)
  const [filter, setFilter] = useState<Annotation['type'] | 'all'>('all')

  const filteredAnnotations = annotations.filter(
    ann => filter === 'all' || ann.type === filter
  )

  const openAnnotations = filteredAnnotations.filter(ann => ann.status === 'open')
  const resolvedAnnotations = filteredAnnotations.filter(ann => ann.status === 'resolved')

  return {
    annotations: filteredAnnotations,
    openAnnotations,
    resolvedAnnotations,
    selectedAnnotation,
    setSelectedAnnotation,
    filter,
    setFilter,
    createAnnotation,
    updateAnnotation,
    deleteAnnotation,
    replyToAnnotation
  }
}

// Hook for optimistic updates
export function useOptimisticUpdate<T>(
  data: T[],
  onUpdate: (item: T) => void
) {
  const [optimisticData, setOptimisticData] = useState<T[]>(data)
  const [pending, setPending] = useState<Set<string>>(new Set())

  useEffect(() => {
    setOptimisticData(data)
  }, [data])

  const optimisticUpdate = useCallback((item: T & { id: string }, updateFn: () => Promise<void>) => {
    // Add to optimistic state immediately
    setOptimisticData(prev => [...prev.filter((i: any) => i.id !== item.id), item])
    setPending(prev => new Set(prev).add(item.id))

    // Perform actual update
    updateFn()
      .then(() => {
        setPending(prev => {
          const updated = new Set(prev)
          updated.delete(item.id)
          return updated
        })
      })
      .catch(() => {
        // Revert on error
        setOptimisticData(data)
        setPending(prev => {
          const updated = new Set(prev)
          updated.delete(item.id)
          return updated
        })
      })
  }, [data])

  return {
    data: optimisticData,
    pending,
    optimisticUpdate
  }
}