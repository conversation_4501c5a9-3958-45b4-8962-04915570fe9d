import { NextRequest, NextResponse } from 'next/server'
import { jobProcessor, JobConfig } from '@/lib/services/job-processor'
import { verifySession } from '@/lib/db/auth'

// Enhanced console logger
function log(level: string, message: string, data?: any) {
  const timestamp = new Date().toISOString()
  const levelEmoji = {
    error: '❌',
    warn: '⚠️',
    info: '✅',
    debug: '🔍',
    http: '🌐'
  }[level.toLowerCase()] || '📝'
  
  const prefix = `${levelEmoji} [${timestamp}] [TAKEOFF JOBS API] ${level}:`
  
  if (data) {
    console.log(prefix, message, JSON.stringify(data, null, 2))
  } else {
    console.log(prefix, message)
  }
}

// Create a new processing job
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  const requestId = `job-${Date.now()}`
  
  log('info', '🚀 POST /api/takeoff/jobs - Creating new job', { requestId })
  
  try {
    // Get session for user authentication
    const sessionData = await verifySession()
    if (!sessionData) {
      log('error', '❌ Unauthorized request', { requestId })
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    const { user } = sessionData

    const formData = await request.formData()
    const projectContextStr = formData.get('projectContext') as string
    const fileCount = parseInt(formData.get('fileCount') as string || '0')
    
    if (!projectContextStr || fileCount === 0) {
      log('error', '❌ Invalid request data', { requestId, hasContext: !!projectContextStr, fileCount })
      return NextResponse.json(
        { error: 'Project context and files are required' },
        { status: 400 }
      )
    }

    const projectContext = JSON.parse(projectContextStr)
    const files: JobConfig['files'] = []

    // Collect all files from formData
    for (let i = 0; i < fileCount; i++) {
      const fileBlob = formData.get(`file_${i}`) as Blob
      const fileName = formData.get(`fileName_${i}`) as string
      
      if (!fileBlob || !fileName) continue

      const arrayBuffer = await fileBlob.arrayBuffer()
      files.push({
        name: fileName,
        size: fileBlob.size,
        mimeType: fileBlob.type,
        data: Buffer.from(arrayBuffer)
      })
    }

    log('info', '📦 Creating job with files', {
      requestId,
      userId: user.id,
      projectId: projectContext.id,
      fileCount: files.length,
      totalSize: files.reduce((sum, f) => sum + f.size, 0),
      companyType: projectContext.companyType
    })

    // Create job configuration
    const jobConfig: JobConfig = {
      userId: user.id,
      projectId: projectContext.id,
      projectContext,
      files
    }

    // Create and start processing job
    const job = await jobProcessor.createJob(jobConfig)

    log('info', '✨ Job created successfully', {
      requestId,
      jobId: job.id,
      status: job.status,
      duration: `${Date.now() - startTime}ms`
    })

    return NextResponse.json({
      success: true,
      data: {
        jobId: job.id,
        status: job.status,
        totalFiles: job.totalFiles,
        message: 'Job created and queued for processing'
      },
      metadata: {
        requestId,
        processingTime: `${Date.now() - startTime}ms`
      }
    })

  } catch (error) {
    log('error', '❌ Failed to create job', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    
    return NextResponse.json(
      { 
        error: 'Failed to create processing job',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId
      },
      { status: 500 }
    )
  }
}

// Get job status
export async function GET(request: NextRequest) {
  const startTime = Date.now()
  const url = new URL(request.url)
  const jobId = url.searchParams.get('jobId')
  
  if (!jobId) {
    return NextResponse.json(
      { error: 'Job ID is required' },
      { status: 400 }
    )
  }

  log('info', '🔍 GET /api/takeoff/jobs - Fetching job status', { jobId })

  try {
    const job = await jobProcessor.getJobStatus(jobId)
    
    if (!job) {
      log('warn', '⚠️ Job not found', { jobId })
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    log('info', '✅ Job status retrieved', {
      jobId,
      status: job.status,
      progress: job.totalFiles > 0 ? Math.round((job.processedFiles / job.totalFiles) * 100) : 0,
      duration: `${Date.now() - startTime}ms`
    })

    return NextResponse.json({
      success: true,
      data: job,
      metadata: {
        processingTime: `${Date.now() - startTime}ms`
      }
    })

  } catch (error) {
    log('error', '❌ Failed to get job status', {
      jobId,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { 
        error: 'Failed to retrieve job status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Cancel a job
export async function DELETE(request: NextRequest) {
  const startTime = Date.now()
  const url = new URL(request.url)
  const jobId = url.searchParams.get('jobId')
  
  if (!jobId) {
    return NextResponse.json(
      { error: 'Job ID is required' },
      { status: 400 }
    )
  }

  log('info', '🛑 DELETE /api/takeoff/jobs - Cancelling job', { jobId })

  try {
    const cancelled = await jobProcessor.cancelJob(jobId)
    
    if (!cancelled) {
      log('warn', '⚠️ Job could not be cancelled', { jobId })
      return NextResponse.json(
        { error: 'Job cannot be cancelled (may be already processing or completed)' },
        { status: 400 }
      )
    }

    log('info', '✅ Job cancelled successfully', {
      jobId,
      duration: `${Date.now() - startTime}ms`
    })

    return NextResponse.json({
      success: true,
      message: 'Job cancelled successfully',
      metadata: {
        processingTime: `${Date.now() - startTime}ms`
      }
    })

  } catch (error) {
    log('error', '❌ Failed to cancel job', {
      jobId,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { 
        error: 'Failed to cancel job',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}