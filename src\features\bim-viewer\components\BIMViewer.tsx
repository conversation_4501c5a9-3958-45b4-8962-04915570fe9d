'use client'

import { Suspense, useRef, useState, useEffect } from 'react'
import { Canvas } from '@react-three/fiber'
import {
  OrbitControls,
  Grid,
  Environment,
  Stats,
  PerspectiveCamera,
  Box,
  Edges,
} from '@react-three/drei'
import * as THREE from 'three'
import { IFCLoader } from 'web-ifc-three'
import { ModelLoadEvent, SelectionEvent, ViewerConfig } from '../types/bim.types'
import { BIMElement } from '@/types'

interface BIMViewerProps {
  modelUrl?: string
  onModelLoad?: (event: ModelLoadEvent) => void
  onElementSelect?: (event: SelectionEvent) => void
  config?: Partial<ViewerConfig>
  className?: string
}

const defaultConfig: ViewerConfig = {
  backgroundColor: '#f0f4f8',
  gridSize: 100,
  gridDivisions: 100,
  ambientLightIntensity: 0.6,
  directionalLightIntensity: 0.8,
  enableShadows: true,
  enablePostProcessing: false,
}

export default function BIMViewer({
  modelUrl,
  onModelLoad,
  onElementSelect,
  config: userConfig,
  className = '',
}: BIMViewerProps) {
  const config = { ...defaultConfig, ...userConfig }
  const [model, setModel] = useState<THREE.Group | null>(null)
  const [selectedElements, setSelectedElements] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const ifcLoaderRef = useRef<IFCLoader | null>(null)
  const sceneRef = useRef<THREE.Scene | null>(null)
  const raycaster = useRef(new THREE.Raycaster())
  const mouse = useRef(new THREE.Vector2())

  // Initialize IFC Loader
  useEffect(() => {
    const initLoader = async () => {
      try {
        const ifcLoader = new IFCLoader()
        await ifcLoader.ifcManager.setWasmPath(
          'https://cdn.jsdelivr.net/npm/web-ifc@0.0.68/'
        )
        ifcLoaderRef.current = ifcLoader
      } catch (err) {
        console.error('Failed to initialize IFC loader:', err)
        setError('Failed to initialize 3D model loader')
      }
    }
    initLoader()
  }, [])

  // Load model when URL changes
  useEffect(() => {
    if (!modelUrl || !ifcLoaderRef.current) return

    const loadModel = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const startTime = performance.now()
        
        if (modelUrl.endsWith('.ifc')) {
          // Load IFC model
          const ifcModel = await ifcLoaderRef.current!.loadAsync(modelUrl)
          const model3d = ifcModel as THREE.Group
          setModel(model3d)
          
          // Calculate bounding box
          const box = new THREE.Box3().setFromObject(model3d)
          const size = box.getSize(new THREE.Vector3())
          const center = box.getCenter(new THREE.Vector3())
          
          // Count elements
          let elementCount = 0
          model3d.traverse(() => elementCount++)
          
          const loadTime = performance.now() - startTime
          
          if (onModelLoad) {
            onModelLoad({
              model: {
                id: crypto.randomUUID(),
                projectId: '',
                name: modelUrl.split('/').pop() || 'model.ifc',
                format: 'ifc',
                url: modelUrl,
                size: 0,
                elements: [],
                metadata: {
                  author: 'Unknown',
                  software: 'Unknown',
                  version: '1.0',
                  units: 'metric',
                },
                createdAt: new Date(),
                updatedAt: new Date(),
              },
              loadTime,
              elementCount,
              boundingBox: {
                min: { x: box.min.x, y: box.min.y, z: box.min.z },
                max: { x: box.max.x, y: box.max.y, z: box.max.z },
              },
            })
          }
        } else {
          // For other formats (GLTF, OBJ), use Three.js loaders
          throw new Error('Only IFC format is currently supported')
        }
      } catch (err) {
        console.error('Failed to load model:', err)
        setError(`Failed to load 3D model: ${err instanceof Error ? err.message : 'Unknown error'}`)
      } finally {
        setLoading(false)
      }
    }

    loadModel()
  }, [modelUrl, onModelLoad])

  // Handle element selection
  const handlePointerDown = (event: any) => {
    if (!model || !sceneRef.current) return

    // Get mouse coordinates from the event
    const rect = event.target.getBoundingClientRect()
    mouse.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    mouse.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    // Cast ray
    const camera = event.camera
    if (camera) {
      raycaster.current.setFromCamera(mouse.current, camera)
      const intersects = raycaster.current.intersectObject(model, true)

      if (intersects.length > 0) {
        const selected = intersects[0]
        const selectedObject = selected.object

        // Mock element data
        const element: BIMElement = {
          id: selectedObject.uuid,
          guid: selectedObject.uuid,
          type: selectedObject.type,
          name: selectedObject.name || 'Unknown Element',
          properties: {},
          parent: selectedObject.parent?.uuid,
          children: selectedObject.children.map(child => child.uuid),
        }

        setSelectedElements([element.id])

        if (onElementSelect) {
          onElementSelect({
            elements: [element],
            event: event as MouseEvent,
            point: {
              x: selected.point.x,
              y: selected.point.y,
              z: selected.point.z,
            },
          })
        }
      }
    }
  }

  // Loading component
  const LoadingBox = () => (
    <Box args={[2, 2, 2]} position={[0, 1, 0]}>
      <meshStandardMaterial color="#64748b" wireframe />
      <Edges color="#1e293b" />
    </Box>
  )

  return (
    <div className={`relative w-full h-full ${className}`}>
      <Canvas
        shadows={config.enableShadows}
        onCreated={({ scene }) => {
          sceneRef.current = scene
          scene.background = new THREE.Color(config.backgroundColor)
        }}
      >
        <PerspectiveCamera makeDefault position={[20, 20, 20]} fov={45} />
        
        {/* Lighting */}
        <ambientLight intensity={config.ambientLightIntensity} />
        <directionalLight
          position={[10, 20, 10]}
          intensity={config.directionalLightIntensity}
          castShadow={config.enableShadows}
          shadow-mapSize={[2048, 2048]}
          shadow-camera-far={100}
          shadow-camera-left={-50}
          shadow-camera-right={50}
          shadow-camera-top={50}
          shadow-camera-bottom={-50}
        />

        {/* Grid */}
        <Grid
          args={[config.gridSize, config.gridSize, config.gridDivisions, config.gridDivisions]}
          cellColor="#e2e8f0"
          sectionColor="#94a3b8"
          fadeDistance={100}
          fadeStrength={1}
          infiniteGrid
        />

        {/* Model */}
        <Suspense fallback={<LoadingBox />}>
          {model && (
            <primitive
              object={model}
              onPointerDown={handlePointerDown}
              castShadow={config.enableShadows}
              receiveShadow={config.enableShadows}
            />
          )}
        </Suspense>

        {/* Environment */}
        <Environment preset="city" />

        {/* Controls */}
        <OrbitControls
          enableDamping
        />

        {/* Performance stats */}
        {process.env.NODE_ENV === 'development' && <Stats />}
      </Canvas>

      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 shadow-xl">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto" />
            <p className="mt-4 text-sm text-gray-600">Loading 3D model...</p>
          </div>
        </div>
      )}

      {/* Error overlay */}
      {error && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 shadow-xl max-w-md">
            <div className="text-red-500 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <p className="text-center text-gray-800 font-semibold">Failed to load 3D model</p>
            <p className="text-center text-sm text-gray-600 mt-2">{error}</p>
          </div>
        </div>
      )}
    </div>
  )
}