// Example integration of Socket.io collaboration with BIM viewer

'use client'

import React, { useRef, useState } from 'react'
import { Canvas, useThree } from '@react-three/fiber'
import { OrbitControls } from '@react-three/drei'
import { 
  CollaborationProvider, 
  CollaborationToolbar,
  CursorsContainer,
  AnnotationsContainer,
  SelectionsContainer,
  useCollaboration
} from './components'
import { useCursorTracking, useAnnotations } from './hooks'
import { Button } from '@/components/ui/button'
import { MessageSquare, MousePointer } from 'lucide-react'

// BIM Viewer with collaboration features
function CollaborativeBIMViewer() {
  const {
    cursors,
    selections,
    annotations,
    users,
    updateCursor,
    selectElements,
    deselectElements,
    createAnnotation,
    updateAnnotation,
    deleteAnnotation,
    replyToAnnotation,
    updateViewState
  } = useCollaboration()

  const { scene, camera } = useThree()
  const [showCursors, setShowCursors] = useState(true)
  const [showSelections, setShowSelections] = useState(true)
  const [isAnnotating, setIsAnnotating] = useState(false)

  // Cursor tracking
  const { trackCursor } = useCursorTracking(
    showCursors,
    updateCursor
  )

  // Annotation management
  const {
    selectedAnnotation,
    setSelectedAnnotation,
    openAnnotations
  } = useAnnotations(
    annotations,
    createAnnotation,
    updateAnnotation,
    deleteAnnotation,
    replyToAnnotation
  )

  // Get user color for selections
  const getUserColor = (userId: string) => {
    const user = users.find(u => u.userId === userId)
    return user?.color || '#999999'
  }

  // Handle mouse move for cursor tracking
  const handleMouseMove = (event: any) => {
    if (event.nativeEvent) {
      trackCursor(event.nativeEvent, camera, scene)
    }
  }

  // Handle click for annotations or selection
  const handleClick = (event: any) => {
    if (isAnnotating) {
      // Create annotation at click position
      const rect = (event.target as HTMLElement).getBoundingClientRect()
      const x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      const y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      // Raycasting to get 3D position
      const raycaster = new (window as any).THREE.Raycaster()
      raycaster.setFromCamera({ x, y }, camera)

      const intersects = raycaster.intersectObjects(scene.children, true)
      
      if (intersects.length > 0) {
        const position = intersects[0].point
        const elementId = intersects[0].object.userData.elementId

        // Prompt for annotation content (in real app, use a modal)
        const content = prompt('Enter annotation:')
        if (content) {
          createAnnotation(
            content,
            'comment',
            { x: position.x, y: position.y, z: position.z },
            elementId
          )
        }
      }
      
      setIsAnnotating(false)
    } else {
      // Handle element selection
      // Similar raycasting logic for selection...
    }
  }

  // Update view state when camera changes
  const handleCameraChange = () => {
    const cameraPosition = camera.position
    const target = (event?.target as any)?.target || { x: 0, y: 0, z: 0 }
    
    updateViewState({
      cameraPosition: { 
        x: cameraPosition.x, 
        y: cameraPosition.y, 
        z: cameraPosition.z 
      },
      cameraTarget: target
    })
  }

  return (
    <>
      {/* 3D Scene */}
      <group onPointerMove={handleMouseMove} onPointerDown={handleClick}>
        {/* Your BIM model would go here */}
        <mesh>
          <boxGeometry args={[2, 2, 2]} />
          <meshStandardMaterial color="#4ECDC4" />
        </mesh>

        {/* Collaboration visualizations */}
        <CursorsContainer
          cursors={cursors}
          users={users}
          visible={showCursors}
        />

        <AnnotationsContainer
          annotations={annotations}
          onUpdate={updateAnnotation}
          onDelete={deleteAnnotation}
          onReply={replyToAnnotation}
          selectedAnnotation={selectedAnnotation}
          onSelectAnnotation={setSelectedAnnotation}
        />

        <SelectionsContainer
          selections={selections}
          getUserColor={getUserColor}
          visible={showSelections}
          scene={scene}
        />
      </group>

      {/* Camera controls */}
      <OrbitControls
        onChange={handleCameraChange}
        enableDamping
      />

      {/* Annotation mode button */}
      <Button
        className="absolute bottom-4 left-4 z-50"
        variant={isAnnotating ? 'default' : 'outline'}
        onClick={() => setIsAnnotating(!isAnnotating)}
      >
        <MessageSquare className="h-4 w-4 mr-2" />
        {isAnnotating ? 'Click to annotate' : 'Add Annotation'}
      </Button>
    </>
  )
}

// Main component with providers
export function CollaborativeBIMViewerExample() {
  // These would come from your auth/user context
  const userId = 'user123'
  const userName = 'John Doe'
  const userRole = 'engineer'
  const roomId = 'project123_model456'

  return (
    <CollaborationProvider
      roomId={roomId}
      userId={userId}
      userName={userName}
      userRole={userRole}
    >
      <div className="relative w-full h-screen">
        <Canvas
          camera={{ position: [5, 5, 5], fov: 60 }}
          style={{ background: '#f0f0f0' }}
        >
          <ambientLight intensity={0.5} />
          <directionalLight position={[10, 10, 10]} intensity={1} />
          <CollaborativeBIMViewer />
        </Canvas>

        {/* UI Overlay */}
        <CollaborationToolbar />
      </div>
    </CollaborationProvider>
  )
}

// Usage in your BIM viewer page:
/*
import { CollaborativeBIMViewerExample } from '@/lib/socket/example-integration'

export default function BIMViewerPage() {
  return <CollaborativeBIMViewerExample />
}
*/