import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'AI Construction Management Platform',
  description: 'Next-generation AI-powered construction management platform combining the best features from industry leaders',
  keywords: 'construction, AI, project management, BIM, safety, scheduling',
  authors: [{ name: 'AI Construction Management Team' }],
  openGraph: {
    title: 'AI Construction Management Platform',
    description: 'Revolutionary AI-powered construction management',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
