# API Documentation

## Overview

The AI Construction Management platform provides a comprehensive REST API for all construction management operations. All API endpoints are secured with JWT authentication and follow RESTful conventions.

## Base URL

```
Development: http://localhost:3000/api
Production: https://api.construction.ai/api
```

## Authentication

All API requests (except auth endpoints) require authentication using JWT tokens.

### Headers

```http
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

### Getting a Token

```http
POST /api/auth/signin
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "role": "PROJECT_MANAGER"
  }
}
```

## API Endpoints

### Authentication

#### Sign Up
```http
POST /api/auth/signup
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "John Doe",
  "company": "Construction Co",
  "companyType": "GENERAL_CONTRACTOR"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "USER"
  }
}
```

#### Sign In
```http
POST /api/auth/signin
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Sign Out
```http
POST /api/auth/signout
```

**Response:**
```json
{
  "message": "Signed out successfully"
}
```

#### Verify Token
```http
GET /api/auth/verify
```

**Response:**
```json
{
  "valid": true,
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "role": "PROJECT_MANAGER"
  }
}
```

### Projects

#### List Projects
```http
GET /api/projects
```

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `status` (string): Filter by status (PLANNING, ACTIVE, COMPLETED, ON_HOLD)
- `search` (string): Search in project name

**Response:**
```json
{
  "projects": [
    {
      "id": "uuid",
      "name": "Downtown Tower",
      "description": "50-story mixed-use development",
      "status": "ACTIVE",
      "startDate": "2024-01-15",
      "endDate": "2025-12-31",
      "budget": 150000000,
      "progress": 45.5,
      "location": {
        "address": "123 Main St",
        "city": "New York",
        "state": "NY",
        "country": "USA",
        "coordinates": {
          "lat": 40.7128,
          "lng": -74.0060
        }
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

#### Get Project
```http
GET /api/projects/:id
```

**Response:**
```json
{
  "id": "uuid",
  "name": "Downtown Tower",
  "description": "50-story mixed-use development",
  "status": "ACTIVE",
  "startDate": "2024-01-15",
  "endDate": "2025-12-31",
  "budget": 150000000,
  "spentBudget": 67500000,
  "progress": 45.5,
  "team": [
    {
      "userId": "uuid",
      "role": "PROJECT_MANAGER",
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  ],
  "metrics": {
    "safety": {
      "score": 98.5,
      "incidents": 2,
      "daysWithoutIncident": 45
    },
    "schedule": {
      "variance": -2.5,
      "criticalPathDelay": 0
    },
    "quality": {
      "score": 96.0,
      "defects": 12,
      "rework": 3
    }
  }
}
```

#### Create Project
```http
POST /api/projects
```

**Request Body:**
```json
{
  "name": "New Office Building",
  "description": "10-story office complex",
  "startDate": "2024-06-01",
  "endDate": "2025-12-31",
  "budget": 50000000,
  "location": {
    "address": "456 Park Ave",
    "city": "Los Angeles",
    "state": "CA",
    "country": "USA",
    "coordinates": {
      "lat": 34.0522,
      "lng": -118.2437
    }
  },
  "team": [
    {
      "userId": "uuid",
      "role": "PROJECT_MANAGER"
    }
  ]
}
```

#### Update Project
```http
PUT /api/projects/:id
```

**Request Body:** (Partial update supported)
```json
{
  "name": "Updated Project Name",
  "status": "ON_HOLD",
  "budget": 55000000
}
```

#### Delete Project
```http
DELETE /api/projects/:id
```

### Documents

#### Upload Document
```http
POST /api/documents/upload
```

**Request:** Multipart form data
- `file`: The document file
- `projectId`: Project UUID
- `type`: Document type (BLUEPRINT, CONTRACT, PERMIT, REPORT, OTHER)
- `name`: Document name
- `description`: Optional description

**Response:**
```json
{
  "id": "uuid",
  "name": "Floor Plans v2",
  "type": "BLUEPRINT",
  "url": "/uploads/documents/uuid.pdf",
  "size": 2048576,
  "mimeType": "application/pdf",
  "uploadedBy": "John Doe",
  "uploadedAt": "2024-03-15T10:30:00Z"
}
```

#### List Documents
```http
GET /api/documents
```

**Query Parameters:**
- `projectId` (string): Filter by project
- `type` (string): Filter by document type
- `search` (string): Search in document name

#### AI Document Analysis
```http
POST /api/documents/:id/analyze
```

**Request Body:**
```json
{
  "analysisType": "takeoff" | "safety" | "compliance" | "general"
}
```

**Response:**
```json
{
  "documentId": "uuid",
  "analysisType": "takeoff",
  "results": {
    "materials": [
      {
        "item": "Concrete",
        "quantity": 500,
        "unit": "cubic yards",
        "confidence": 0.95
      }
    ],
    "summary": "Analyzed 15 pages, extracted 23 material items",
    "processingTime": 3.5
  }
}
```

### Takeoff & Estimating

#### Create Takeoff Job
```http
POST /api/takeoff
```

**Request Body:**
```json
{
  "projectId": "uuid",
  "documentIds": ["uuid1", "uuid2"],
  "scope": "Full building takeoff",
  "includePricing": true
}
```

**Response:**
```json
{
  "jobId": "uuid",
  "status": "PROCESSING",
  "createdAt": "2024-03-15T10:30:00Z",
  "estimatedCompletion": "2024-03-15T10:35:00Z"
}
```

#### Get Takeoff Job Status
```http
GET /api/takeoff/jobs/:jobId
```

**Response:**
```json
{
  "jobId": "uuid",
  "status": "COMPLETED",
  "progress": 100,
  "results": {
    "materials": [
      {
        "category": "Concrete",
        "items": [
          {
            "name": "Foundation Concrete",
            "quantity": 250,
            "unit": "CY",
            "unitPrice": 150,
            "totalPrice": 37500
          }
        ]
      }
    ],
    "totalCost": 1250000,
    "confidence": 0.92
  }
}
```

### Schedule

#### Get Project Schedule
```http
GET /api/projects/:projectId/schedule
```

**Response:**
```json
{
  "projectId": "uuid",
  "tasks": [
    {
      "id": "task-uuid",
      "name": "Foundation Work",
      "startDate": "2024-04-01",
      "endDate": "2024-04-30",
      "duration": 30,
      "progress": 75,
      "dependencies": ["task-uuid-2"],
      "resources": [
        {
          "type": "CREW",
          "name": "Concrete Crew A",
          "allocation": 100
        }
      ],
      "isCriticalPath": true
    }
  ],
  "milestones": [
    {
      "id": "milestone-uuid",
      "name": "Foundation Complete",
      "date": "2024-04-30",
      "status": "ON_TRACK"
    }
  ]
}
```

#### AI Schedule Optimization
```http
POST /api/projects/:projectId/schedule/optimize
```

**Request Body:**
```json
{
  "constraints": {
    "mustFinishBy": "2025-12-31",
    "weatherConsideration": true,
    "resourceLimits": {
      "maxCrews": 5,
      "maxDailyBudget": 50000
    }
  },
  "optimizeFor": "TIME" | "COST" | "BALANCED"
}
```

**Response:**
```json
{
  "scenarios": [
    {
      "id": "scenario-1",
      "duration": 365,
      "cost": 45000000,
      "endDate": "2025-11-15",
      "score": 0.95,
      "changes": [
        {
          "task": "Foundation Work",
          "originalDuration": 30,
          "optimizedDuration": 25,
          "strategy": "Add second crew"
        }
      ]
    }
  ],
  "recommendation": "scenario-1",
  "savings": {
    "time": 45,
    "cost": 5000000
  }
}
```

### Safety

#### Report Safety Incident
```http
POST /api/safety/incidents
```

**Request Body:**
```json
{
  "projectId": "uuid",
  "type": "NEAR_MISS" | "MINOR_INJURY" | "MAJOR_INJURY" | "PROPERTY_DAMAGE",
  "date": "2024-03-15",
  "time": "14:30",
  "location": "Level 5, North Tower",
  "description": "Worker slipped on wet surface",
  "involvedPersonnel": ["John Doe", "Jane Smith"],
  "witnessess": ["Mike Johnson"],
  "immediateActions": "Area cordoned off, first aid administered",
  "rootCause": "Water leak from above floor"
}
```

#### Get Safety Metrics
```http
GET /api/projects/:projectId/safety/metrics
```

**Response:**
```json
{
  "projectId": "uuid",
  "metrics": {
    "totalIncidents": 5,
    "ltir": 0.8,
    "trir": 1.2,
    "daysWithoutIncident": 45,
    "safetyScore": 98.5,
    "trends": {
      "monthly": [
        {
          "month": "2024-01",
          "incidents": 1,
          "score": 97.0
        }
      ]
    },
    "riskAreas": [
      {
        "area": "Scaffolding",
        "riskLevel": "MEDIUM",
        "recommendations": ["Additional training needed"]
      }
    ]
  }
}
```

### AI Assistant

#### Chat with AI
```http
POST /api/ai/chat
```

**Request Body:**
```json
{
  "message": "What's the status of the foundation work?",
  "projectId": "uuid",
  "context": {
    "includeSchedule": true,
    "includeBudget": true,
    "includeSafety": true
  }
}
```

**Response:**
```json
{
  "response": "The foundation work is currently 75% complete and on schedule. The concrete pour for the north section was completed yesterday, and the team is now working on the south section. Based on current progress, we expect to complete foundation work by April 30th, which is in line with the original schedule. Budget-wise, we've spent $850,000 of the allocated $1.2M for foundation work. No safety incidents have been reported in this phase.",
  "suggestions": [
    "Review concrete curing times for upcoming sections",
    "Schedule foundation inspection for next week"
  ],
  "relatedData": {
    "tasks": ["task-uuid-1", "task-uuid-2"],
    "documents": ["doc-uuid-1"]
  }
}
```

#### Generate Report
```http
POST /api/ai/reports/generate
```

**Request Body:**
```json
{
  "projectId": "uuid",
  "reportType": "DAILY" | "WEEKLY" | "SAFETY" | "PROGRESS" | "EXECUTIVE",
  "dateRange": {
    "start": "2024-03-01",
    "end": "2024-03-15"
  },
  "sections": ["summary", "progress", "safety", "issues", "lookahead"]
}
```

### Analytics

#### Get Project Analytics
```http
GET /api/projects/:projectId/analytics
```

**Query Parameters:**
- `metrics`: Comma-separated list of metrics (budget,schedule,safety,quality)
- `period`: Time period (daily,weekly,monthly)
- `startDate`: Start date for analysis
- `endDate`: End date for analysis

**Response:**
```json
{
  "projectId": "uuid",
  "period": "monthly",
  "metrics": {
    "budget": {
      "planned": 150000000,
      "actual": 67500000,
      "forecast": 148000000,
      "variance": -1.33,
      "trend": [
        {
          "date": "2024-01",
          "planned": 10000000,
          "actual": 9500000
        }
      ]
    },
    "schedule": {
      "plannedCompletion": 45,
      "actualCompletion": 47,
      "variance": 2,
      "criticalPathStatus": "ON_TRACK"
    },
    "safety": {
      "score": 98.5,
      "incidents": 2,
      "trend": "IMPROVING"
    },
    "quality": {
      "score": 96.0,
      "defects": 12,
      "reworkRate": 2.5
    }
  },
  "insights": [
    {
      "type": "RISK",
      "severity": "MEDIUM",
      "message": "Material delivery delays may impact schedule",
      "recommendation": "Consider ordering materials 2 weeks earlier"
    }
  ]
}
```

### Field Operations

#### Create Field Report
```http
POST /api/field/reports
```

**Request Body:**
```json
{
  "projectId": "uuid",
  "date": "2024-03-15",
  "weather": {
    "condition": "Partly Cloudy",
    "temperature": 72,
    "windSpeed": 10
  },
  "manpower": {
    "planned": 45,
    "actual": 42,
    "contractors": [
      {
        "company": "ABC Concrete",
        "workers": 12
      }
    ]
  },
  "equipment": [
    {
      "type": "Crane",
      "id": "CR-001",
      "hours": 8,
      "issues": "None"
    }
  ],
  "workCompleted": [
    "Completed concrete pour for Level 5 slab",
    "Installed rebar for Level 6"
  ],
  "issues": [
    {
      "description": "Material delivery delayed",
      "impact": "LOW",
      "resolution": "Rescheduled for tomorrow"
    }
  ],
  "photos": ["photo-uuid-1", "photo-uuid-2"]
}
```

### IoT & Sensors

#### Get Sensor Data
```http
GET /api/iot/sensors/:sensorId/data
```

**Query Parameters:**
- `startTime`: ISO timestamp
- `endTime`: ISO timestamp
- `interval`: Data interval (1m, 5m, 1h, 1d)

**Response:**
```json
{
  "sensorId": "sensor-uuid",
  "type": "TEMPERATURE",
  "location": "Concrete Pour Area B",
  "data": [
    {
      "timestamp": "2024-03-15T10:00:00Z",
      "value": 28.5,
      "unit": "celsius"
    }
  ],
  "alerts": [
    {
      "timestamp": "2024-03-15T14:00:00Z",
      "type": "HIGH_TEMPERATURE",
      "message": "Temperature exceeded threshold"
    }
  ]
}
```

## Error Responses

All error responses follow a consistent format:

```json
{
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "Project not found",
    "details": {
      "projectId": "invalid-uuid"
    }
  }
}
```

### Common Error Codes

- `UNAUTHORIZED`: Missing or invalid authentication
- `FORBIDDEN`: Insufficient permissions
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `VALIDATION_ERROR`: Invalid request data
- `CONFLICT`: Resource conflict (e.g., duplicate email)
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_SERVER_ERROR`: Server error

## Rate Limiting

API requests are rate-limited to ensure fair usage:

- **Standard tier**: 100 requests per minute
- **Premium tier**: 1000 requests per minute
- **Enterprise tier**: Unlimited

Rate limit headers are included in all responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1647360000
```

## Webhooks

Configure webhooks to receive real-time notifications:

### Available Events

- `project.created`
- `project.updated`
- `project.completed`
- `safety.incident`
- `schedule.delayed`
- `document.processed`
- `budget.exceeded`

### Webhook Payload

```json
{
  "event": "project.updated",
  "timestamp": "2024-03-15T10:30:00Z",
  "data": {
    "projectId": "uuid",
    "changes": {
      "status": {
        "old": "PLANNING",
        "new": "ACTIVE"
      }
    }
  }
}
```

## Best Practices

1. **Always use HTTPS** in production
2. **Include proper error handling** for all API calls
3. **Implement exponential backoff** for retries
4. **Cache responses** where appropriate
5. **Use pagination** for large datasets
6. **Include correlation IDs** in requests for debugging
7. **Validate webhook signatures** for security

## SDK Examples

### JavaScript/TypeScript

```typescript
import { ConstructionAPI } from '@construction/sdk';

const api = new ConstructionAPI({
  baseURL: 'https://api.construction.ai',
  apiKey: 'your-api-key'
});

// Get project
const project = await api.projects.get('project-uuid');

// Create safety incident
const incident = await api.safety.reportIncident({
  projectId: 'project-uuid',
  type: 'NEAR_MISS',
  description: 'Close call with equipment'
});
```

### Python

```python
from construction_sdk import ConstructionAPI

api = ConstructionAPI(
    base_url='https://api.construction.ai',
    api_key='your-api-key'
)

# Get project analytics
analytics = api.projects.get_analytics(
    project_id='project-uuid',
    metrics=['budget', 'schedule'],
    period='monthly'
)
```

## API Versioning

The API uses URL versioning. The current version is v1. When breaking changes are introduced, a new version will be released.

```
https://api.construction.ai/v1/projects
https://api.construction.ai/v2/projects  (future)
```

Deprecated endpoints will be supported for at least 6 months after a new version is released.