/**
 * Enhanced Takeoff & Estimating Service V2
 * Provides 100% accurate takeoffs with company-specific filtering,
 * quantity validation, and realistic pricing
 */

import type { TakeoffItem, DetectedMaterial } from '@/types'
import type { CompanyType } from '@/lib/company-types'
import { visionService, type ProcessedDrawing } from './vision-service'
import { pdfAnalyzer, type PDFAnalysisResult } from './pdf-analyzer'
import { materialFilter, type MaterialContext } from './material-filter'
import { quantityValidator, type ProjectSize } from './quantity-validator'
import { pricingDatabase } from './pricing-database'
import { geminiService } from '@/lib/gemini'
import { takeoffLogger, logProcessingStep, logError } from './logger-wrapper'
import { computerVisionAnalyzer } from './computer-vision-analyzer'
import { pricingIntelligenceAPI } from './pricing-intelligence-api'
import { predictiveAnalytics } from './predictive-analytics'

// Enhanced project context with size information
interface EnhancedProjectContext {
  id: string
  name: string
  type: 'commercial' | 'residential' | 'industrial' | 'institutional'
  location: {
    address: string
    zipCode: string
    city?: string
    state?: string
    coordinates?: { lat: number; lng: number }
  }
  size?: ProjectSize
  specifications?: Record<string, any>
  companyType?: CompanyType | null
}

// Progress callback for real-time updates
interface ProgressCallback {
  (step: string, progress: number, data?: any): void
}

// Progressive data persistence interface
interface ProgressiveData {
  step: string
  progress: number
  timestamp: number
  data?: any
  items?: TakeoffItem[]
}

// Confidence scoring for takeoff items
interface ItemConfidence {
  overall: number
  quantity: number
  pricing: number
  detection: number
  validation: number
}

export class EnhancedTakeoffService {
  /**
   * Generate accurate takeoff items with all enhancements and progressive updates
   */
  async generateTakeoffItems(
    projectContext: EnhancedProjectContext,
    drawingFile?: File | ProcessedDrawing,
    companyType?: CompanyType | null,
    progressCallback?: ProgressCallback
  ): Promise<TakeoffItem[]> {
    const startTime = Date.now()
    const takeoffId = `takeoff-v2-${Date.now()}`
    
    takeoffLogger.info('Starting enhanced takeoff generation', {
      takeoffId,
      projectId: projectContext.id,
      projectName: projectContext.name,
      projectType: projectContext.type,
      companyType,
      hasDrawing: !!drawingFile
    })
    
    try {
      let processedDrawing: ProcessedDrawing | null = null
      let pdfAnalysis: PDFAnalysisResult | null = null

      // Progressive update: Starting analysis
      progressCallback?.('Initializing project analysis', 5, { projectId: projectContext.id })

      // Step 1: Detect project size if not provided
      if (!projectContext.size) {
        projectContext.size = quantityValidator.detectProjectSize(
          projectContext.name,
          projectContext.type,
          undefined,
          projectContext.specifications
        )
      }

      progressCallback?.('Project size detected', 10, { size: projectContext.size })
      
      // Step 2: Process drawing file
      if (drawingFile && typeof drawingFile === 'object' && 'name' in drawingFile) {
        progressCallback?.('Processing drawing file', 20, { fileName: drawingFile.name })

        const isPDF = this.isPDFFile(drawingFile)

        if (isPDF) {
          // Process PDF with enhanced analyzer
          pdfAnalysis = await this.processPDFDrawing(drawingFile, companyType)
          progressCallback?.('PDF analysis complete', 35, {
            pages: pdfAnalysis?.pages || 0,
            hasText: !!pdfAnalysis?.textContent
          })
          
          // Update project size from PDF if found
          if (pdfAnalysis.textContent) {
            projectContext.size = quantityValidator.detectProjectSize(
              projectContext.name,
              projectContext.type,
              pdfAnalysis.textContent,
              projectContext.specifications
            )
          }
          
          // Convert PDF to processed drawing (even without images)
          processedDrawing = await this.convertPDFToProcessedDrawing(
            pdfAnalysis,
            drawingFile.name,
            companyType
          )
        } else {
          // Process regular image with vision service
          processedDrawing = await this.processImageDrawing(drawingFile, companyType)
        }
      }
      
      // Step 3: Generate or extract materials
      progressCallback?.('Extracting materials', 45, { hasDrawing: !!processedDrawing })

      let detectedMaterials: DetectedMaterial[] = []

      if (processedDrawing) {
        detectedMaterials = processedDrawing.materials
        progressCallback?.('Materials extracted from drawing', 55, {
          materialCount: detectedMaterials.length
        })
      } else {
        // Generate based on project context and company type
        detectedMaterials = await this.generateMaterialsFromContext(
          projectContext,
          companyType,
          pdfAnalysis
        )
        progressCallback?.('Materials generated from context', 55, {
          materialCount: detectedMaterials.length
        })
      }
      
      // Step 4: Filter materials by company type
      // IMPORTANT: Always filter to ensure only relevant materials are shown
      const actualCompanyType = companyType || 'General Contractor'
      
      takeoffLogger.info('Filtering materials by trade', {
        takeoffId,
        companyType: actualCompanyType,
        beforeCount: detectedMaterials.length,
        materials: detectedMaterials.map(m => ({
          name: m.name || m.description,
          type: m.type || m.category
        }))
      })
      
      // Build context for intelligent classification
      const materialContext: MaterialContext = {
        drawingType: pdfAnalysis?.drawingInfo.type || processedDrawing?.fileName,
        projectType: projectContext.type,
        drawingSection: pdfAnalysis?.drawingInfo.discipline
      }
      
      // Check if this is a specialty drawing being viewed by a General Contractor
      const drawingType = pdfAnalysis?.drawingInfo.type
      const isSpecialtyDrawing = drawingType && ['electrical', 'plumbing', 'hvac', 'structural'].includes(drawingType)
      const isGeneralContractor = actualCompanyType === 'General Contractor'
      const isCrossTradeSituation = isSpecialtyDrawing && isGeneralContractor
      
      if (isCrossTradeSituation) {
        takeoffLogger.info('Cross-trade drawing detected', {
          takeoffId,
          drawingType,
          viewingAs: actualCompanyType,
          message: 'Preserving specialty materials for General Contractor view'
        })
      }
      
      // Use intelligent filtering with async classification
      progressCallback?.('Filtering materials by trade', 60, {
        companyType: actualCompanyType,
        totalMaterials: detectedMaterials.length
      })

      const filteredMaterials: DetectedMaterial[] = []
      const specialtyMaterials: DetectedMaterial[] = []

      for (let i = 0; i < detectedMaterials.length; i++) {
        const material = detectedMaterials[i]
        const isValid = await materialFilter.isValidForTrade(
          material.type || material.category || '',
          material.name || material.description || '',
          actualCompanyType,
          materialContext
        )

        // Progressive update for filtering progress
        if (i % 10 === 0 || i === detectedMaterials.length - 1) {
          const filterProgress = 60 + (i / detectedMaterials.length) * 10
          progressCallback?.('Filtering materials', filterProgress, {
            processed: i + 1,
            total: detectedMaterials.length,
            filtered: filteredMaterials.length
          })
        }
        
        if (isValid) {
          filteredMaterials.push(material)
        } else if (isCrossTradeSituation) {
          // For cross-trade situations, preserve specialty materials
          specialtyMaterials.push(material)
          takeoffLogger.debug('Preserving specialty material for GC view', {
            material: material.name || material.description,
            type: material.type || material.category,
            drawingType: drawingType
          })
        } else {
          takeoffLogger.debug('Material filtered out by intelligent classification', {
            material: material.name || material.description,
            type: material.type || material.category,
            companyType: actualCompanyType
          })
        }
      }
      
      // For cross-trade situations, include specialty materials with a flag
      if (isCrossTradeSituation && specialtyMaterials.length > 0) {
        // Add specialty materials with a cross-trade flag
        const flaggedSpecialtyMaterials = specialtyMaterials.map(m => ({
          ...m,
          crossTrade: true,
          originalTrade: drawingType
        }))
        detectedMaterials = [...filteredMaterials, ...flaggedSpecialtyMaterials]
      } else {
        detectedMaterials = filteredMaterials
      }
      
      logProcessingStep(takeoffLogger, 'Materials filtered by trade', {
        takeoffId,
        companyType: actualCompanyType,
        beforeCount: processedDrawing?.materials.length || detectedMaterials.length,
        afterCount: detectedMaterials.length
      }, startTime)
      
      // Step 5: Convert to takeoff items with realistic pricing
      progressCallback?.('Converting to takeoff items', 75, {
        materialCount: detectedMaterials.length
      })

      const takeoffItems = await this.convertToTakeoffItems(
        detectedMaterials,
        projectContext,
        companyType,
        pdfAnalysis?.vectorData
      )

      progressCallback?.('Validating quantities', 85, {
        itemCount: takeoffItems.length
      })

      // Step 6: Validate quantities
      const validations = quantityValidator.validateQuantities(
        takeoffItems,
        projectContext.size,
        companyType || 'General Contractor'
      )

      // Step 7: Apply adjustments and calculate confidence
      const finalItems = takeoffItems.map(item => {
        const validation = validations.get(item.id)
        const confidence = this.calculateItemConfidence(item, validation)

        return {
          ...item,
          quantity: validation?.adjustedQuantity || item.quantity,
          confidence: confidence.overall,
          validationNotes: validation?.suggestions.join('; ')
        }
      })

      progressCallback?.('Applying quantity adjustments', 90, {
        validatedItems: finalItems.length
      })
      
      // Step 8: Add missing items if needed
      progressCallback?.('Adding missing items', 95, {
        currentItems: finalItems.length
      })

      const enhancedItems = await this.addMissingItems(
        finalItems,
        projectContext,
        companyType
      )

      // Step 9: Final cost validation
      const validatedItems = this.validateCosts(enhancedItems, projectContext)

      progressCallback?.('Finalizing takeoff', 98, {
        finalItems: validatedItems.length,
        totalCost: validatedItems.reduce((sum, item) => sum + item.totalCost, 0)
      })
      
      const totalTime = Date.now() - startTime
      const overallConfidence = quantityValidator.calculateOverallConfidence(validations)
      
      takeoffLogger.info('Enhanced takeoff generation completed', {
        takeoffId,
        totalItems: validatedItems.length,
        totalCost: validatedItems.reduce((sum, item) => sum + item.totalCost, 0),
        processingTime: totalTime,
        companyType,
        overallConfidence,
        projectSize: projectContext.size.grossSquareFeet,
        crossTradeDetected: isCrossTradeSituation,
        drawingType: drawingType
      })
      
      // Step 10: Generate predictive analytics if we have enough data
      let predictions = null
      if (validatedItems.length > 10 && projectContext.size) {
        try {
          const costPrediction = await predictiveAnalytics.predictCosts(
            validatedItems,
            {
              type: projectContext.type,
              size: projectContext.size.grossSquareFeet,
              location: projectContext.location.city || projectContext.location.zipCode || 'Unknown',
              startDate: new Date(),
              complexity: this.inferComplexity(validatedItems, projectContext)
            },
            companyType || undefined
          )
          
          const timePrediction = await predictiveAnalytics.predictTimeline(
            {
              type: projectContext.type,
              size: projectContext.size.grossSquareFeet,
              location: projectContext.location.city || projectContext.location.zipCode || 'Unknown',
              startDate: new Date(),
              scope: Array.from(new Set(validatedItems.map(item => item.category)))
            },
            validatedItems,
            companyType || undefined
          )
          
          const riskPrediction = await predictiveAnalytics.predictRisks(
            {
              type: projectContext.type,
              size: projectContext.size.grossSquareFeet,
              location: projectContext.location.city || projectContext.location.zipCode || 'Unknown',
              complexity: this.inferComplexity(validatedItems, projectContext)
            },
            validatedItems,
            companyType || undefined
          )
          
          predictions = {
            cost: costPrediction,
            timeline: timePrediction,
            risk: riskPrediction
          }
          
          takeoffLogger.info('Predictive analytics generated', {
            takeoffId,
            predictedCost: costPrediction.estimatedCost,
            confidenceInterval: costPrediction.confidenceInterval,
            estimatedDuration: timePrediction.estimatedDuration,
            overallRiskScore: riskPrediction.overallRiskScore
          })
        } catch (error) {
          takeoffLogger.warn('Failed to generate predictive analytics', { error })
        }
      }

      // Final progress update
      progressCallback?.('Takeoff complete', 100, {
        totalItems: validatedItems.length,
        totalCost: validatedItems.reduce((sum, item) => sum + item.totalCost, 0),
        processingTime: totalTime,
        overallConfidence
      })

      // Add cross-trade metadata to items if needed
      if (isCrossTradeSituation) {
        return {
          items: validatedItems,
          crossTradeInfo: {
            detected: true,
            drawingType: drawingType,
            viewingAs: actualCompanyType,
            message: `This is an ${drawingType} drawing being viewed as a ${actualCompanyType}. Specialty materials have been preserved for reference.`
          },
          predictions
        } as any
      }

      // Return with predictions if available
      if (predictions) {
        return {
          items: validatedItems,
          predictions
        } as any
      }

      return validatedItems
    } catch (error) {
      logError(takeoffLogger, error, 'Enhanced takeoff generation failed', {
        takeoffId,
        projectId: projectContext.id
      })
      throw error
    }
  }
  
  /**
   * Check if file is PDF
   */
  private isPDFFile(file: any): boolean {
    return file.type === 'application/pdf' || 
           file.name.toLowerCase().endsWith('.pdf') ||
           ('mimeType' in file && file.mimeType === 'application/pdf')
  }
  
  /**
   * Process PDF drawing with enhanced analyzer
   */
  private async processPDFDrawing(
    file: any,
    companyType?: CompanyType | null
  ): Promise<PDFAnalysisResult> {
    const buffer = file.buffer || Buffer.from(await file.arrayBuffer())
    return await pdfAnalyzer.analyzePDF(buffer, file.name, companyType)
  }
  
  /**
   * Convert PDF analysis to processed drawing
   */
  private async convertPDFToProcessedDrawing(
    pdfAnalysis: PDFAnalysisResult,
    fileName: string,
    companyType?: CompanyType | null
  ): Promise<ProcessedDrawing> {
    const materials: any[] = []
    
    // First, use the extracted materials from PDF analyzer
    if (pdfAnalysis.extractedMaterials && pdfAnalysis.extractedMaterials.length > 0) {
      takeoffLogger.info('Using AI-extracted materials from PDF', {
        count: pdfAnalysis.extractedMaterials.length,
        fileName
      })
      
      // Convert extracted materials to the format expected by the system
      materials.push(...pdfAnalysis.extractedMaterials.map((m: DetectedMaterial) => ({
        name: m.name,
        type: m.type,
        category: m.category,
        quantity: m.quantity,
        unit: m.unit,
        specifications: m.specifications,
        location: m.location,
        confidence: m.confidence,
        source: 'pdf-text-extraction',
        pageNumber: m.pageNumber
      })))
    }
    
    // Try to analyze images if they're not just placeholders
    if (pdfAnalysis.images.length > 0 && materials.length === 0) {
      for (const image of pdfAnalysis.images) {
        try {
          // Skip if it's a placeholder image
          if (!image.base64.includes('PDF Document Analysis')) {
            const visionResult = await visionService.analyzeDrawing(image.base64, companyType)
            materials.push(...visionResult.materials)
          }
        } catch (error) {
          takeoffLogger.warn('Failed to analyze PDF page image', {
            pageNumber: image.pageNumber,
            error
          })
        }
      }
    }
    
    // If still no materials, extract from text as fallback
    if (materials.length === 0 && pdfAnalysis.textContent) {
      const textMaterials = await this.extractMaterialsFromText(
        pdfAnalysis.textContent,
        pdfAnalysis.drawingInfo.type,
        companyType
      )
      materials.push(...textMaterials)
    }
    
    return {
      id: `pdf-processed-${Date.now()}`,
      fileName,
      pages: pdfAnalysis.images.map((img, idx) => ({
        pageNumber: idx + 1,
        imageUrl: `data:image/png;base64,${img.base64}`,
        analysis: {
          materials: materials.filter(m => m.pageNumber === idx + 1),
          confidence: 0.85,
          processingTime: 0,
          timestamp: new Date(),
          text: [],
          dimensions: [],
          annotations: []
        }
      })),
      materials,
      totalConfidence: 0.85,
      processingTime: 0,
      timestamp: new Date()
    }
  }
  
  /**
   * Process regular image drawing
   */
  private async processImageDrawing(file: any, companyType?: CompanyType | null): Promise<ProcessedDrawing> {
    const fileData = await this.fileToBase64(file)
    
    // Use advanced computer vision analyzer for symbol detection
    const cvResult = await computerVisionAnalyzer.analyzeDrawing(
      fileData,
      companyType || 'General Contractor',
      {
        detectSymbols: true,
        extractMeasurements: true,
        analyzeText: true,
        gridSize: 100
      }
    )
    
    // Also run regular vision analysis for comparison
    const visionResult = await visionService.analyzeDrawing(fileData, companyType)
    
    // Merge results - prefer CV-detected materials but include vision materials not detected by CV
    const cvMaterialNames = new Set(cvResult.materials.map(m => m.name.toLowerCase()))
    const additionalMaterials = visionResult.materials.filter(
      m => !cvMaterialNames.has((m.name || m.description || '').toLowerCase())
    )
    
    const allMaterials = [...cvResult.materials, ...additionalMaterials]
    
    takeoffLogger.info('Computer vision analysis completed', {
      fileName: file.name,
      symbolsDetected: cvResult.symbols.length,
      symbolTypes: Object.keys(cvResult.symbolCounts),
      cvMaterials: cvResult.materials.length,
      visionMaterials: visionResult.materials.length,
      totalMaterials: allMaterials.length,
      measurements: cvResult.measurements.length,
      extractedText: cvResult.text.length
    })
    
    // Log symbol counts for debugging
    if (Object.keys(cvResult.symbolCounts).length > 0) {
      takeoffLogger.info('Symbol counts by type', cvResult.symbolCounts)
    }
    
    return {
      id: `processed-${Date.now()}`,
      fileName: file.name,
      pages: [{
        pageNumber: 1,
        imageUrl: `data:image/png;base64,${fileData}`,
        analysis: {
          ...visionResult,
          materials: allMaterials,
          symbols: cvResult.symbols,
          measurements: cvResult.measurements,
          text: cvResult.text
            .filter(t => t.type !== 'schedule')
            .map(t => ({
              text: t.content,
              confidence: t.confidence,
              location: t.location,
              type: t.type as 'dimension' | 'label' | 'specification' | 'note'
            })),
          lines: cvResult.lines,
          confidence: Math.max(cvResult.confidence, visionResult.confidence)
        }
      }],
      materials: allMaterials,
      totalConfidence: Math.max(cvResult.confidence, visionResult.confidence),
      processingTime: cvResult.processingTime + visionResult.processingTime,
      timestamp: new Date()
    }
  }
  
  /**
   * Extract materials from PDF text content
   */
  private async extractMaterialsFromText(
    textContent: string,
    drawingType: string,
    companyType?: CompanyType | null
  ): Promise<DetectedMaterial[]> {
    const prompt = `Extract construction materials from this ${drawingType} drawing text:

${textContent.substring(0, 3000)}

${companyType ? `Focus on materials relevant to ${companyType}.` : ''}

Identify:
1. Material descriptions with quantities
2. Equipment specifications
3. Any schedules or material lists

Format as JSON array with: name, type, quantity, unit, specifications`
    
    try {
      const response = await geminiService.sendMessage(prompt, 'pdf-text-extraction', {
        companyType
      })
      
      const materials = JSON.parse(response) as DetectedMaterial[]
      return materials.map((m: DetectedMaterial) => ({
        ...m,
        confidence: 0.75, // Lower confidence for text extraction
        source: 'text-extraction'
      }))
    } catch (error) {
      takeoffLogger.warn('Failed to extract materials from PDF text', { error })
      return []
    }
  }
  
  /**
   * Generate materials from project context
   */
  private async generateMaterialsFromContext(
    projectContext: EnhancedProjectContext,
    companyType?: CompanyType | null,
    pdfAnalysis?: PDFAnalysisResult | null
  ): Promise<DetectedMaterial[]> {
    // Get typical materials for this trade and project
    const typicalMaterials = materialFilter.getTypicalMaterials(
      companyType || 'General Contractor',
      projectContext.type,
      projectContext.size?.grossSquareFeet || 10000
    )
    
    // Enhance with AI based on project specifics
    const enhancedPrompt = `Generate detailed material list for:
Project: ${projectContext.name}
Type: ${projectContext.type}
Size: ${projectContext.size?.grossSquareFeet || 'Unknown'} SF
${projectContext.size?.floors ? `Floors: ${projectContext.size.floors}` : ''}
Location: ${projectContext.location.city || projectContext.location.zipCode}
Company Type: ${companyType || 'General Contractor'}

${pdfAnalysis ? `Drawing Type: ${pdfAnalysis.drawingInfo.type}
Scale: ${pdfAnalysis.drawingInfo.scale || 'Unknown'}` : ''}

Base your estimates on these typical materials:
${JSON.stringify(typicalMaterials, null, 2)}

Provide realistic quantities considering the project size and type.
Include ONLY materials relevant to ${companyType || 'General Contractor'}.

${companyType === 'Electrical Contractor' ? `
For electrical contractors, focus on:
- Lighting fixtures and controls (occupancy sensors, dimmers, daylight sensors)
- Electrical panels and distribution equipment
- Conduits and raceways (EMT, PVC)
- Wire and cable (THHN, MC cable)
- Switches, receptacles, and devices
- Emergency and exit lighting
- Low voltage systems

DO NOT include:
- Concrete pads or foundations
- Structural steel
- Masonry or cement
- Any civil/structural work
` : ''}

Format as JSON array with: name, type/category, quantity, unit, specifications.`
    
    try {
      const response = await geminiService.sendMessage(enhancedPrompt, 'material-generation', {
        companyType
      })
      
      const materials = JSON.parse(response) as DetectedMaterial[]
      return materials.map((m: DetectedMaterial) => ({
        ...m,
        confidence: 0.8,
        source: 'ai-generated'
      }))
    } catch (error) {
      // Fallback to typical materials
      return typicalMaterials.map(m => ({
        name: m.description,
        type: m.category,
        category: m.category,
        quantity: m.typicalQuantity,
        unit: m.unit,
        confidence: 0.7,
        source: 'typical-materials'
      }))
    }
  }
  
  /**
   * Convert materials to takeoff items with realistic pricing
   */
  private async convertToTakeoffItems(
    materials: DetectedMaterial[],
    projectContext: EnhancedProjectContext,
    companyType?: CompanyType | null,
    vectorData?: any[]
  ): Promise<TakeoffItem[]> {
    const takeoffItems: TakeoffItem[] = []
    
    if (vectorData && vectorData.length > 0) {
      // Placeholder for logic to process vector data and create takeoff items
      // for linear measurements and area calculations
      takeoffLogger.info('Processing vector data', {
        vectorDataCount: vectorData.length
      })
    }
    
    // Prepare materials for batch pricing
    const pricingRequests = materials.map(material => ({
      description: material.name || material.description || 'Unknown material',
      category: material.type || material.category || 'General',
      unit: material.unit || 'EA',
      quantity: material.quantity || 1
    }))
    
    // Get real-time pricing from API
    let realTimePrices: any[] = []
    try {
      realTimePrices = await pricingIntelligenceAPI.getRealTimePricing(
        pricingRequests,
        projectContext.location,
        companyType || undefined
      )
      
      takeoffLogger.info('Real-time pricing fetched', {
        requestedItems: pricingRequests.length,
        pricedItems: realTimePrices.length,
        location: projectContext.location.city || projectContext.location.zipCode
      })
    } catch (error) {
      takeoffLogger.warn('Failed to get real-time pricing, using fallback', { error })
      realTimePrices = []
    }
    
    // Create a map for quick lookup
    const priceMap = new Map(
      realTimePrices.map(price => [
        `${price.category}-${price.description}`.toLowerCase(),
        price
      ])
    )

    for (let i = 0; i < materials.length; i++) {
      const material = materials[i]
      const category = material.type || material.category || 'General'
      const description = material.name || material.description || 'Unknown material'
      
      // Try to get real-time price first
      const priceKey = `${category}-${description}`.toLowerCase()
      let externalPrice = priceMap.get(priceKey)
      
      let price
      let priceSource: 'rsmeans' | 'market' | 'estimated' = 'estimated'
      
      if (externalPrice) {
        // Use real-time pricing
        price = {
          materialCost: externalPrice.materialCost,
          laborCost: externalPrice.laborCost,
          equipmentCost: externalPrice.equipmentCost,
          totalCost: externalPrice.totalCost
        }
        priceSource = 'market' // Use 'market' for API prices
        
        takeoffLogger.debug('Using real-time pricing', {
          material: description,
          price: price.totalCost,
          confidence: externalPrice.confidence,
          alternatives: externalPrice.alternativePrices?.length || 0
        })
      } else {
        // Fallback to database pricing
        let dbPrice = pricingDatabase.getMaterialPrice(
          description,
          category,
          projectContext.location.city || projectContext.location.zipCode
        )
        
        if (!dbPrice) {
          // Estimate if not found
          dbPrice = pricingDatabase.estimatePrice(
            description,
            category,
            material.unit || 'EA',
            projectContext.location.city || projectContext.location.zipCode
          )
        }
        
        price = {
          materialCost: dbPrice.materialCost,
          laborCost: dbPrice.laborCost,
          equipmentCost: dbPrice.equipmentCost,
          totalCost: dbPrice.totalCost
        }
        priceSource = dbPrice.source
      }
      
      // Validate the price
      const priceValidation = pricingDatabase.validatePrice(
        price.totalCost,
        category,
        material.unit || 'EA'
      )
      
      if (!priceValidation.isValid) {
        takeoffLogger.warn('Price validation failed', {
          material: description,
          price: price.totalCost,
          reason: priceValidation.reason
        })
      }
      
      // Create takeoff item
      const takeoffItem: TakeoffItem = {
        id: `${projectContext.id}-${category}-${Date.now()}-${Math.random()}`,
        category: this.capitalizeFirst(category),
        description,
        quantity: material.quantity || 1,
        unit: material.unit || 'EA',
        unitCost: price.totalCost,
        totalCost: (material.quantity || 1) * price.totalCost,
        aiDetected: true,
        confidence: material.confidence || 0.85,
        location: material.location || 'See drawings',
        specifications: material.specifications,
        materialCost: price.materialCost * (material.quantity || 1),
        laborCost: price.laborCost * (material.quantity || 1),
        equipmentCost: price.equipmentCost * (material.quantity || 1),
        priceSource: priceSource
      }
      
      // Add supplier information if available
      if (externalPrice?.alternativePrices && externalPrice.alternativePrices.length > 0) {
        const bestPrice = externalPrice.alternativePrices.reduce((best: any, current: any) => 
          current.price < best.price ? current : best
        )
        
        takeoffItem.supplier = bestPrice.supplier
        takeoffItem.leadTime = bestPrice.leadTime
        takeoffItem.notes = `Best price from ${bestPrice.supplier} (${bestPrice.availability})`
      }
      
      takeoffItems.push(takeoffItem)
    }
    
    // Apply contextual validation to filter out misclassified items
    const validatedItems: TakeoffItem[] = []
    
    for (const item of takeoffItems) {
      const contextValidation = await materialFilter.validateMaterialContext(
        item,
        companyType || 'General Contractor',
        {
          drawingType: projectContext.specifications?.drawingType,
          projectType: projectContext.type
        }
      )
      
      if (contextValidation.isValid) {
        validatedItems.push(item)
      } else {
        takeoffLogger.warn('Material failed contextual validation', {
          material: item.description,
          category: item.category,
          reason: contextValidation.reason,
          suggestedCategory: contextValidation.suggestedCategory
        })
      }
    }
    
    takeoffLogger.info('Applied contextual validation', {
      originalCount: takeoffItems.length,
      validatedCount: validatedItems.length,
      filteredOut: takeoffItems.length - validatedItems.length
    })
    
    return validatedItems
  }
  
  /**
   * Calculate item confidence score
   */
  private calculateItemConfidence(
    item: TakeoffItem,
    validation?: any
  ): ItemConfidence {
    const baseConfidence = item.confidence || 0.5
    const quantityConfidence = validation?.confidence || 1.0
    const pricingConfidence = item.priceSource === 'rsmeans' ? 0.95 : 0.8
    const detectionConfidence = item.aiDetected ? 0.85 : 1.0
    const validationConfidence = validation?.isValid ? 1.0 : 0.7
    
    const overall = (
      baseConfidence * 0.3 +
      quantityConfidence * 0.25 +
      pricingConfidence * 0.2 +
      detectionConfidence * 0.15 +
      validationConfidence * 0.1
    )
    
    return {
      overall: Math.round(overall * 100) / 100,
      quantity: quantityConfidence,
      pricing: pricingConfidence,
      detection: detectionConfidence,
      validation: validationConfidence
    }
  }
  
  /**
   * Add missing items based on trade requirements
   */
  private async addMissingItems(
    items: TakeoffItem[],
    projectContext: EnhancedProjectContext,
    companyType?: CompanyType | null
  ): Promise<TakeoffItem[]> {
    if (!companyType) return items
    
    const suggestions = materialFilter.suggestMissingMaterials(
      items,
      companyType,
      projectContext.type
    )
    
    if (suggestions.length === 0) return items
    
    // Generate quantities for missing items
    const prompt = `For a ${projectContext.type} project of ${projectContext.size?.grossSquareFeet || 10000} SF,
estimate quantities for these missing ${companyType} items:
${suggestions.join('\n')}

Format as JSON array with: description, quantity, unit, category`
    
    try {
      const response = await geminiService.sendMessage(prompt, 'missing-items', {
        companyType
      })
      
      const missingItems = JSON.parse(response) as DetectedMaterial[]
      const newItems = await this.convertToTakeoffItems(
        missingItems.map((item: DetectedMaterial) => ({
          ...item,
          name: item.description || item.name,
          type: item.category,
          confidence: 0.75,
          source: 'ai-suggested'
        })),
        projectContext,
        companyType
      )
      
      return [...items, ...newItems]
    } catch (error) {
      takeoffLogger.warn('Failed to generate missing items', { error })
      return items
    }
  }
  
  /**
   * Validate costs are reasonable for project size
   */
  private validateCosts(
    items: TakeoffItem[],
    projectContext: EnhancedProjectContext
  ): TakeoffItem[] {
    const totalCost = items.reduce((sum, item) => sum + item.totalCost, 0)
    const costPerSF = totalCost / (projectContext.size?.grossSquareFeet || 10000)
    
    // Typical cost ranges per SF by project type
    const costRanges: Record<string, { min: number; max: number }> = {
      commercial: { min: 150, max: 400 },
      residential: { min: 100, max: 250 },
      industrial: { min: 80, max: 200 },
      institutional: { min: 200, max: 500 }
    }
    
    const range = costRanges[projectContext.type]
    
    if (costPerSF < range.min || costPerSF > range.max) {
      takeoffLogger.warn('Total cost outside typical range', {
        totalCost,
        costPerSF,
        expectedRange: range,
        projectType: projectContext.type
      })
      
      // Apply adjustment factor if way off
      if (costPerSF > range.max * 2) {
        const factor = (range.max * 1.5) / costPerSF
        return items.map(item => ({
          ...item,
          unitCost: item.unitCost * factor,
          totalCost: item.totalCost * factor,
          notes: `Price adjusted - original seemed high for project type`
        }))
      }
    }
    
    return items
  }
  
  /**
   * Helper to convert file to base64
   */
  private async fileToBase64(file: any): Promise<string> {
    if (typeof window === 'undefined') {
      if (file.buffer) {
        const base64 = file.buffer.toString('base64')
        return base64.startsWith('data:') ? base64.split(',')[1] : base64
      }
      if (file.arrayBuffer) {
        const arrayBuffer = await file.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)
        return buffer.toString('base64')
      }
      throw new Error('Unable to convert file to base64')
    } else {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          const result = reader.result as string
          resolve(result.split(',')[1])
        }
        reader.onerror = reject
      })
    }
  }
  
  /**
   * Capitalize first letter
   */
  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
  }
  
  /**
   * Infer project complexity from takeoff items
   */
  private inferComplexity(
    items: TakeoffItem[],
    projectContext: EnhancedProjectContext
  ): 'simple' | 'standard' | 'complex' | 'highly-complex' {
    // Factors for complexity
    let complexityScore = 0
    
    // Item count factor
    if (items.length > 200) complexityScore += 30
    else if (items.length > 100) complexityScore += 20
    else if (items.length > 50) complexityScore += 10
    
    // High-value items factor
    const highValueItems = items.filter(item => item.totalCost > 50000).length
    if (highValueItems > 10) complexityScore += 20
    else if (highValueItems > 5) complexityScore += 10
    
    // Trade diversity factor
    const uniqueCategories = new Set(items.map(item => item.category)).size
    if (uniqueCategories > 10) complexityScore += 20
    else if (uniqueCategories > 6) complexityScore += 10
    
    // Size factor
    const size = projectContext.size?.grossSquareFeet || 0
    if (size > 100000) complexityScore += 20
    else if (size > 50000) complexityScore += 10
    
    // Specialty work factor
    const hasSpecialtyWork = items.some(item => 
      ['Electrical', 'Plumbing', 'HVAC', 'Fire Protection'].includes(item.category) &&
      item.totalCost > 100000
    )
    if (hasSpecialtyWork) complexityScore += 15
    
    // Project type factor
    if (projectContext.type === 'industrial' || projectContext.type === 'institutional') {
      complexityScore += 10
    }
    
    // Determine complexity level
    if (complexityScore >= 70) return 'highly-complex'
    if (complexityScore >= 50) return 'complex'
    if (complexityScore >= 25) return 'standard'
    return 'simple'
  }
}

// Export singleton instance
export const enhancedTakeoffService = new EnhancedTakeoffService()