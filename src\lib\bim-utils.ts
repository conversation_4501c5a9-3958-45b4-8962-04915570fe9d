import { BIMModel, BIMElement } from '@/types'
import * as THREE from 'three'

interface Point3D {
  x: number
  y: number
  z: number
}

interface BoundingBox {
  min: Point3D
  max: Point3D
  center: Point3D
  size: Point3D
}

interface ModelStatistics {
  totalElements: number
  elementsByType: Map<string, number>
  totalVolume: number
  totalArea: number
  boundingBox: BoundingBox
  averageProgress: number
  completedElements: number
  inProgressElements: number
  notStartedElements: number
}

// Bounding Box Calculations
export function calculateBoundingBox(elements: BIMElement[]): BoundingBox {
  if (elements.length === 0) {
    return {
      min: { x: 0, y: 0, z: 0 },
      max: { x: 0, y: 0, z: 0 },
      center: { x: 0, y: 0, z: 0 },
      size: { x: 0, y: 0, z: 0 },
    }
  }

  const min = { x: Infinity, y: Infinity, z: Infinity }
  const max = { x: -Infinity, y: -Infinity, z: -Infinity }

  elements.forEach(element => {
    const pos = element.position || { x: 0, y: 0, z: 0 }
    const props = element.properties || {}
    
    // Consider element dimensions
    const halfWidth = (props.width || 0) / 2
    const halfLength = (props.length || 0) / 2
    const halfHeight = (props.height || 0) / 2

    min.x = Math.min(min.x, pos.x - halfLength)
    min.y = Math.min(min.y, pos.y)
    min.z = Math.min(min.z, pos.z - halfWidth)
    
    max.x = Math.max(max.x, pos.x + halfLength)
    max.y = Math.max(max.y, pos.y + (props.height || 0))
    max.z = Math.max(max.z, pos.z + halfWidth)
  })

  const center = {
    x: (min.x + max.x) / 2,
    y: (min.y + max.y) / 2,
    z: (min.z + max.z) / 2,
  }

  const size = {
    x: max.x - min.x,
    y: max.y - min.y,
    z: max.z - min.z,
  }

  return { min, max, center, size }
}

// Element Filtering
export function getElementsByType(
  elements: BIMElement[],
  types: string | string[]
): BIMElement[] {
  const typeArray = Array.isArray(types) ? types : [types]
  return elements.filter(element => 
    element.ifcType && typeArray.includes(element.ifcType)
  )
}

export function getElementChildren(
  elementId: string,
  elements: BIMElement[],
  recursive = false
): BIMElement[] {
  const children: BIMElement[] = []
  
  elements.forEach(element => {
    if (element.parent === elementId) {
      children.push(element)
      if (recursive) {
        children.push(...getElementChildren(element.id, elements, true))
      }
    }
  })
  
  return children
}

export function getElementParents(
  elementId: string,
  elements: BIMElement[],
  recursive = false
): BIMElement[] {
  const element = elements.find(e => e.id === elementId)
  if (!element || !element.parent) return []
  
  const parent = elements.find(e => e.id === element.parent)
  if (!parent) return []
  
  const parents = [parent]
  
  if (recursive) {
    parents.push(...getElementParents(parent.id, elements, true))
  }
  
  return parents
}

export function filterVisibleElements(
  elements: BIMElement[],
  hiddenIds: string[]
): BIMElement[] {
  if (!hiddenIds || hiddenIds.length === 0) return elements
  return elements.filter(element => !hiddenIds.includes(element.id))
}

// Progress Calculations
export function calculateElementProgress(
  elements: BIMElement[],
  weighted = false
): number {
  if (elements.length === 0) return 0
  
  if (!weighted) {
    const totalProgress = elements.reduce((sum, element) => 
      sum + (element.progress || 0), 0
    )
    return totalProgress / elements.length
  }
  
  // Weighted by volume
  let totalWeightedProgress = 0
  let totalVolume = 0
  
  elements.forEach(element => {
    const volume = calculateElementVolume(element)
    totalVolume += volume
    totalWeightedProgress += (element.progress || 0) * volume
  })
  
  return totalVolume > 0 ? totalWeightedProgress / totalVolume : 0
}

function calculateElementVolume(element: BIMElement): number {
  const props = element.properties || {}
  
  if (element.ifcType === 'IfcColumn' && props.radius && props.height) {
    return Math.PI * props.radius * props.radius * props.height
  }
  
  if (props.length && props.width && props.height) {
    return props.length * props.width * props.height
  }
  
  // Default volume
  return 1000
}

// Grouping Functions
export function groupElementsByFloor(elements: BIMElement[]): Map<number, BIMElement[]> {
  const grouped = new Map<number, BIMElement[]>()
  
  elements.forEach(element => {
    const floor = element.floor ?? -1
    if (!grouped.has(floor)) {
      grouped.set(floor, [])
    }
    grouped.get(floor)!.push(element)
  })
  
  return grouped
}

// Model Statistics
export function calculateModelStatistics(model: BIMModel): ModelStatistics {
  const elements = model.elements || []
  
  const elementsByType = new Map<string, number>()
  let totalVolume = 0
  let totalArea = 0
  let completedElements = 0
  let inProgressElements = 0
  let notStartedElements = 0
  
  elements.forEach(element => {
    // Count by type
    const type = element.ifcType || element.type
    elementsByType.set(type, (elementsByType.get(type) || 0) + 1)
    
    // Calculate volumes
    totalVolume += calculateElementVolume(element)
    
    // Calculate areas (simplified)
    const props = element.properties || {}
    if (props.area) {
      totalArea += props.area
    } else if (props.length && props.width) {
      totalArea += props.length * props.width
    }
    
    // Count by progress
    const progress = element.progress || 0
    if (progress === 100) completedElements++
    else if (progress > 0) inProgressElements++
    else notStartedElements++
  })
  
  return {
    totalElements: elements.length,
    elementsByType,
    totalVolume,
    totalArea,
    boundingBox: calculateBoundingBox(elements),
    averageProgress: calculateElementProgress(elements),
    completedElements,
    inProgressElements,
    notStartedElements,
  }
}

// Coordinate Conversions
export function convertCoordinates(
  point: Point3D,
  from: 'world' | 'local',
  to: 'world' | 'local',
  transform?: THREE.Matrix4
): Point3D {
  if (from === to) return point
  
  const vec = new THREE.Vector3(point.x, point.y, point.z)
  
  if (from === 'local' && to === 'world' && transform) {
    vec.applyMatrix4(transform)
  } else if (from === 'world' && to === 'local' && transform) {
    const inverse = transform.clone().invert()
    vec.applyMatrix4(inverse)
  }
  
  return { x: vec.x, y: vec.y, z: vec.z }
}

// 3D Math Utilities
export function calculateDistance3D(p1: Point3D, p2: Point3D): number {
  const dx = p2.x - p1.x
  const dy = p2.y - p1.y
  const dz = p2.z - p1.z
  return Math.sqrt(dx * dx + dy * dy + dz * dz)
}

export function calculateAngle3D(v1: Point3D, v2: Point3D): number {
  const dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z
  const len1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z)
  const len2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y + v2.z * v2.z)
  
  if (len1 === 0 || len2 === 0) return 0
  
  const cos = dot / (len1 * len2)
  const angle = Math.acos(Math.max(-1, Math.min(1, cos)))
  return angle * 180 / Math.PI
}

export function normalizeVector(v: Point3D): Point3D {
  const length = Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z)
  if (length === 0) return { x: 0, y: 0, z: 0 }
  return {
    x: v.x / length,
    y: v.y / length,
    z: v.z / length,
  }
}

// Camera Utilities
export function getCameraViewMatrix(
  position: Point3D,
  target: Point3D,
  up: Point3D = { x: 0, y: 1, z: 0 }
): THREE.Matrix4 {
  const matrix = new THREE.Matrix4()
  matrix.lookAt(
    new THREE.Vector3(position.x, position.y, position.z),
    new THREE.Vector3(target.x, target.y, target.z),
    new THREE.Vector3(up.x, up.y, up.z)
  )
  return matrix
}

export function projectToScreen(
  point: Point3D,
  camera: THREE.Camera,
  screenSize: { width: number; height: number }
): { x: number; y: number } {
  const vec = new THREE.Vector3(point.x, point.y, point.z)
  vec.project(camera)
  
  return {
    x: (vec.x + 1) * screenSize.width / 2,
    y: (-vec.y + 1) * screenSize.height / 2,
  }
}

export function unprojectFromScreen(
  screenPoint: { x: number; y: number },
  camera: THREE.Camera,
  screenSize: { width: number; height: number },
  depth = 0
): { origin: Point3D; direction: Point3D } {
  const x = (screenPoint.x / screenSize.width) * 2 - 1
  const y = -(screenPoint.y / screenSize.height) * 2 + 1
  
  const vec = new THREE.Vector3(x, y, depth)
  vec.unproject(camera)
  
  const origin = {
    x: camera.position.x,
    y: camera.position.y,
    z: camera.position.z,
  }
  
  const direction = normalizeVector({
    x: vec.x - origin.x,
    y: vec.y - origin.y,
    z: vec.z - origin.z,
  })
  
  return { origin, direction }
}

// Raycasting
export function raycastElements(
  ray: { origin: Point3D; direction: Point3D },
  elements: BIMElement[],
  maxDistance = Infinity
): { element: BIMElement; distance: number; point: Point3D }[] {
  const results: { element: BIMElement; distance: number; point: Point3D }[] = []
  
  // Simplified raycasting - in real implementation would use Three.js
  elements.forEach(element => {
    if (!element.position) return
    
    const distance = calculateDistance3D(ray.origin, element.position)
    if (distance <= maxDistance) {
      results.push({
        element,
        distance,
        point: element.position,
      })
    }
  })
  
  return results.sort((a, b) => a.distance - b.distance)
}

// Element Path
export function generateElementPath(
  elementId: string,
  elements: BIMElement[]
): string[] {
  const path: string[] = []
  let current = elements.find(e => e.id === elementId)
  
  while (current) {
    path.unshift(current.id)
    current = current.parent 
      ? elements.find(e => e.id === current!.parent)
      : undefined
  }
  
  return path
}

// Annotation Utilities
export function mergeAnnotations(
  local: any[],
  remote: any[]
): any[] {
  const merged = new Map()
  
  // Add local annotations
  local.forEach(ann => merged.set(ann.id, ann))
  
  // Override with remote annotations (remote wins conflicts)
  remote.forEach(ann => merged.set(ann.id, ann))
  
  return Array.from(merged.values())
}

export function validateAnnotationPosition(
  position: Point3D,
  bounds: BoundingBox
): boolean {
  return (
    position.x >= bounds.min.x &&
    position.x <= bounds.max.x &&
    position.y >= bounds.min.y &&
    position.y <= bounds.max.y &&
    position.z >= bounds.min.z &&
    position.z <= bounds.max.z
  )
}

// Measurement Formatting
export function formatMeasurement(
  value: number,
  unit: string,
  precision = 1,
  targetUnit?: string
): string {
  let convertedValue = value
  let displayUnit = unit
  
  // Simple unit conversion
  if (targetUnit && unit !== targetUnit) {
    if (unit === 'mm' && targetUnit === 'm') {
      convertedValue = value / 1000
      displayUnit = targetUnit
    } else if (unit === 'm' && targetUnit === 'mm') {
      convertedValue = value * 1000
      displayUnit = targetUnit
    }
  }
  
  return `${convertedValue.toFixed(precision)} ${displayUnit}`
}

// IFC Property Parsing
export function parseIFCProperties(ifcData: any): Record<string, any> {
  const properties: Record<string, any> = {}
  
  function flatten(obj: any, prefix = ''): void {
    Object.entries(obj).forEach(([key, value]) => {
      const fullKey = prefix ? `${prefix}.${key}` : key
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        flatten(value, fullKey)
      } else {
        // Skip property set prefixes
        const cleanKey = key.startsWith('Pset_') ? key : fullKey
        properties[cleanKey] = value
      }
    })
  }
  
  // Flatten property sets
  Object.entries(ifcData).forEach(([psetName, pset]) => {
    if (psetName.startsWith('Pset_') && typeof pset === 'object') {
      flatten(pset)
    } else {
      flatten({ [psetName]: pset })
    }
  })
  
  return properties
}