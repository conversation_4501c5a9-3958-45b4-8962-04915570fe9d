# Fix Missing Google Generative AI Package
# This script installs the missing @google/generative-ai package

Write-Host "=== Fixing Missing @google/generative-ai Package ===" -ForegroundColor Green
Write-Host ""

# Check if npm is available
try {
    $npmVersion = npm --version
    Write-Host "✓ npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ npm not found. Please install Node.js first." -ForegroundColor Red
    exit 1
}

# Navigate to project root
$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot
Write-Host "✓ Working directory: $projectRoot" -ForegroundColor Green
Write-Host ""

# Check if package.json exists
if (!(Test-Path "package.json")) {
    Write-Host "✗ package.json not found in project root" -ForegroundColor Red
    exit 1
}

Write-Host "Installing @google/generative-ai package..." -ForegroundColor Yellow
Write-Host ""

# Install the specific package
try {
    npm install @google/generative-ai@0.21.0
    Write-Host ""
    Write-Host "✓ Package installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to install package" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Verifying Installation ===" -ForegroundColor Green

# Check if the package exists in node_modules
if (Test-Path "node_modules/@google/generative-ai/package.json") {
    $packageJson = Get-Content "node_modules/@google/generative-ai/package.json" | ConvertFrom-Json
    Write-Host "✓ @google/generative-ai version $($packageJson.version) is installed" -ForegroundColor Green
} else {
    Write-Host "✗ Package not found in node_modules" -ForegroundColor Red
    Write-Host "Try running: npm install" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Cyan
Write-Host "1. Restart your development server: npm run dev" -ForegroundColor White
Write-Host "2. Hard refresh your browser: Ctrl+Shift+R" -ForegroundColor White
Write-Host ""
Write-Host "✓ Fix completed!" -ForegroundColor Green
