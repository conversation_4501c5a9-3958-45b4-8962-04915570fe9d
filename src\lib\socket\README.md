# Socket.io Real-time Collaboration for BIM Viewer

This module provides comprehensive real-time collaboration features for the BIM viewer using Socket.io.

## Features

- **User Presence**: See who's viewing the model in real-time
- **3D Cursor Tracking**: Track other users' cursor positions in 3D space
- **Element Selection Sync**: See what elements other users have selected
- **Annotations**: Create, update, and reply to annotations on model elements
- **Model Changes Broadcast**: Sync model modifications across all connected users
- **View State Sync**: Share camera positions and view settings

## Architecture

### Server-Side
- `server.ts`: Socket.io server implementation with room management
- `socket-handler.ts`: Next.js API route handler for WebSocket connections
- Supports multiple collaboration rooms (one per project/model)

### Client-Side
- `client.ts`: Socket.io client service with reconnection handling
- `hooks.ts`: React hooks for easy integration
- `components/`: Pre-built UI components for collaboration features

### Type Safety
- `types.ts`: Comprehensive TypeScript types for all events and data structures
- Full type safety for client-server communication

## Usage

### 1. Initialize Socket.io Server

Socket.io is automatically initialized when the server starts. The WebSocket handler is integrated into the Next.js server lifecycle. No additional setup is required.

### 2. Wrap Your BIM Viewer with CollaborationProvider

```tsx
import { CollaborationProvider } from '@/lib/socket/components'

function BIMViewerPage() {
  const userId = 'user123' // From auth context
  const userName = 'John Doe'
  const userRole = 'engineer'
  const roomId = `${projectId}_${modelId}`

  return (
    <CollaborationProvider
      roomId={roomId}
      userId={userId}
      userName={userName}
      userRole={userRole}
    >
      <YourBIMViewer />
      <CollaborationToolbar />
    </CollaborationProvider>
  )
}
```

### 3. Use Collaboration Features in Your Components

```tsx
import { useCollaboration } from '@/lib/socket/components'
import { CursorsContainer, AnnotationsContainer } from '@/lib/socket/components'

function BIMViewer() {
  const {
    cursors,
    annotations,
    updateCursor,
    createAnnotation
  } = useCollaboration()

  return (
    <Canvas>
      {/* Your 3D model */}
      <YourModel />
      
      {/* Collaboration visualizations */}
      <CursorsContainer cursors={cursors} users={users} visible={true} />
      <AnnotationsContainer annotations={annotations} onUpdate={updateAnnotation} />
    </Canvas>
  )
}
```

## Event Types

### User Presence Events
- `USER_JOINED`: New user joined the collaboration session
- `USER_LEFT`: User left the session
- `USER_PRESENCE_UPDATE`: User status changed

### Interaction Events
- `CURSOR_MOVE`: 3D cursor position update
- `ELEMENT_SELECT`: Element(s) selected
- `ELEMENT_DESELECT`: Element(s) deselected

### Annotation Events
- `ANNOTATION_CREATE`: New annotation created
- `ANNOTATION_UPDATE`: Annotation modified
- `ANNOTATION_DELETE`: Annotation removed
- `ANNOTATION_REPLY`: Reply added to annotation

### Model Events
- `MODEL_CHANGE`: Model element modified
- `MODEL_SYNC`: Batch model updates
- `VIEW_STATE_CHANGE`: Camera/view settings changed

## Components

### CollaborationToolbar
Displays active users, connection status, and collaboration options.

### CursorVisualization
Renders 3D cursors for other users with name labels.

### AnnotationMarker
Interactive 3D annotations with discussion threads.

### SelectionHighlight
Visual highlighting of selected elements by other users.

## Performance Considerations

- Cursor updates are throttled to 50ms intervals
- View state changes are debounced
- Large model changes are batched
- Automatic reconnection with exponential backoff

## Environment Variables

```env
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000  # Socket.io server URL
```

## Example Integration

See `example-integration.tsx` for a complete example of integrating all collaboration features into a BIM viewer.