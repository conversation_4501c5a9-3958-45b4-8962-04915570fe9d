# BIM Viewer User Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Interface Overview](#interface-overview)
4. [Navigation Controls](#navigation-controls)
5. [Model Management](#model-management)
6. [Viewing Options](#viewing-options)
7. [Measurement Tools](#measurement-tools)
8. [Annotations & Markup](#annotations--markup)
9. [Collaboration Features](#collaboration-features)
10. [Progress Tracking](#progress-tracking)
11. [Clash Detection](#clash-detection)
12. [Keyboard Shortcuts](#keyboard-shortcuts)
13. [Troubleshooting](#troubleshooting)

## Introduction

The BIM Viewer is a powerful 3D visualization tool integrated into the AI Construction Management platform. It allows you to view, navigate, and interact with Building Information Models (BIM) directly in your web browser, enabling better project understanding, collaboration, and decision-making.

### Key Features

- **Web-based 3D Visualization**: No software installation required
- **Multi-format Support**: IFC, Revit, AutoCAD, and more
- **Real-time Collaboration**: Share views and annotations with team members
- **Progress Tracking**: Compare as-built vs. as-planned
- **AI Integration**: Intelligent insights and recommendations
- **Performance Optimized**: Handles large models smoothly

## Getting Started

### Accessing the BIM Viewer

1. **From Project Dashboard**
   - Navigate to your project
   - Click on the "BIM Viewer" tab
   - Select the model you want to view

2. **From Documents**
   - Go to Documents section
   - Find your BIM file
   - Click "Open in BIM Viewer"

### First Time Setup

When you first open the BIM Viewer, you'll see:

```
┌─────────────────────────────────────────────────────┐
│  Toolbar                                    [User]  │
├─────────────┬───────────────────────────────────────┤
│             │                                       │
│   Model     │                                       │
│   Tree      │         3D Viewport                   │
│             │                                       │
│   Layers    │                                       │
│             │                                       │
├─────────────┴───────────────────────────────────────┤
│  Properties Panel              Timeline/Progress    │
└─────────────────────────────────────────────────────┘
```

## Interface Overview

### Main Components

#### 1. Toolbar
Located at the top, contains:
- **File Operations**: Open, Save View, Export
- **View Controls**: Home, Fit to Screen, View Presets
- **Tools**: Select, Measure, Section, Annotate
- **Display Options**: Render mode, Transparency, Shadows
- **Collaboration**: Share, Comments, Live Session

#### 2. Model Tree
Left sidebar showing:
- Building hierarchy (Site > Building > Floor > Room)
- Component categories (Structural, MEP, Architecture)
- Search and filter options
- Visibility toggles

#### 3. 3D Viewport
Central area for model display:
- Interactive 3D view
- Context menu on right-click
- Selection highlighting
- Real-time rendering

#### 4. Properties Panel
Bottom panel displaying:
- Selected element properties
- Dimensions and specifications
- Material information
- Associated documents

## Navigation Controls

### Mouse Controls

| Action | Control |
|--------|---------|
| **Orbit** | Left-click + drag |
| **Pan** | Middle-click + drag OR Shift + left-click + drag |
| **Zoom** | Scroll wheel OR Right-click + drag |
| **Select** | Single left-click |
| **Multi-select** | Ctrl + left-click |
| **Box select** | Left-click + drag (with Select tool) |

### Touch Controls (Tablet/Mobile)

| Action | Gesture |
|--------|---------|
| **Orbit** | One finger drag |
| **Pan** | Two finger drag |
| **Zoom** | Pinch in/out |
| **Select** | Single tap |

### Navigation Cube

Click on the ViewCube faces to quickly orient the model:
- **Faces**: Front, Back, Left, Right, Top, Bottom
- **Edges**: Click to view at 45° angles
- **Corners**: Click for isometric views
- **Home**: Return to default view

## Model Management

### Loading Models

1. **Upload New Model**
   ```
   Click [+] Upload Model
   Select file (IFC, RVT, DWG)
   Wait for processing
   Model appears in viewer
   ```

2. **Model Formats Supported**
   - IFC (2x3, 4)
   - Revit (RVT, RFA)
   - AutoCAD (DWG, DXF)
   - SketchUp (SKP)
   - STEP/IGES
   - OBJ, FBX

### Model Organization

#### Federated Models
Combine multiple models:
1. Load primary model (e.g., Architecture)
2. Click "Add Model" 
3. Select additional models (Structure, MEP)
4. Adjust alignment if needed
5. Save federated view

#### Version Control
- Track model versions automatically
- Compare versions side-by-side
- View revision history
- Restore previous versions

## Viewing Options

### Display Modes

1. **Shaded**: Default realistic view
2. **Wireframe**: See through objects
3. **X-Ray**: Transparent with edges
4. **Realistic**: With textures and materials
5. **Technical**: Black and white line drawing

### Visibility Controls

#### By Category
Toggle visibility of:
- Structural elements
- MEP systems
- Architectural components
- Site elements
- Furniture and fixtures

#### By Level
- Show/hide specific floors
- Isolate single level
- View multiple levels with transparency

#### Smart Visibility
AI-powered visibility suggestions:
- "Show only structural elements"
- "Hide all furniture"
- "Isolate HVAC systems"

### Section Views

1. **Section Box**
   - Click Section tool
   - Drag handles to adjust box
   - Double-click face to align view

2. **Section Planes**
   - Create horizontal/vertical cuts
   - Save section views
   - Animate through building

3. **Floor Plans**
   - Auto-generate from 3D model
   - Adjust cut height
   - Export as 2D drawings

## Measurement Tools

### Distance Measurement

1. Select Measure tool (ruler icon)
2. Click first point
3. Click second point
4. Measurement appears with:
   - Direct distance
   - X, Y, Z components
   - Angle from horizontal

### Area Measurement

1. Select Area tool
2. Click to define polygon vertices
3. Double-click to complete
4. Shows:
   - Total area
   - Perimeter
   - Individual edge lengths

### Volume Calculation

1. Select element(s)
2. Right-click > Calculate Volume
3. View in Properties panel:
   - Gross volume
   - Net volume
   - Weight (if density defined)

### Advanced Measurements

- **Angle**: Measure between faces/edges
- **Radius**: For curved elements
- **Clearance**: Minimum distance between objects
- **Count**: Number of selected elements

## Annotations & Markup

### Creating Annotations

1. **3D Labels**
   - Click Annotate tool
   - Click on model location
   - Type annotation text
   - Adjust leader line

2. **2D Markup**
   - Draw directly on view
   - Shapes: Arrow, Circle, Rectangle
   - Freehand sketching
   - Text with formatting

### Annotation Management

- **Categories**: RFI, Issue, Comment, Approval
- **Status**: Open, In Progress, Resolved
- **Assignment**: Assign to team members
- **Attachments**: Add photos, documents

### BCF Integration

Export/import annotations as BCF (BIM Collaboration Format):
- Industry-standard format
- Preserve viewpoints
- Include screenshots
- Track resolution status

## Collaboration Features

### View Sharing

1. **Save Viewpoint**
   - Position model as desired
   - Click Save View
   - Name and describe
   - Share link with team

2. **Viewpoint Contents**
   - Camera position
   - Visible elements
   - Section planes
   - Annotations
   - Display settings

### Live Sessions

1. **Start Session**
   - Click "Start Live Session"
   - Share session code
   - Team members join

2. **During Session**
   - Synchronized navigation
   - Shared cursor/pointer
   - Voice chat integration
   - Screen annotation

3. **Permissions**
   - Presenter: Full control
   - Participants: View and annotate
   - Moderator: Manage users

### Comments & Discussions

- **Contextual Comments**: Attached to model elements
- **Threading**: Reply to create discussions
- **Notifications**: Email/app alerts
- **History**: Full audit trail

## Progress Tracking

### 4D Simulation

1. **Timeline Setup**
   - Link model elements to schedule
   - Define construction sequence
   - Set element appearance by status

2. **Playback Controls**
   ```
   [<<] [<] [||] [>] [>>]  [Timeline Slider]  Date: MM/DD/YYYY
   ```

3. **Status Colors**
   - Gray: Not started
   - Blue: In progress
   - Green: Completed
   - Red: Delayed
   - Yellow: Ahead of schedule

### Progress Comparison

1. **Planned vs. Actual**
   - Load baseline model
   - Import progress data
   - Visual deviation analysis
   - Generate reports

2. **Data Sources**
   - Manual updates
   - IoT sensors
   - Drone/photo capture
   - AI-powered analysis

### KPI Dashboard

View real-time metrics:
- Percentage complete by trade
- Volume of work in place
- Deviation from plan
- Productivity rates

## Clash Detection

### Running Clash Tests

1. **Setup Rules**
   - Select disciplines to check
   - Define tolerance (e.g., 1 inch)
   - Set priority levels

2. **Run Analysis**
   ```
   Clash Detection
   ├── Hard Clashes (Physical interference)
   ├── Soft Clashes (Clearance violations)
   └── Workflow Clashes (Sequence issues)
   ```

3. **Review Results**
   - Navigate through clashes
   - Assign to responsible party
   - Track resolution status
   - Generate reports

### Clash Groups

Organize similar clashes:
- By location
- By discipline
- By severity
- By assigned user

### AI-Powered Insights

- Predict likely clash locations
- Suggest resolution strategies
- Identify patterns
- Prevent future issues

## Keyboard Shortcuts

### Navigation

| Key | Action |
|-----|--------|
| **W** | Move forward |
| **S** | Move backward |
| **A** | Move left |
| **D** | Move right |
| **Q** | Move up |
| **E** | Move down |
| **Space** | Reset view |
| **F** | Fit to screen |

### Selection

| Key | Action |
|-----|--------|
| **Esc** | Clear selection |
| **Ctrl+A** | Select all visible |
| **H** | Hide selected |
| **Shift+H** | Isolate selected |
| **Alt+H** | Show all |

### Tools

| Key | Action |
|-----|--------|
| **M** | Measure tool |
| **N** | Annotation tool |
| **X** | Section box |
| **C** | Clash detection |
| **V** | Change view mode |

### View Management

| Key | Action |
|-----|--------|
| **Ctrl+S** | Save viewpoint |
| **Ctrl+O** | Open model |
| **Ctrl+E** | Export view |
| **F11** | Fullscreen |

## Troubleshooting

### Performance Issues

**Model loads slowly:**
- Check internet connection
- Clear browser cache
- Reduce model complexity
- Enable hardware acceleration

**Laggy navigation:**
1. Lower display quality (Settings > Performance)
2. Hide unnecessary elements
3. Use simplified view mode
4. Close other browser tabs

### Display Problems

**Model appears black:**
- Check lighting settings
- Verify material definitions
- Try different display mode

**Missing elements:**
- Check visibility settings
- Verify model upload completed
- Review filter settings

### Browser Compatibility

**Recommended Browsers:**
- Chrome (latest)
- Edge (Chromium-based)
- Firefox (latest)
- Safari 14+

**Minimum Requirements:**
- WebGL 2.0 support
- 4GB RAM
- Dedicated graphics recommended

### Common Issues

1. **Cannot select elements**
   - Ensure Select tool is active
   - Check if element is hidden
   - Verify layer is unlocked

2. **Measurements incorrect**
   - Verify model units
   - Check project settings
   - Calibrate if needed

3. **Annotations not saving**
   - Check permissions
   - Ensure logged in
   - Verify project access

## Best Practices

### Model Optimization

1. **Before Upload**
   - Remove unnecessary detail
   - Purge unused elements
   - Optimize file size
   - Verify coordinate system

2. **Organization**
   - Use consistent naming
   - Organize by discipline
   - Maintain model hierarchy
   - Document changes

### Collaboration Tips

1. **Communication**
   - Use clear annotation titles
   - Assign to specific people
   - Set due dates
   - Follow up on issues

2. **View Sharing**
   - Save meaningful viewpoints
   - Include descriptions
   - Update as needed
   - Archive completed items

### Security & Privacy

- Models encrypted in transit
- Access control by project
- Audit trail of all actions
- GDPR compliant
- Regular backups

## Advanced Features

### API Integration

Connect BIM Viewer to other tools:
```javascript
// Example: Get selected elements
viewer.getSelection().then(elements => {
  console.log('Selected:', elements);
});

// Example: Highlight elements
viewer.highlight(elementIds, color);
```

### Custom Workflows

Create automated processes:
- Auto-generate reports
- Schedule clash detection
- Progress photo mapping
- Quality checklists

### AI Assistant Integration

Ask questions about the model:
- "Show me all fire doors on level 3"
- "Calculate concrete volume for foundations"
- "Find potential safety hazards"
- "Generate progress report for this week"

## Support Resources

### Help Center
- Video tutorials
- FAQ section
- Best practices guide
- Release notes

### Training
- Live webinars
- Self-paced courses
- Certification program
- Custom training available

### Technical Support
- In-app chat support
- Email: <EMAIL>
- Knowledge base
- Community forum