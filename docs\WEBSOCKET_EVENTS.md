# WebSocket Events Documentation

## Overview

The AI Construction Management platform uses Socket.io for real-time communication between clients and the server. This document describes all WebSocket events, their payloads, and usage examples.

## Connection Setup

### Client Connection

```typescript
import { io, Socket } from 'socket.io-client';

// Initialize connection
const socket: Socket = io(process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:3001', {
  auth: {
    token: localStorage.getItem('authToken')
  },
  transports: ['websocket', 'polling'],
  reconnection: true,
  reconnectionDelay: 1000,
  reconnectionAttempts: 5
});

// Connection events
socket.on('connect', () => {
  console.log('Connected to server:', socket.id);
});

socket.on('disconnect', (reason) => {
  console.log('Disconnected:', reason);
});

socket.on('error', (error) => {
  console.error('Socket error:', error);
});
```

### Server Setup

```typescript
import { Server } from 'socket.io';
import { createServer } from 'http';

const httpServer = createServer();
const io = new Server(httpServer, {
  cors: {
    origin: process.env.CLIENT_URL,
    credentials: true
  }
});

// Authentication middleware
io.use(async (socket, next) => {
  const token = socket.handshake.auth.token;
  try {
    const user = await verifyToken(token);
    socket.data.user = user;
    next();
  } catch (err) {
    next(new Error('Authentication error'));
  }
});
```

## Event Categories

### 1. Project Events
### 2. Task Events  
### 3. Document Events
### 4. Safety Events
### 5. Chat Events
### 6. Notification Events
### 7. Collaboration Events
### 8. System Events

## Project Events

### project:join

Join a project room for real-time updates.

**Client → Server**
```typescript
// Emit
socket.emit('project:join', {
  projectId: 'uuid-123'
});

// Response
socket.on('project:joined', (data: {
  projectId: string;
  members: User[];
  success: boolean;
}) => {
  console.log('Joined project:', data.projectId);
});
```

### project:leave

Leave a project room.

**Client → Server**
```typescript
// Emit
socket.emit('project:leave', {
  projectId: 'uuid-123'
});
```

### project:update

Real-time project updates.

**Server → Client**
```typescript
// Listen
socket.on('project:update', (data: {
  projectId: string;
  changes: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  updatedBy: {
    userId: string;
    name: string;
  };
  timestamp: string;
}) => {
  // Update local state
  updateProject(data);
});
```

### project:member:add

New team member added to project.

**Server → Client**
```typescript
socket.on('project:member:add', (data: {
  projectId: string;
  member: {
    userId: string;
    name: string;
    role: string;
    email: string;
  };
  addedBy: string;
}) => {
  // Update team list
});
```

### project:status:change

Project status changed.

**Server → Client**
```typescript
socket.on('project:status:change', (data: {
  projectId: string;
  oldStatus: ProjectStatus;
  newStatus: ProjectStatus;
  reason?: string;
  changedBy: string;
  timestamp: string;
}) => {
  // Handle status change
});
```

## Task Events

### task:create

New task created.

**Client → Server**
```typescript
// Emit
socket.emit('task:create', {
  projectId: 'uuid-123',
  task: {
    name: 'Foundation Work',
    startDate: '2024-04-01',
    endDate: '2024-04-30',
    assignedTo: ['user-1', 'user-2'],
    dependencies: ['task-0']
  }
});

// Response
socket.on('task:created', (data: {
  task: Task;
  success: boolean;
  error?: string;
}) => {
  if (data.success) {
    addTaskToSchedule(data.task);
  }
});
```

### task:update

Task updated.

**Client → Server**
```typescript
// Emit
socket.emit('task:update', {
  taskId: 'task-uuid',
  projectId: 'project-uuid',
  updates: {
    progress: 75,
    status: 'IN_PROGRESS'
  }
});

// Broadcast to project members
socket.on('task:updated', (data: {
  taskId: string;
  projectId: string;
  updates: Partial<Task>;
  updatedBy: string;
}) => {
  updateTaskInUI(data);
});
```

### task:progress

Task progress update.

**Server → Client**
```typescript
socket.on('task:progress', (data: {
  taskId: string;
  projectId: string;
  progress: number;
  completedActivities: string[];
  remainingWork: string[];
  estimatedCompletion: string;
}) => {
  updateProgressBar(data);
});
```

### task:assign

Task assignment change.

**Server → Client**
```typescript
socket.on('task:assign', (data: {
  taskId: string;
  projectId: string;
  assigned: string[];
  unassigned: string[];
  assignedBy: string;
}) => {
  // Update task assignments
});
```

## Document Events

### document:upload:start

Document upload initiated.

**Client → Server**
```typescript
// Emit
socket.emit('document:upload:start', {
  projectId: 'uuid-123',
  fileName: 'floor-plans.pdf',
  fileSize: 5242880,
  fileType: 'application/pdf'
});

// Response
socket.on('document:upload:ready', (data: {
  uploadId: string;
  uploadUrl: string;
  chunkSize: number;
}) => {
  startChunkedUpload(data);
});
```

### document:upload:progress

Upload progress updates.

**Client → Server**
```typescript
// Emit progress
socket.emit('document:upload:progress', {
  uploadId: 'upload-uuid',
  progress: 45,
  bytesUploaded: 2359296
});

// Broadcast to project members
socket.on('document:upload:progress', (data: {
  uploadId: string;
  fileName: string;
  progress: number;
  uploadedBy: string;
}) => {
  updateUploadProgress(data);
});
```

### document:process:complete

Document processing completed.

**Server → Client**
```typescript
socket.on('document:process:complete', (data: {
  documentId: string;
  projectId: string;
  fileName: string;
  metadata: {
    pages: number;
    extractedText: boolean;
    thumbnailGenerated: boolean;
    aiAnalysis?: {
      documentType: string;
      keyInformation: string[];
      suggestions: string[];
    };
  };
}) => {
  // Update document list
});
```

### document:comment:add

Comment added to document.

**Server → Client**
```typescript
socket.on('document:comment:add', (data: {
  documentId: string;
  comment: {
    id: string;
    text: string;
    author: User;
    timestamp: string;
    parentId?: string;
    mentions: string[];
  };
}) => {
  addCommentToDocument(data);
});
```

## Safety Events

### safety:incident:report

New safety incident reported.

**Client → Server**
```typescript
// Emit
socket.emit('safety:incident:report', {
  projectId: 'uuid-123',
  incident: {
    type: 'NEAR_MISS',
    location: 'Level 5, North Tower',
    description: 'Worker slipped on wet surface',
    severity: 'MEDIUM',
    involvedPersonnel: ['user-1', 'user-2'],
    immediateActions: 'Area cordoned off'
  }
});

// Broadcast alert
socket.on('safety:incident:alert', (data: {
  incident: SafetyIncident;
  projectId: string;
  alertLevel: 'INFO' | 'WARNING' | 'CRITICAL';
}) => {
  showSafetyAlert(data);
});
```

### safety:hazard:detected

AI-detected safety hazard.

**Server → Client**
```typescript
socket.on('safety:hazard:detected', (data: {
  projectId: string;
  hazard: {
    id: string;
    type: string;
    location: string;
    confidence: number;
    imageUrl?: string;
    description: string;
    recommendedAction: string;
  };
  detectedAt: string;
}) => {
  // Show hazard warning
});
```

### safety:inspection:update

Safety inspection progress.

**Server → Client**
```typescript
socket.on('safety:inspection:update', (data: {
  inspectionId: string;
  projectId: string;
  inspector: User;
  progress: number;
  findings: {
    issues: number;
    resolved: number;
    pending: number;
  };
  currentLocation: string;
}) => {
  updateInspectionStatus(data);
});
```

## Chat Events

### chat:message

Send/receive chat messages.

**Client → Server**
```typescript
// Send message
socket.emit('chat:message', {
  projectId: 'uuid-123',
  channelId: 'general',
  message: {
    text: 'Foundation work completed!',
    attachments: ['image-url'],
    mentions: ['@user-2']
  }
});

// Receive message
socket.on('chat:message', (data: {
  id: string;
  projectId: string;
  channelId: string;
  message: {
    text: string;
    author: User;
    timestamp: string;
    attachments: Attachment[];
    mentions: string[];
    edited?: boolean;
  };
}) => {
  addMessageToChat(data);
});
```

### chat:typing

Typing indicators.

**Client → Server**
```typescript
// Start typing
socket.emit('chat:typing', {
  projectId: 'uuid-123',
  channelId: 'general',
  isTyping: true
});

// Typing indicator
socket.on('chat:typing', (data: {
  projectId: string;
  channelId: string;
  user: User;
  isTyping: boolean;
}) => {
  updateTypingIndicator(data);
});
```

### chat:reaction

Message reactions.

**Client → Server**
```typescript
// Add reaction
socket.emit('chat:reaction', {
  messageId: 'msg-uuid',
  reaction: '👍',
  action: 'add'
});

// Reaction update
socket.on('chat:reaction', (data: {
  messageId: string;
  reactions: {
    emoji: string;
    users: string[];
    count: number;
  }[];
}) => {
  updateMessageReactions(data);
});
```

## Notification Events

### notification:new

New notification for user.

**Server → Client**
```typescript
socket.on('notification:new', (data: {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  data?: any;
  actions?: {
    label: string;
    action: string;
    style?: 'primary' | 'secondary' | 'danger';
  }[];
  timestamp: string;
}) => {
  showNotification(data);
});
```

### notification:read

Mark notification as read.

**Client → Server**
```typescript
// Mark single notification
socket.emit('notification:read', {
  notificationId: 'notif-uuid'
});

// Mark multiple
socket.emit('notification:read:bulk', {
  notificationIds: ['notif-1', 'notif-2']
});
```

### notification:update:preferences

Update notification preferences.

**Client → Server**
```typescript
socket.emit('notification:update:preferences', {
  preferences: {
    email: {
      projectUpdates: true,
      safetyAlerts: true,
      taskAssignments: false
    },
    push: {
      all: true
    },
    inApp: {
      all: true
    }
  }
});
```

## Collaboration Events

### collab:cursor

Share cursor position in collaborative views.

**Client → Server**
```typescript
// Send cursor position
socket.emit('collab:cursor', {
  projectId: 'uuid-123',
  viewId: 'bim-viewer',
  position: {
    x: 450,
    y: 320,
    z: 0
  },
  viewport: {
    width: 1920,
    height: 1080
  }
});

// Receive cursor positions
socket.on('collab:cursor', (data: {
  userId: string;
  user: User;
  position: Position3D;
  color: string;
}) => {
  updateUserCursor(data);
});
```

### collab:selection

Share selection in collaborative editing.

**Client → Server**
```typescript
// Share selection
socket.emit('collab:selection', {
  projectId: 'uuid-123',
  viewId: 'schedule-gantt',
  selection: {
    type: 'task',
    ids: ['task-1', 'task-2']
  }
});

// Receive selections
socket.on('collab:selection', (data: {
  userId: string;
  user: User;
  selection: Selection;
  color: string;
}) => {
  highlightUserSelection(data);
});
```

### collab:annotation

Collaborative annotations.

**Client → Server**
```typescript
// Create annotation
socket.emit('collab:annotation:create', {
  projectId: 'uuid-123',
  viewId: 'bim-viewer',
  annotation: {
    type: 'comment',
    position: { x: 100, y: 200, z: 50 },
    text: 'Check this dimension',
    attachTo: 'element-uuid'
  }
});

// Annotation updates
socket.on('collab:annotation', (data: {
  action: 'create' | 'update' | 'delete';
  annotation: Annotation;
  user: User;
}) => {
  updateAnnotations(data);
});
```

### collab:screenshare

Screen sharing for remote collaboration.

**Client → Server**
```typescript
// Start screen share
socket.emit('collab:screenshare:start', {
  projectId: 'uuid-123',
  streamId: 'stream-uuid'
});

// Screen share events
socket.on('collab:screenshare', (data: {
  action: 'start' | 'stop';
  userId: string;
  user: User;
  streamId?: string;
}) => {
  handleScreenShare(data);
});
```

## System Events

### system:maintenance

System maintenance notifications.

**Server → Client**
```typescript
socket.on('system:maintenance', (data: {
  type: 'scheduled' | 'emergency';
  startTime: string;
  estimatedDuration: number;
  affectedServices: string[];
  message: string;
}) => {
  showMaintenanceNotice(data);
});
```

### system:performance

Performance metrics updates.

**Server → Client**
```typescript
socket.on('system:performance', (data: {
  metrics: {
    activeUsers: number;
    responseTime: number;
    queueDepth: number;
    serverLoad: number;
  };
  status: 'normal' | 'degraded' | 'critical';
}) => {
  updatePerformanceIndicator(data);
});
```

### system:feature:update

New feature announcements.

**Server → Client**
```typescript
socket.on('system:feature:update', (data: {
  feature: {
    id: string;
    name: string;
    description: string;
    category: string;
    learnMoreUrl: string;
  };
  version: string;
}) => {
  showFeatureAnnouncement(data);
});
```

## Error Handling

### Error Events

```typescript
// Connection errors
socket.on('connect_error', (error) => {
  console.error('Connection error:', error.message);
  if (error.type === 'authentication_error') {
    // Re-authenticate
    refreshAuthToken();
  }
});

// Custom errors
socket.on('error', (error: {
  code: string;
  message: string;
  details?: any;
}) => {
  handleSocketError(error);
});

// Reconnection
socket.on('reconnect', (attemptNumber) => {
  console.log('Reconnected after', attemptNumber, 'attempts');
  // Re-join rooms
  rejoinRooms();
});
```

## Room Management

### Room Structure

```typescript
// Room naming conventions
const rooms = {
  // Project rooms
  project: (id: string) => `project:${id}`,
  
  // Task rooms
  task: (projectId: string, taskId: string) => `project:${projectId}:task:${taskId}`,
  
  // Document rooms
  document: (projectId: string, docId: string) => `project:${projectId}:doc:${docId}`,
  
  // User rooms (for direct messages)
  user: (userId: string) => `user:${userId}`,
  
  // System rooms
  system: 'system:broadcast'
};
```

### Dynamic Room Management

```typescript
// Server-side room management
io.on('connection', (socket) => {
  // Auto-join user to their projects
  socket.on('auth:complete', async () => {
    const user = socket.data.user;
    const projects = await getUserProjects(user.id);
    
    projects.forEach(project => {
      socket.join(rooms.project(project.id));
    });
  });
  
  // Leave all project rooms on disconnect
  socket.on('disconnecting', () => {
    const rooms = Array.from(socket.rooms);
    rooms.forEach(room => {
      if (room.startsWith('project:')) {
        // Notify room members
        socket.to(room).emit('user:left', {
          userId: socket.data.user.id,
          room
        });
      }
    });
  });
});
```

## Best Practices

### Client-Side Implementation

```typescript
// Singleton socket instance
class SocketManager {
  private static instance: SocketManager;
  private socket: Socket;
  private eventHandlers: Map<string, Function[]> = new Map();
  
  private constructor() {
    this.socket = io(WS_URL, {
      auth: { token: getAuthToken() }
    });
    this.setupEventHandlers();
  }
  
  static getInstance(): SocketManager {
    if (!SocketManager.instance) {
      SocketManager.instance = new SocketManager();
    }
    return SocketManager.instance;
  }
  
  emit(event: string, data: any): void {
    this.socket.emit(event, data);
  }
  
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
    this.socket.on(event, handler);
  }
  
  off(event: string, handler: Function): void {
    this.socket.off(event, handler);
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }
  
  private setupEventHandlers(): void {
    this.socket.on('connect', () => {
      console.log('Socket connected');
      this.rejoinRooms();
    });
    
    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, try to reconnect
        this.socket.connect();
      }
    });
  }
  
  private rejoinRooms(): void {
    // Rejoin previously joined rooms
    const activeProjects = getActiveProjects();
    activeProjects.forEach(projectId => {
      this.emit('project:join', { projectId });
    });
  }
}

// Usage
const socketManager = SocketManager.getInstance();
socketManager.on('project:update', handleProjectUpdate);
socketManager.emit('task:create', taskData);
```

### Server-Side Best Practices

```typescript
// Middleware for event validation
const validateEvent = (schema: any) => {
  return (socket: Socket, next: Function) => {
    return (data: any, callback?: Function) => {
      try {
        const validated = schema.parse(data);
        next(validated, callback);
      } catch (error) {
        socket.emit('error', {
          code: 'VALIDATION_ERROR',
          message: error.message
        });
        if (callback) {
          callback({ error: 'Validation failed' });
        }
      }
    };
  };
};

// Usage
socket.on('task:create', 
  validateEvent(CreateTaskSchema),
  async (data, callback) => {
    try {
      const task = await createTask(data);
      // Broadcast to project room
      socket.to(rooms.project(data.projectId)).emit('task:created', task);
      callback({ success: true, task });
    } catch (error) {
      callback({ success: false, error: error.message });
    }
  }
);
```

### Performance Optimization

```typescript
// Debounce frequent events
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Throttle cursor movements
const throttledCursorUpdate = throttle((position) => {
  socket.emit('collab:cursor', position);
}, 50);

// Batch updates
class UpdateBatcher {
  private updates: any[] = [];
  private timeout: NodeJS.Timeout | null = null;
  
  add(update: any): void {
    this.updates.push(update);
    if (!this.timeout) {
      this.timeout = setTimeout(() => this.flush(), 100);
    }
  }
  
  flush(): void {
    if (this.updates.length > 0) {
      socket.emit('batch:update', this.updates);
      this.updates = [];
    }
    this.timeout = null;
  }
}
```

## Testing WebSocket Events

### Unit Testing

```typescript
import { Server } from 'socket.io';
import { io as ioc, Socket as ClientSocket } from 'socket.io-client';

describe('WebSocket Events', () => {
  let io: Server;
  let serverSocket: any;
  let clientSocket: ClientSocket;
  
  beforeAll((done) => {
    const httpServer = createServer();
    io = new Server(httpServer);
    
    httpServer.listen(() => {
      const port = (httpServer.address() as any).port;
      clientSocket = ioc(`http://localhost:${port}`);
      
      io.on('connection', (socket) => {
        serverSocket = socket;
      });
      
      clientSocket.on('connect', done);
    });
  });
  
  afterAll(() => {
    io.close();
    clientSocket.close();
  });
  
  test('should join project room', (done) => {
    serverSocket.on('project:join', (data: any) => {
      expect(data.projectId).toBe('test-project');
      serverSocket.join(`project:${data.projectId}`);
      serverSocket.emit('project:joined', { success: true });
    });
    
    clientSocket.emit('project:join', { projectId: 'test-project' });
    
    clientSocket.on('project:joined', (data: any) => {
      expect(data.success).toBe(true);
      done();
    });
  });
});
```

## Debugging

### Client-Side Debugging

```typescript
// Enable debug mode
localStorage.debug = 'socket.io-client:*';

// Log all events
const originalEmit = socket.emit;
socket.emit = function(...args) {
  console.log('⬆️ Emit:', args[0], args[1]);
  return originalEmit.apply(socket, args);
};

const originalOn = socket.on;
socket.on = function(event, handler) {
  return originalOn.call(socket, event, function(...args) {
    console.log('⬇️ Receive:', event, args[0]);
    return handler.apply(this, args);
  });
};
```

### Server-Side Debugging

```typescript
// Log all incoming events
io.use((socket, next) => {
  const originalOnevent = socket.onevent;
  socket.onevent = function(packet) {
    console.log('📨 Event:', packet.data);
    originalOnevent.call(this, packet);
  };
  next();
});

// Monitor room changes
io.of('/').adapter.on('join-room', (room, id) => {
  console.log(`Socket ${id} joined room ${room}`);
});

io.of('/').adapter.on('leave-room', (room, id) => {
  console.log(`Socket ${id} left room ${room}`);
});
```

## Security Considerations

### Authentication

```typescript
// Token refresh on reconnection
socket.on('connect', async () => {
  const token = await getValidToken();
  socket.emit('auth:refresh', { token });
});

// Server-side token validation
io.use(async (socket, next) => {
  const token = socket.handshake.auth.token;
  
  try {
    const decoded = await verifyToken(token);
    
    // Check token expiration
    if (decoded.exp < Date.now() / 1000) {
      return next(new Error('Token expired'));
    }
    
    // Load user data
    const user = await getUserById(decoded.userId);
    if (!user) {
      return next(new Error('User not found'));
    }
    
    socket.data.user = user;
    next();
  } catch (error) {
    next(new Error('Authentication failed'));
  }
});
```

### Rate Limiting

```typescript
// Server-side rate limiting
const rateLimiters = new Map();

const rateLimit = (eventName: string, maxRequests: number, windowMs: number) => {
  return async (socket: Socket, next: Function) => {
    const key = `${socket.data.user.id}:${eventName}`;
    
    if (!rateLimiters.has(key)) {
      rateLimiters.set(key, {
        requests: 0,
        resetTime: Date.now() + windowMs
      });
    }
    
    const limiter = rateLimiters.get(key);
    
    if (Date.now() > limiter.resetTime) {
      limiter.requests = 0;
      limiter.resetTime = Date.now() + windowMs;
    }
    
    if (limiter.requests >= maxRequests) {
      socket.emit('error', {
        code: 'RATE_LIMIT_EXCEEDED',
        message: `Too many ${eventName} requests`
      });
      return;
    }
    
    limiter.requests++;
    next();
  };
};

// Apply rate limiting
socket.use(rateLimit('chat:message', 10, 60000)); // 10 messages per minute
```

## Monitoring and Analytics

### Event Metrics

```typescript
// Track event metrics
const eventMetrics = new Map();

io.use((socket, next) => {
  const originalEmit = socket.emit;
  
  socket.emit = function(event, ...args) {
    // Track outgoing events
    trackEvent('outgoing', event, socket.data.user?.id);
    return originalEmit.apply(socket, [event, ...args]);
  };
  
  const originalOnevent = socket.onevent;
  socket.onevent = function(packet) {
    // Track incoming events
    if (packet.data && packet.data[0]) {
      trackEvent('incoming', packet.data[0], socket.data.user?.id);
    }
    originalOnevent.call(this, packet);
  };
  
  next();
});

function trackEvent(direction: string, event: string, userId?: string) {
  const key = `${direction}:${event}`;
  
  if (!eventMetrics.has(key)) {
    eventMetrics.set(key, {
      count: 0,
      users: new Set()
    });
  }
  
  const metric = eventMetrics.get(key);
  metric.count++;
  
  if (userId) {
    metric.users.add(userId);
  }
}

// Export metrics
setInterval(() => {
  const metrics = Array.from(eventMetrics.entries()).map(([key, value]) => ({
    event: key,
    count: value.count,
    uniqueUsers: value.users.size
  }));
  
  console.log('WebSocket Metrics:', metrics);
  // Send to monitoring service
}, 60000);
```

## Troubleshooting

### Common Issues

1. **Connection fails**
   - Check CORS configuration
   - Verify authentication token
   - Check firewall/proxy settings

2. **Events not received**
   - Ensure proper room joining
   - Check event name spelling
   - Verify server-side broadcast

3. **Performance issues**
   - Implement event throttling
   - Use event batching
   - Optimize payload size

4. **Memory leaks**
   - Remove event listeners on cleanup
   - Clear references on disconnect
   - Monitor room membership

### Debug Checklist

- [ ] Client connected successfully
- [ ] Authentication passed
- [ ] Joined correct rooms
- [ ] Event handlers registered
- [ ] No console errors
- [ ] Network tab shows WebSocket connection
- [ ] Server logs show events
- [ ] Rate limits not exceeded