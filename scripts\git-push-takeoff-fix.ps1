# Git commit and push for takeoff & estimating fix
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host " Committing Takeoff & Estimating Fix" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Check current branch
Write-Host "📍 Current branch:" -ForegroundColor Yellow
git branch --show-current

# Step 2: Check status
Write-Host "`n📊 Git status:" -ForegroundColor Yellow
git status --short

# Step 3: Stage all changes
Write-Host "`n➕ Staging all changes..." -ForegroundColor Yellow
git add -A

# Step 4: Create commit
Write-Host "`n💬 Creating commit..." -ForegroundColor Yellow

$commitMessage = @"
fix: enhance vision service to filter materials by company type for accurate electrical takeoffs

Problem:
- Electrical contractors were seeing general construction materials (concrete, steel, drywall) instead of electrical components
- Vision service was not filtering materials based on company type when analyzing drawings
- The uploaded electrical blueprint was incorrectly generating general construction takeoff items

Solution:
- Enhanced vision service to accept and use company type parameter for material detection
- Added company-specific prompts for Gemini AI to focus on trade-relevant materials only
- Implemented material filtering throughout the vision analysis pipeline
- Updated takeoff service v2 to pass company type to vision service during drawing analysis

Key Changes:
- src/lib/services/vision-service.ts:
  * Added company type parameter to analyzeDrawing and processDrawingPages methods
  * Created getCompanySpecificPrompt method with specialized prompts for each trade
  * Added getRelevantPatterns method to filter material patterns by trade
  * Enhanced material filtering based on company type
- src/lib/services/takeoff-service-v2.ts:
  * Updated to pass company type to vision service methods
  * Fixed processImageDrawing to accept company type parameter
- ace.md: Updated with fix documentation

Result:
- Electrical contractors now see only electrical materials (panels, conduits, wire, fixtures)
- Each trade sees only their relevant materials during takeoff analysis
- AI takeoff accuracy significantly improved for specialized contractors
"@

git commit -m $commitMessage

if ($LASTEXITCODE -ne 0) {
    Write-Host "`n❌ Commit failed!" -ForegroundColor Red
    exit 1
}

Write-Host "`n✅ Commit created successfully!" -ForegroundColor Green

# Step 5: Run the existing push script
Write-Host "`n🚀 Running push script with token authentication..." -ForegroundColor Yellow
Write-Host ""

# Execute the push script
& ".\scripts\git-push-with-token.ps1"
