#!/usr/bin/env node

/**
 * Clean logs on startup
 * This script removes old log files to ensure fresh logs for each development session
 */

const fs = require('fs');
const path = require('path');

const LOG_DIR = path.join(__dirname, '..', 'logs');
const LOG_SUBDIRS = ['vision', 'takeoff', 'gemini', 'api', 'general'];

console.log('🧹 Cleaning up logs...');

// Create logs directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
  console.log('✅ Created logs directory');
}

// Clean each subdirectory
LOG_SUBDIRS.forEach(subdir => {
  const dirPath = path.join(LOG_DIR, subdir);
  
  // Create subdirectory if it doesn't exist
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ Created ${subdir} directory`);
  } else {
    // Clean existing log files
    try {
      const files = fs.readdirSync(dirPath);
      let cleanedCount = 0;
      
      files.forEach(file => {
        if (file.endsWith('.log')) {
          fs.unlinkSync(path.join(dirPath, file));
          cleanedCount++;
        }
      });
      
      if (cleanedCount > 0) {
        console.log(`🗑️  Cleaned ${cleanedCount} log files from ${subdir}/`);
      }
    } catch (error) {
      console.error(`❌ Error cleaning ${subdir}:`, error.message);
    }
  }
});

console.log('✨ Log cleanup completed!');
console.log('📝 New logs will be created as the application runs');
console.log('');
console.log('💡 Tip: View logs in real-time with:');
console.log('   - API logs: tail -f logs/api/combined-*.log');
console.log('   - Vision logs: tail -f logs/vision/combined-*.log');
console.log('   - Takeoff logs: tail -f logs/takeoff/combined-*.log');
console.log('   - Or use the API: GET /api/logs?service=all');
console.log('');