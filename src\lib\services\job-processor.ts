// TODO: Uncomment after Prisma client generation is fixed
// import { ProcessingJob, ProcessingFile, PrismaClient } from '@prisma/client'
import type { ProcessingJob, ProcessingFile } from '@prisma/client'
const PrismaClient = {} as any // Temporary workaround
import { enhancedTakeoffService } from './takeoff-service-v2'
import { ProjectContext } from '../company-types'
import { TakeoffItem } from '@/types'

const prisma = new PrismaClient()

export interface JobConfig {
  userId: string
  projectId?: string
  projectContext: ProjectContext
  files: Array<{
    name: string
    size: number
    mimeType: string
    data: Buffer | ArrayBuffer
  }>
}

export interface JobProgress {
  jobId: string
  status: string
  totalFiles: number
  processedFiles: number
  failedFiles: number
  progress: number
  currentFile?: string
}

class JobProcessor {
  private processingQueue: Map<string, NodeJS.Timeout> = new Map()
  private progressCallbacks: Map<string, (progress: JobProgress) => void> = new Map()

  async createJob(config: JobConfig): Promise<ProcessingJob> {
    // Create job record in database
    const job = await prisma.processingJob.create({
      data: {
        userId: config.userId,
        projectId: config.projectId,
        status: 'pending',
        totalFiles: config.files.length,
        companyType: config.projectContext.companyType,
        projectContext: JSON.stringify(config.projectContext)
      }
    })

    // Create file records
    for (const file of config.files) {
      await prisma.processingFile.create({
        data: {
          jobId: job.id,
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.mimeType,
          status: 'pending'
        }
      })
    }

    // Queue job for processing
    this.queueJob(job.id, config)

    return job
  }

  private queueJob(jobId: string, config: JobConfig) {
    // Process job asynchronously after a short delay
    const timeout = setTimeout(async () => {
      await this.processJob(jobId, config)
      this.processingQueue.delete(jobId)
    }, 100)

    this.processingQueue.set(jobId, timeout)
  }

  private async processJob(jobId: string, config: JobConfig) {
    try {
      // Update job status to processing
      await prisma.processingJob.update({
        where: { id: jobId },
        data: {
          status: 'processing',
          startedAt: new Date()
        }
      })

      let processedCount = 0
      let failedCount = 0
      const allItems: TakeoffItem[] = []

      // Process each file
      for (let i = 0; i < config.files.length; i++) {
        const file = config.files[i]
        const fileRecord = await prisma.processingFile.findFirst({
          where: {
            jobId,
            fileName: file.name
          }
        })

        if (!fileRecord) continue

        try {
          // Update file status
          await prisma.processingFile.update({
            where: { id: fileRecord.id },
            data: { status: 'processing' }
          })

          // Emit progress
          this.emitProgress(jobId, {
            jobId,
            status: 'processing',
            totalFiles: config.files.length,
            processedFiles: processedCount,
            failedFiles: failedCount,
            progress: Math.round((i / config.files.length) * 100),
            currentFile: file.name
          })

          // Process file
          const startTime = Date.now()
          const fileData = {
            name: file.name,
            type: file.mimeType,
            size: file.size,
            arrayBuffer: async () => file.data,
            buffer: Buffer.isBuffer(file.data) ? file.data : Buffer.from(file.data)
          }

          const items = await enhancedTakeoffService.generateTakeoffItems(
            {
              ...config.projectContext,
              id: `${config.projectContext.id}-file-${i}`,
              name: file.name.replace(/\.[^/.]+$/, '')
            },
            fileData as any, // File-like object
            config.projectContext.companyType
          )

          // Add source file information
          const itemsWithSource = items.map(item => ({
            ...item,
            sourceFile: file.name,
            sourceFileIndex: i
          }))

          allItems.push(...itemsWithSource)

          // Update file record
          await prisma.processingFile.update({
            where: { id: fileRecord.id },
            data: {
              status: 'completed',
              itemsDetected: items.length,
              processingTime: Date.now() - startTime,
              progress: 100
            }
          })

          processedCount++
        } catch (error) {
          console.error(`Failed to process file ${file.name}:`, error)
          
          // Update file record with error
          await prisma.processingFile.update({
            where: { id: fileRecord.id },
            data: {
              status: 'failed',
              errorMessage: error instanceof Error ? error.message : 'Unknown error',
              progress: 0
            }
          })

          failedCount++
        }
      }

      // Calculate totals
      const totalCost = allItems.reduce((sum, item) => sum + item.totalCost, 0)

      // Create takeoff record if we have items
      if (allItems.length > 0 && config.projectId) {
        const takeoff = await prisma.takeoff.create({
          data: {
            projectId: config.projectId,
            userId: config.userId,
            processingJobId: jobId,
            status: 'completed',
            totalCost,
            confidence: 0.9
          }
        })

        // Create takeoff items
        for (const item of allItems) {
          await prisma.takeoffItem.create({
            data: {
              takeoffId: takeoff.id,
              name: item.description,
              description: item.specifications,
              quantity: item.quantity,
              unit: item.unit,
              unitCost: item.unitCost,
              totalCost: item.totalCost,
              category: item.category,
              materialType: item.materialType,
              supplier: item.supplier,
              leadTime: item.leadTime,
              confidence: item.confidence,
              source: 'vision'
            }
          })
        }
      }

      // Update job as completed
      await prisma.processingJob.update({
        where: { id: jobId },
        data: {
          status: 'completed',
          processedFiles: processedCount,
          failedFiles: failedCount,
          totalItems: allItems.length,
          totalCost,
          completedAt: new Date()
        }
      })

      // Emit final progress
      this.emitProgress(jobId, {
        jobId,
        status: 'completed',
        totalFiles: config.files.length,
        processedFiles: processedCount,
        failedFiles: failedCount,
        progress: 100
      })

    } catch (error) {
      console.error(`Job ${jobId} failed:`, error)
      
      // Update job as failed
      await prisma.processingJob.update({
        where: { id: jobId },
        data: {
          status: 'failed',
          completedAt: new Date()
        }
      })

      // Emit error progress
      this.emitProgress(jobId, {
        jobId,
        status: 'failed',
        totalFiles: config.files.length,
        processedFiles: 0,
        failedFiles: config.files.length,
        progress: 0
      })
    }
  }

  async getJobStatus(jobId: string): Promise<ProcessingJob | null> {
    return await prisma.processingJob.findUnique({
      where: { id: jobId },
      include: {
        files: true,
        takeoffs: {
          include: {
            items: true
          }
        }
      }
    })
  }

  onProgress(jobId: string, callback: (progress: JobProgress) => void) {
    this.progressCallbacks.set(jobId, callback)
  }

  offProgress(jobId: string) {
    this.progressCallbacks.delete(jobId)
  }

  private emitProgress(jobId: string, progress: JobProgress) {
    const callback = this.progressCallbacks.get(jobId)
    if (callback) {
      callback(progress)
    }
  }

  async cancelJob(jobId: string): Promise<boolean> {
    const timeout = this.processingQueue.get(jobId)
    if (timeout) {
      clearTimeout(timeout)
      this.processingQueue.delete(jobId)
    }

    const job = await prisma.processingJob.findUnique({
      where: { id: jobId }
    })

    if (job && job.status === 'pending') {
      await prisma.processingJob.update({
        where: { id: jobId },
        data: {
          status: 'cancelled',
          completedAt: new Date()
        }
      })
      return true
    }

    return false
  }
}

export const jobProcessor = new JobProcessor()