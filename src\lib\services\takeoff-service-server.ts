// Server-side takeoff service functions that can't run in the browser
// These functions are imported by API routes

import type { TakeoffItem, Estimate } from '@/types'
import type { CompanyType } from '@/lib/company-types'
import { geminiService } from '@/lib/gemini'

// Generate AI insights from takeoff items
export async function getAIInsights(
  items: TakeoffItem[],
  companyType?: CompanyType | null
): Promise<any[]> {
  const totalCost = items.reduce((sum, item) => sum + item.totalCost, 0)
  const categories = Array.from(new Set(items.map(item => item.category)))
  
  const prompt = `Analyze these takeoff items for a ${companyType || 'General Contractor'} and provide actionable insights:

Items: ${JSON.stringify(items.slice(0, 10))} // First 10 items for context
Total Cost: ${totalCost}
Categories: ${categories.join(', ')}

Provide 3-5 specific insights about:
1. Cost optimization opportunities
2. Material efficiency improvements
3. Schedule optimization
4. Risk mitigation

Format as JSON array with: type (optimization/accuracy/missing/schedule), title, description, impact`

  try {
    const response = await geminiService.sendMessage(prompt, 'takeoff-insights', {
      companyType
    })
    
    const insights = JSON.parse(response)
    return insights
  } catch (error) {
    // Fallback insights
    return [
      {
        type: 'optimization',
        title: 'Bulk Purchase Opportunity',
        description: `Consider bulk purchasing for high-quantity items to reduce costs`,
        impact: 'Potential 5-10% savings'
      },
      {
        type: 'accuracy',
        title: 'High Confidence Detection',
        description: 'AI detection confidence is above 90% for most items',
        impact: '90%+ Accuracy'
      }
    ]
  }
}

// Calculate category statistics
export async function getCategoryStats(items: TakeoffItem[]): Promise<any[]> {
  const categoryMap = new Map<string, { count: number; value: number }>()
  
  items.forEach(item => {
    const existing = categoryMap.get(item.category) || { count: 0, value: 0 }
    categoryMap.set(item.category, {
      count: existing.count + 1,
      value: existing.value + item.totalCost
    })
  })
  
  const totalValue = items.reduce((sum, item) => sum + item.totalCost, 0)
  
  return Array.from(categoryMap.entries()).map(([name, data]) => ({
    name,
    count: data.count,
    value: data.value,
    percentage: Math.round((data.value / totalValue) * 100)
  }))
}

// Generate cost breakdown
export async function getCostBreakdown(items: TakeoffItem[]): Promise<any> {
  const totalCost = items.reduce((sum, item) => sum + item.totalCost, 0)
  
  // Estimate breakdown based on typical construction ratios
  const materialRatio = 0.4
  const laborRatio = 0.35
  const equipmentRatio = 0.1
  const overheadRatio = 0.15
  
  return {
    materials: Math.round(totalCost * materialRatio),
    labor: Math.round(totalCost * laborRatio),
    equipment: Math.round(totalCost * equipmentRatio),
    overhead: Math.round(totalCost * overheadRatio),
    total: totalCost
  }
}

// Generate estimate from takeoff items
export async function generateEstimate(
  items: TakeoffItem[],
  projectDetails: any,
  companyType?: CompanyType | null
): Promise<Estimate> {
  // Calculate base costs from takeoff items
  let materialCost = 0
  let laborCost = 0
  let equipmentCost = 0
  
  // If items have broken-down costs, use them
  items.forEach(item => {
    if (item.materialCost) materialCost += item.materialCost
    if (item.laborCost) laborCost += item.laborCost
    if (item.equipmentCost) equipmentCost += item.equipmentCost
    
    // If not broken down, estimate based on typical ratios
    if (!item.materialCost && !item.laborCost && !item.equipmentCost) {
      materialCost += item.totalCost * 0.4
      laborCost += item.totalCost * 0.35
      equipmentCost += item.totalCost * 0.1
    }
  })
  
  // Calculate subcontractor costs based on company type
  let subcontractorCost = 0
  if (companyType === 'General Contractor') {
    // GCs typically subcontract 60-80% of work
    subcontractorCost = (materialCost + laborCost) * 0.7
    // Reduce direct costs accordingly
    materialCost *= 0.3
    laborCost *= 0.3
  }
  
  // Calculate overhead based on company size and type
  const overheadRates: Record<string, number> = {
    'General Contractor': 0.15,
    'Electrical Contractor': 0.12,
    'Plumbing Contractor': 0.12,
    'HVAC Contractor': 0.13,
    'Concrete Contractor': 0.10,
    'Steel Contractor': 0.11,
    'Roofing Contractor': 0.12,
    'Painting Contractor': 0.10,
    'Flooring Contractor': 0.11,
    'Landscaping Contractor': 0.10
  }
  
  const overheadRate = overheadRates[companyType || 'General Contractor'] || 0.15
  const subtotal = materialCost + laborCost + equipmentCost + subcontractorCost
  const overhead = subtotal * overheadRate
  
  // Calculate profit margin
  const profitRate = companyType === 'General Contractor' ? 0.10 : 0.15
  const totalBeforeProfit = subtotal + overhead
  const profit = totalBeforeProfit * profitRate
  
  // Add contingency based on project complexity
  const contingencyRate = projectDetails.type === 'commercial' ? 0.05 :
                         projectDetails.type === 'industrial' ? 0.07 :
                         projectDetails.type === 'institutional' ? 0.06 : 0.04
  const contingency = (totalBeforeProfit + profit) * contingencyRate
  
  const totalCost = Math.round(totalBeforeProfit + profit + contingency)
  
  // Generate AI insights about the estimate
  const prompt = `Analyze this construction estimate for a ${companyType || 'General Contractor'}:

Project: ${projectDetails.name}
Type: ${projectDetails.type}
Location: ${projectDetails.location?.address || 'Unknown'}

Cost Breakdown:
- Materials: $${materialCost.toLocaleString()}
- Labor: $${laborCost.toLocaleString()}
- Equipment: $${equipmentCost.toLocaleString()}
- Subcontractors: $${subcontractorCost.toLocaleString()}
- Overhead: $${overhead.toLocaleString()} (${(overheadRate * 100).toFixed(0)}%)
- Profit: $${profit.toLocaleString()} (${(profitRate * 100).toFixed(0)}%)
- Contingency: $${contingency.toLocaleString()} (${(contingencyRate * 100).toFixed(0)}%)
- Total: $${totalCost.toLocaleString()}

Provide brief notes about:
1. Whether these ratios are reasonable for this project type
2. Any cost optimization opportunities
3. Risk factors to consider`

  let notes = ''
  try {
    notes = await geminiService.sendMessage(prompt, 'estimate-analysis', {
      companyType
    })
  } catch (error) {
    notes = 'Estimate generated based on industry-standard ratios for ' + (companyType || 'general contractors') + '.'
  }
  
  return {
    id: `estimate-${Date.now()}`,
    projectId: projectDetails.id,
    takeoffId: `takeoff-${projectDetails.id}`,
    name: `Estimate for ${projectDetails.name}`,
    version: 1,
    status: 'draft' as const,
    laborCost: Math.round(laborCost),
    materialCost: Math.round(materialCost),
    equipmentCost: Math.round(equipmentCost),
    subcontractorCost: Math.round(subcontractorCost),
    overhead: Math.round(overhead),
    profit: Math.round(profit),
    totalCost,
    contingency: Math.round(contingency),
    notes,
    createdBy: 'system',
    createdAt: new Date(),
    updatedAt: new Date()
  }
}