import bcrypt from 'bcryptjs'
import { SignJWT, jwtVerify } from 'jose'
import prisma from '@/lib/prisma'
import { User, Session } from '@prisma/client'
import { cookies } from 'next/headers'
import { AUTH_CONFIG } from '@/lib/auth-config'
import { UserRole, hasPermission, convertLegacyRole } from '@/lib/user-role-types'

const SESSION_COOKIE_NAME = AUTH_CONFIG.SESSION_COOKIE_NAME

// Use getters to ensure we always use the latest env values
function getJWTSecret(): string {
  // For middleware/edge runtime compatibility, use the config
  return AUTH_CONFIG.JWT_SECRET
}

function getJWTExpiresIn(): string {
  return AUTH_CONFIG.JWT_EXPIRES_IN
}

export interface JWTPayload {
  userId: string
  email: string
  role: string
  sessionId: string
  [key: string]: any // Add index signature for jose compatibility
}

// Password hashing
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 10)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// JWT token management
export async function generateToken(payload: JWTPayload): Promise<string> {
  const secret = new TextEncoder().encode(getJWTSecret())
  const expiresIn = getJWTExpiresIn()

  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(expiresIn)
    .sign(secret)
}

export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const secret = new TextEncoder().encode(getJWTSecret())
    const { payload } = await jwtVerify(token, secret)

    // Validate that the payload has our required fields
    if (payload &&
        typeof payload.userId === 'string' &&
        typeof payload.email === 'string' &&
        typeof payload.role === 'string' &&
        typeof payload.sessionId === 'string') {
      return payload as JWTPayload
    }
    return null
  } catch (error) {
    return null
  }
}

// Session management
export async function createSession(userId: string): Promise<{ session: Session; token: string; expiresAt: Date }> {
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now

  const session = await prisma.session.create({
    data: {
      userId,
      sessionToken: generateSessionToken(),
      expires: expiresAt,
    },
    include: {
      user: true,
    },
  })

  // Create JWT token with standardized role
  const token = await generateToken({
    userId: session.user.id,
    email: session.user.email,
    role: convertLegacyRole(session.user.role),
    sessionId: session.id,
  })

  // Return token and session for the API route to handle
  return { session, token, expiresAt }
}

export async function verifySession(): Promise<{ user: User; session: Session } | null> {
  const cookieStore = await cookies()
  const token = cookieStore.get(SESSION_COOKIE_NAME)?.value

  if (!token) {
    return null
  }

  const payload = await verifyToken(token)
  if (!payload) {
    return null
  }

  // Verify session exists and is valid
  const session = await prisma.session.findUnique({
    where: {
      id: payload.sessionId,
    },
    include: {
      user: true,
    },
  })

  if (!session || session.expires < new Date()) {
    return null
  }

  // Update last login
  await prisma.user.update({
    where: { id: session.userId },
    data: { lastLogin: new Date() },
  })

  return { user: session.user, session }
}

export async function deleteSession(sessionId: string): Promise<void> {
  await prisma.session.delete({
    where: { id: sessionId },
  })

  const cookieStore = await cookies()
  cookieStore.delete(SESSION_COOKIE_NAME)
}

// User authentication
export async function signUp(data: {
  email: string
  password: string
  name?: string
  companyName?: string
  companyType?: string
}) {
  const existingUser = await prisma.user.findUnique({
    where: { email: data.email },
  })

  if (existingUser) {
    throw new Error('User already exists')
  }

  const hashedPassword = await hashPassword(data.password)

  const user = await prisma.user.create({
    data: {
      email: data.email,
      passwordHash: hashedPassword,
      name: data.name,
      companyName: data.companyName,
      companyType: data.companyType as any || 'GENERAL',
    },
  })

  // Create session
  const { session, token, expiresAt } = await createSession(user.id)

  return { user, session: session, token, expiresAt }
}

export async function signIn(email: string, password: string) {
  const user = await prisma.user.findUnique({
    where: { email },
  })

  if (!user || !user.passwordHash) {
    throw new Error('Invalid credentials')
  }

  const isValid = await verifyPassword(password, user.passwordHash)
  if (!isValid) {
    throw new Error('Invalid credentials')
  }

  if (!user.isActive) {
    throw new Error('Account is disabled')
  }

  // Create session
  const { session, token, expiresAt } = await createSession(user.id)

  return { user, session: session, token, expiresAt }
}

export async function signOut() {
  const sessionData = await verifySession()
  if (sessionData) {
    await deleteSession(sessionData.session.id)
  }
}

// Helper functions
function generateSessionToken(): string {
  return Buffer.from(crypto.getRandomValues(new Uint8Array(32))).toString('base64')
}

// Get current user
export async function getCurrentUser(): Promise<User | null> {
  const sessionData = await verifySession()
  return sessionData?.user || null
}

// Check if user has specific role
export async function hasRole(requiredRole: string): Promise<boolean> {
  const user = await getCurrentUser()
  if (!user) return false

  // Convert legacy roles to standardized format
  const userRole = convertLegacyRole(user.role) as UserRole
  const standardizedRequiredRole = convertLegacyRole(requiredRole) as UserRole

  return hasPermission(userRole, standardizedRequiredRole)
}