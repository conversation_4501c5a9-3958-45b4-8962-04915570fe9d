// Socket.io handler for Next.js API routes

import { NextApiRequest, NextApiResponse } from 'next'
import { Server as HTTPServer } from 'http'
import { initializeCollaborationServer } from './server'

export type NextApiResponseWithSocket = NextApiResponse & {
  socket: {
    server: HTTPServer & {
      io?: ReturnType<typeof initializeCollaborationServer>
    }
  }
}

export default function SocketHandler(req: NextApiRequest, res: NextApiResponseWithSocket) {
  if (!res.socket.server.io) {
    console.log('Initializing Socket.io server...')
    
    const httpServer = res.socket.server as HTTPServer
    const io = initializeCollaborationServer(httpServer)
    
    res.socket.server.io = io as any
    
    console.log('Socket.io server initialized')
  } else {
    console.log('Socket.io server already initialized')
  }
  
  res.end()
}