// Collaboration Provider component for BIM viewer

'use client'

import React, { createContext, useContext, ReactNode } from 'react'
import { useBIMCollaboration } from '../hooks'
import { 
  UserPresence, 
  Cursor3D, 
  ElementSelection, 
  Annotation, 
  ModelChange,
  CollaborationRoom,
  SocketError
} from '../types'
import { BIMViewerState } from '@/types'

interface CollaborationContextType {
  // Connection state
  isConnected: boolean
  error: SocketError | null
  
  // Room state
  room: CollaborationRoom | null
  users: UserPresence[]
  
  // Collaboration data
  cursors: Cursor3D[]
  selections: ElementSelection[]
  annotations: Annotation[]
  modelChanges: ModelChange[]
  viewStates: Record<string, BIMViewerState>
  
  // Actions
  updateCursor: (position: { x: number; y: number; z: number }, direction: { x: number; y: number; z: number }) => void
  selectElements: (elementIds: string[], mode?: 'single' | 'multiple' | 'box') => void
  deselectElements: (elementIds: string[]) => void
  createAnnotation: (content: string, type: Annotation['type'], position: { x: number; y: number; z: number }, elementId?: string) => void
  updateAnnotation: (id: string, updates: Partial<Annotation>) => void
  deleteAnnotation: (id: string) => void
  replyToAnnotation: (annotationId: string, content: string) => void
  sendModelChange: (change: Omit<ModelChange, 'id' | 'timestamp' | 'userId'>) => void
  updateViewState: (viewState: Partial<BIMViewerState>) => void
}

const CollaborationContext = createContext<CollaborationContextType | undefined>(undefined)

export interface CollaborationProviderProps {
  children: ReactNode
  roomId: string
  userId: string
  userName: string
  userRole: string
}

export function CollaborationProvider({
  children,
  roomId,
  userId,
  userName,
  userRole
}: CollaborationProviderProps) {
  const collaboration = useBIMCollaboration(roomId, userId, userName, userRole)

  return (
    <CollaborationContext.Provider value={collaboration}>
      {children}
    </CollaborationContext.Provider>
  )
}

export function useCollaboration() {
  const context = useContext(CollaborationContext)
  if (context === undefined) {
    throw new Error('useCollaboration must be used within a CollaborationProvider')
  }
  return context
}