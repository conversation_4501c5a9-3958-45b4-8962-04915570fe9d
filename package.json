{"name": "ai-construction-management", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,md,json}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,md,json}\"", "postinstall": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts", "analyze": "ANALYZE=true next build", "docker:build": "docker build -t ai-construction-app .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f app", "deploy:aws": "./scripts/deploy-aws.sh", "deploy:vercel": "./scripts/deploy-vercel.sh", "deploy:railway": "./scripts/deploy-railway.sh"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.3.2", "@mapbox/mapbox-gl-draw": "^1.4.3", "@prisma/client": "^5.7.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-three/drei": "^9.92.7", "@react-three/fiber": "^8.15.12", "@tanstack/react-query": "^5.14.2", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-table": "^8.10.7", "@types/react-big-calendar": "^1.8.9", "@vercel/analytics": "^1.1.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "csv-parser": "^3.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "ioredis": "^5.3.2", "jose": "^5.2.0", "lucide-react": "^0.303.0", "mapbox-gl": "^3.0.1", "next": "14.2.30", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "react": "^18.2.0", "react-big-calendar": "^1.8.5", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-resizable-panels": "^1.0.7", "recharts": "^2.10.3", "sharp": "^0.34.2", "socket.io": "^4.6.0", "socket.io-client": "^4.6.0", "sonner": "^1.3.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "three": "^0.160.0", "vaul": "^0.8.0", "web-ifc-three": "^0.0.126", "webrtc-adapter": "^8.2.3", "xlsx": "^0.18.5", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/mapbox-gl": "^3.0.0", "@types/node": "^20.10.5", "@types/pdf-parse": "^1.1.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/three": "^0.160.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.2.30", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "prisma": "^5.7.0", "tailwindcss": "^3.4.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=20.0.0", "npm": ">=9.0.0"}}