'use client'

import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  Building2, 
  Brain, 
  Calendar, 
  Camera, 
  Shield, 
  TrendingUp,
  Zap,
  Users,
  BarChart3,
  Bot,
  Eye,
  Clock
} from 'lucide-react'
import Link from 'next/link'

export default function HomePage() {
  const router = useRouter()

  const features = [
    {
      icon: Brain,
      title: "AI-Powered Intelligence",
      description: "Advanced machine learning algorithms optimize every aspect of your construction projects",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: Camera,
      title: "360° Reality Capture",
      description: "Automated progress tracking with computer vision and real-time site monitoring",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      icon: Calendar,
      title: "Smart Scheduling",
      description: "Generate millions of schedule scenarios to find the optimal project timeline",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      icon: Shield,
      title: "AI Safety Monitoring",
      description: "Predictive analytics to prevent accidents before they happen",
      gradient: "from-red-500 to-orange-500"
    },
    {
      icon: Building2,
      title: "Automated Takeoff",
      description: "98% accurate quantity takeoff from plans in seconds, not hours",
      gradient: "from-indigo-500 to-purple-500"
    },
    {
      icon: Bo<PERSON>,
      title: "Robotic Integration",
      description: "Automated layout printing and field operations with cutting-edge robotics",
      gradient: "from-yellow-500 to-orange-500"
    }
  ]

  const stats = [
    { label: "Project Duration Reduction", value: "30%", icon: Clock },
    { label: "Cost Savings", value: "20%", icon: TrendingUp },
    { label: "Safety Incidents Reduced", value: "50%", icon: Shield },
    { label: "Productivity Increase", value: "40%", icon: Zap }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-gray-100/50 dark:bg-grid-gray-700/50" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6">
              The Future of
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-construction-blue to-construction-orange">
                Construction Management
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-10">
              Harness the power of AI, computer vision, and robotics to revolutionize 
              how you plan, build, and manage construction projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.push('/dashboard')}
                className="px-8 py-4 bg-gradient-to-r from-construction-blue to-construction-orange text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all"
              >
                Start Free Trial
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.push('/demo')}
                className="px-8 py-4 bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all border border-gray-200 dark:border-gray-700"
              >
                Watch Demo
              </motion.button>
            </div>
          </motion.div>

          {/* Animated Construction Visual */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            className="mt-16 relative"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-construction-blue/20 to-construction-orange/20 blur-3xl" />
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 max-w-5xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[Eye, BarChart3, Users].map((Icon, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                    className="flex flex-col items-center p-6 bg-gray-50 dark:bg-gray-700 rounded-lg"
                  >
                    <Icon className="w-12 h-12 text-construction-blue mb-4" />
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {index === 0 ? "Real-time Monitoring" : index === 1 ? "Advanced Analytics" : "Team Collaboration"}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Powered by Advanced AI
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Combining the best features from industry leaders with breakthrough innovations
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl"
                  style={{ backgroundImage: `linear-gradient(to right, ${feature.gradient.split(' ')[1]}, ${feature.gradient.split(' ')[3]})` }}
                />
                <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-8 h-full">
                  <div className={`inline-flex p-3 rounded-lg bg-gradient-to-r ${feature.gradient} mb-6`}>
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Proven Results
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Join industry leaders achieving unprecedented efficiency
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="inline-flex p-4 rounded-full bg-gradient-to-r from-construction-blue to-construction-orange mb-4">
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 dark:text-gray-300">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-construction-blue to-construction-orange">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Transform Your Construction Projects?
            </h2>
            <p className="text-xl text-white/90 mb-10">
              Start your free trial today and experience the future of construction management
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => router.push('/signup')}
              className="px-10 py-5 bg-white text-construction-blue font-bold rounded-lg shadow-xl hover:shadow-2xl transition-all text-lg"
            >
              Get Started Free
            </motion.button>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
