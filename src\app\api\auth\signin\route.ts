import { NextRequest, NextResponse } from 'next/server'
import { signIn } from '@/lib/db/auth'
import { z } from 'zod'

const signInSchema = z.object({
  email: z.string().email(),
  password: z.string(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const { email, password } = signInSchema.parse(body)
    
    // Sign in user
    const { user, session, token, expiresAt } = await signIn(email, password)
    
    // Create response
    const response = NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        companyType: user.companyType,
        companyName: user.companyName,
      },
      message: 'Signed in successfully',
      redirectUrl: '/dashboard',
    })
    
    // Set the session cookie in the response
    response.cookies.set('session', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: expiresAt,
      path: '/',
    })
    
    return response
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }
    
    if (error instanceof Error && error.message === 'Invalid credentials') {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      )
    }
    
    if (error instanceof Error && error.message === 'Account is disabled') {
      return NextResponse.json(
        { error: 'Account is disabled' },
        { status: 403 }
      )
    }
    
    console.error('Signin error:', error)
    return NextResponse.json(
      { error: 'Failed to sign in' },
      { status: 500 }
    )
  }
}