# Database Setup Guide for AI Construction Management

## Quick Start

The application uses PostgreSQL as its database. Follow these steps to set it up:

### Option 1: Use Default PostgreSQL (Recommended)

1. **Install PostgreSQL** (if not already installed):
   - Windows: Download from https://www.postgresql.org/download/windows/
   - During installation, remember the password you set for the 'postgres' user

2. **Update Database Password**:
   - Open `.env` file in the project root
   - Update the DATABASE_URL with your PostgreSQL password:
   ```
   DATABASE_URL="postgresql://postgres:YOUR_PASSWORD@localhost:5432/ai_construction_db"
   ```
   Replace `YOUR_PASSWORD` with your actual PostgreSQL password

3. **Run Database Setup**:
   ```bash
   npm run db:push
   npm run db:seed
   ```

### Option 2: Use Docker (Alternative)

1. **Create docker-compose.yml**:
   ```yaml
   version: '3.8'
   services:
     postgres:
       image: postgres:15
       environment:
         POSTGRES_USER: postgres
         POSTGRES_PASSWORD: postgres
         POSTGRES_DB: ai_construction_db
       ports:
         - "5432:5432"
       volumes:
         - postgres_data:/var/lib/postgresql/data

   volumes:
     postgres_data:
   ```

2. **Start PostgreSQL**:
   ```bash
   docker-compose up -d
   ```

3. **Run Database Setup**:
   ```bash
   npm run db:push
   npm run db:seed
   ```

### Option 3: Use SQLite for Development (Simplest)

1. **Run SQLite setup script**:
   ```bash
   node scripts/use-sqlite-fallback.js
   npm run db:push
   npm run db:seed
   ```

## Troubleshooting

### "Authentication failed" Error
- Make sure PostgreSQL is running
- Verify your password is correct in the DATABASE_URL
- Try connecting with pgAdmin or psql to verify credentials

### "Cannot connect to localhost:5432"
- Ensure PostgreSQL service is running:
  - Windows: Check Services app for "postgresql-x64-[version]"
  - Run: `pg_ctl status` or check Task Manager

### "Permission denied" errors
- Close any applications using the database
- Restart your terminal/PowerShell
- Try running as Administrator (Windows)

## Demo Credentials

After successful setup, use these credentials:
- **Admin**: <EMAIL> / demo123456
- **Contractor**: <EMAIL> / demo123456

## Need Help?

1. Check PostgreSQL logs for errors
2. Verify DATABASE_URL format is correct
3. Ensure no firewall is blocking port 5432
4. Try the SQLite option for quick development setup