Write-Host "=====================================" -ForegroundColor Cyan
Write-Host " Committing and Pushing to GitHub" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Check current branch
Write-Host "📍 Current branch:" -ForegroundColor Yellow
git branch --show-current

# Step 2: Check status
Write-Host "`n📊 Git status:" -ForegroundColor Yellow
git status --short

# Step 3: Stage all changes
Write-Host "`n➕ Staging all changes..." -ForegroundColor Yellow
git add -A

# Step 4: Create commit
Write-Host "`n💬 Creating commit..." -ForegroundColor Yellow

$commitMessage = @"
fix: resolve CompanyType export error and improve build stability

- Fixed CompanyType re-export error in CompanyContext.tsx
- Removed redundant export statement that was causing build failures
- Ensured all imports from company-types module are correct
- Added build verification script for future checks
- Updated ace.md with latest fix documentation and project status

The application now builds successfully with the Company Type specialization
feature working as intended. AI responses are properly tailored to the
selected construction trade type.

Affected files:
- src/contexts/CompanyContext.tsx (fixed re-export)
- scripts/verify-build.ps1 (new verification script)
- scripts/git-commit-push.ps1 (new commit helper)
- ace.md (updated documentation)
"@

git commit -m $commitMessage

if ($LASTEXITCODE -ne 0) {
    Write-Host "`n❌ Commit failed!" -ForegroundColor Red
    exit 1
}

Write-Host "`n✅ Commit created successfully!" -ForegroundColor Green

# Step 5: Run the existing push script
Write-Host "`n🚀 Running push script with token authentication..." -ForegroundColor Yellow
Write-Host ""

# Execute the push script
& ".\scripts\git-push-with-token.ps1"
