# Security Documentation

## Table of Contents

1. [Security Overview](#security-overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Data Protection](#data-protection)
4. [API Security](#api-security)
5. [Infrastructure Security](#infrastructure-security)
6. [Security Best Practices](#security-best-practices)
7. [Vulnerability Management](#vulnerability-management)
8. [Incident Response](#incident-response)
9. [Compliance](#compliance)
10. [Security Checklist](#security-checklist)

## Security Overview

The AI Construction Management platform implements multiple layers of security to protect sensitive construction project data, ensure user privacy, and maintain system integrity. Our security approach follows industry best practices and compliance requirements.

### Security Principles

1. **Defense in Depth**: Multiple security layers
2. **Least Privilege**: Minimal access rights
3. **Zero Trust**: Verify everything
4. **Data Minimization**: Collect only what's needed
5. **Secure by Default**: Security built-in, not added on

### Security Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        WAF / DDoS Protection                 │
├─────────────────────────────────────────────────────────────┤
│                          Load Balancer                       │
│                         (SSL Termination)                    │
├─────────────────────────────────────────────────────────────┤
│                          API Gateway                         │
│               (Rate Limiting, Authentication)                │
├─────────────────────────────────────────────────────────────┤
│                      Application Layer                       │
│           (CORS, CSP, Input Validation, JWT)               │
├─────────────────────────────────────────────────────────────┤
│                        Data Layer                           │
│        (Encryption at Rest, Row-Level Security)            │
└─────────────────────────────────────────────────────────────┘
```

## Authentication & Authorization

### Authentication System

#### JWT (JSON Web Tokens)

```typescript
// Token structure
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "userId": "uuid",
    "email": "<EMAIL>",
    "role": "PROJECT_MANAGER",
    "companyId": "uuid",
    "iat": 1234567890,
    "exp": 1234571490
  },
  "signature": "..."
}
```

#### Token Management

1. **Access Tokens**
   - Short-lived (15 minutes)
   - Used for API requests
   - Contains user claims

2. **Refresh Tokens**
   - Long-lived (7 days)
   - Stored securely
   - Used to obtain new access tokens

3. **Token Security**
   ```typescript
   // Secure token generation
   import { SignJWT } from 'jose';
   
   const secret = new TextEncoder().encode(process.env.JWT_SECRET);
   
   const token = await new SignJWT({ userId, role })
     .setProtectedHeader({ alg: 'HS256' })
     .setIssuedAt()
     .setExpirationTime('15m')
     .sign(secret);
   ```

### Authorization

#### Role-Based Access Control (RBAC)

```typescript
enum UserRole {
  ADMIN = 'ADMIN',
  PROJECT_MANAGER = 'PROJECT_MANAGER',
  ENGINEER = 'ENGINEER',
  FIELD_WORKER = 'FIELD_WORKER',
  VIEWER = 'VIEWER'
}

// Permission matrix
const permissions = {
  ADMIN: ['*'],
  PROJECT_MANAGER: [
    'project:read',
    'project:write',
    'project:delete',
    'team:manage',
    'report:generate'
  ],
  ENGINEER: [
    'project:read',
    'project:write',
    'document:upload',
    'model:view'
  ],
  FIELD_WORKER: [
    'project:read',
    'report:create',
    'photo:upload'
  ],
  VIEWER: [
    'project:read',
    'document:read'
  ]
};
```

#### Resource-Level Permissions

```typescript
// Check project access
async function canAccessProject(
  userId: string, 
  projectId: string, 
  permission: string
): Promise<boolean> {
  const access = await prisma.projectUser.findFirst({
    where: {
      userId,
      projectId,
      role: {
        permissions: {
          has: permission
        }
      }
    }
  });
  
  return !!access;
}
```

### Multi-Factor Authentication (MFA)

1. **TOTP (Time-based One-Time Passwords)**
   ```typescript
   // Setup MFA
   const secret = authenticator.generateSecret();
   const qrCode = await qrcode.toDataURL(
     authenticator.keyuri(email, 'Construction AI', secret)
   );
   ```

2. **SMS/Email Verification**
   - Backup authentication method
   - Rate-limited to prevent abuse

3. **Biometric Authentication**
   - WebAuthn support
   - Platform-specific biometrics

## Data Protection

### Encryption

#### Encryption at Rest

1. **Database Encryption**
   ```sql
   -- PostgreSQL transparent data encryption
   ALTER DATABASE construction_db SET encryption_key = 'vault:v1:key';
   ```

2. **File Storage Encryption**
   ```typescript
   // Encrypt files before storage
   import { createCipheriv, randomBytes } from 'crypto';
   
   function encryptFile(buffer: Buffer): EncryptedFile {
     const algorithm = 'aes-256-gcm';
     const key = Buffer.from(process.env.ENCRYPTION_KEY, 'hex');
     const iv = randomBytes(16);
     const cipher = createCipheriv(algorithm, key, iv);
     
     const encrypted = Buffer.concat([
       cipher.update(buffer),
       cipher.final()
     ]);
     
     return {
       data: encrypted,
       iv: iv.toString('hex'),
       tag: cipher.getAuthTag().toString('hex')
     };
   }
   ```

#### Encryption in Transit

1. **TLS 1.3**
   - All connections use HTTPS
   - Strong cipher suites only
   - HSTS enabled

2. **Certificate Pinning**
   ```typescript
   // Mobile app certificate pinning
   const certificatePin = 'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=';
   ```

### Data Classification

| Level | Description | Examples | Protection |
|-------|-------------|----------|------------|
| **Critical** | Highly sensitive | Passwords, API keys | Encrypted, limited access |
| **Confidential** | Business sensitive | Financial data, PII | Encrypted, role-based access |
| **Internal** | Internal use | Project details | Standard protection |
| **Public** | Public information | Marketing content | Basic protection |

### Personal Data Protection

#### GDPR Compliance

1. **Data Minimization**
   ```typescript
   // Collect only necessary data
   interface UserRegistration {
     email: string;
     name: string;
     company: string;
     // No unnecessary fields
   }
   ```

2. **Right to Erasure**
   ```typescript
   async function deleteUserData(userId: string) {
     // Soft delete with anonymization
     await prisma.user.update({
       where: { id: userId },
       data: {
         email: `deleted-${userId}@example.com`,
         name: 'Deleted User',
         deletedAt: new Date()
       }
     });
   }
   ```

3. **Data Portability**
   ```typescript
   async function exportUserData(userId: string) {
     const data = await prisma.user.findUnique({
       where: { id: userId },
       include: {
         projects: true,
         documents: true,
         activities: true
       }
     });
     
     return formatForExport(data);
   }
   ```

## API Security

### Input Validation

#### Zod Schemas

```typescript
import { z } from 'zod';

// Strict input validation
const CreateProjectSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  startDate: z.date(),
  endDate: z.date(),
  budget: z.number().positive().max(1e9),
  location: z.object({
    address: z.string(),
    coordinates: z.object({
      lat: z.number().min(-90).max(90),
      lng: z.number().min(-180).max(180)
    })
  })
});

// Validate and sanitize
export async function createProject(req: Request) {
  const body = await req.json();
  const validated = CreateProjectSchema.parse(body);
  // Process validated data
}
```

### Rate Limiting

```typescript
// Rate limiting configuration
const rateLimiter = new RateLimiter({
  points: 100, // Number of points
  duration: 60, // Per 60 seconds
  blockDuration: 60 * 5, // Block for 5 minutes
});

// Apply rate limiting
export async function rateLimit(req: Request) {
  const key = req.headers.get('x-forwarded-for') || 'anonymous';
  
  try {
    await rateLimiter.consume(key);
  } catch (rejRes) {
    throw new Error('Too Many Requests', { 
      status: 429,
      headers: {
        'Retry-After': rejRes.msBeforeNext / 1000,
        'X-RateLimit-Limit': rateLimiter.points,
        'X-RateLimit-Remaining': rejRes.remainingPoints,
        'X-RateLimit-Reset': new Date(Date.now() + rejRes.msBeforeNext)
      }
    });
  }
}
```

### CORS Configuration

```typescript
// Strict CORS policy
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      process.env.NEXT_PUBLIC_APP_URL,
      'https://app.construction.ai',
      'https://mobile.construction.ai'
    ];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  maxAge: 86400 // 24 hours
};
```

### API Key Management

```typescript
// API key generation
function generateApiKey(): string {
  const prefix = 'sk_live_';
  const key = randomBytes(32).toString('hex');
  return `${prefix}${key}`;
}

// API key validation
async function validateApiKey(key: string) {
  const hashedKey = await hash(key);
  const apiKey = await prisma.apiKey.findFirst({
    where: { 
      keyHash: hashedKey,
      expiresAt: { gt: new Date() },
      revoked: false
    }
  });
  
  if (!apiKey) {
    throw new UnauthorizedError('Invalid API key');
  }
  
  // Update last used
  await prisma.apiKey.update({
    where: { id: apiKey.id },
    data: { lastUsedAt: new Date() }
  });
  
  return apiKey;
}
```

## Infrastructure Security

### Network Security

1. **Network Segmentation**
   ```
   ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
   │   Public DMZ    │────▶│   App Subnet    │────▶│   Data Subnet   │
   │  Load Balancer  │     │   App Servers   │     │    Database     │
   └─────────────────┘     └─────────────────┘     └─────────────────┘
          Public                Private                 Private
   ```

2. **Firewall Rules**
   - Deny all by default
   - Allow only required ports
   - IP whitelisting for admin access

3. **VPN Access**
   - Required for infrastructure access
   - MFA enforced
   - Audit logging enabled

### Container Security

```dockerfile
# Secure Dockerfile practices
FROM node:18-alpine AS builder

# Run as non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy only necessary files
COPY --chown=nextjs:nodejs . .

# Remove dev dependencies
RUN npm ci --only=production

# Final stage
FROM node:18-alpine
RUN apk add --no-cache libc6-compat

# Copy from builder
COPY --from=builder --chown=nextjs:nodejs /app .

# Security headers
ENV NODE_ENV production

USER nextjs
EXPOSE 3000

CMD ["node", "server.js"]
```

### Secrets Management

1. **Environment Variables**
   ```typescript
   // Use environment variables for secrets
   const config = {
     jwtSecret: process.env.JWT_SECRET,
     dbUrl: process.env.DATABASE_URL,
     apiKeys: {
       gemini: process.env.GEMINI_API_KEY,
       mapbox: process.env.MAPBOX_TOKEN
     }
   };
   
   // Validate required secrets
   function validateSecrets() {
     const required = ['JWT_SECRET', 'DATABASE_URL'];
     for (const key of required) {
       if (!process.env[key]) {
         throw new Error(`Missing required secret: ${key}`);
       }
     }
   }
   ```

2. **Key Rotation**
   - Regular rotation schedule
   - Zero-downtime rotation
   - Automated where possible

## Security Best Practices

### Secure Coding

1. **Dependency Management**
   ```json
   // Regular updates
   "scripts": {
     "audit": "npm audit",
     "audit:fix": "npm audit fix",
     "check-updates": "ncu -u"
   }
   ```

2. **Code Review Security Checklist**
   - [ ] No hardcoded secrets
   - [ ] Input validation present
   - [ ] SQL injection prevention
   - [ ] XSS prevention
   - [ ] CSRF protection
   - [ ] Proper error handling
   - [ ] Secure session management

3. **Security Headers**
   ```typescript
   // middleware.ts
   export function middleware(request: NextRequest) {
     const response = NextResponse.next();
     
     // Security headers
     response.headers.set('X-Frame-Options', 'DENY');
     response.headers.set('X-Content-Type-Options', 'nosniff');
     response.headers.set('X-XSS-Protection', '1; mode=block');
     response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
     response.headers.set(
       'Content-Security-Policy',
       "default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
     );
     response.headers.set(
       'Strict-Transport-Security',
       'max-age=31536000; includeSubDomains'
     );
     
     return response;
   }
   ```

### Session Security

```typescript
// Secure session configuration
const sessionConfig = {
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 1000 * 60 * 60 * 24, // 24 hours
    sameSite: 'strict'
  }
};

// Session validation
function validateSession(session: Session) {
  // Check session age
  if (Date.now() - session.createdAt > MAX_SESSION_AGE) {
    throw new SessionExpiredError();
  }
  
  // Validate session fingerprint
  if (session.fingerprint !== generateFingerprint(request)) {
    throw new SessionHijackError();
  }
}
```

## Vulnerability Management

### Security Scanning

1. **Automated Scanning**
   ```yaml
   # GitHub Actions security workflow
   name: Security Scan
   on: [push, pull_request]
   
   jobs:
     security:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         
         - name: Run npm audit
           run: npm audit
           
         - name: Run Snyk scan
           uses: snyk/actions/node@master
           env:
             SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
             
         - name: Run SAST scan
           uses: github/super-linter@v4
   ```

2. **Dependency Scanning**
   - Daily vulnerability checks
   - Automated PR for updates
   - Security advisories monitoring

### Penetration Testing

1. **Regular Testing Schedule**
   - Quarterly external pen tests
   - Monthly internal security reviews
   - Continuous automated testing

2. **Testing Scope**
   - Application security
   - API security
   - Infrastructure security
   - Social engineering

### Bug Bounty Program

```markdown
## Responsible Disclosure

We appreciate security researchers who help us keep our platform secure.

### Scope
- *.construction.ai
- Mobile applications
- API endpoints

### Out of Scope
- Social engineering
- Physical attacks
- Third-party services

### Rewards
- Critical: $1,000 - $5,000
- High: $500 - $1,000
- Medium: $100 - $500
- Low: $50 - $100

Report to: <EMAIL>
```

## Incident Response

### Incident Response Plan

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Detect    │────▶│   Assess    │────▶│   Contain   │────▶│   Recover   │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
       │                                                             │
       └─────────────────────────────────────────────────────────────┘
                                  Learn & Improve
```

1. **Detection**
   - Monitoring alerts
   - User reports
   - Automated detection

2. **Assessment**
   - Severity classification
   - Impact analysis
   - Resource allocation

3. **Containment**
   - Isolate affected systems
   - Preserve evidence
   - Prevent spread

4. **Recovery**
   - Remove threat
   - Restore services
   - Verify integrity

5. **Post-Incident**
   - Root cause analysis
   - Lessons learned
   - Process improvements

### Security Monitoring

```typescript
// Centralized logging
const logger = winston.createLogger({
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ 
      filename: 'security.log',
      level: 'warn'
    })
  ]
});

// Security event logging
function logSecurityEvent(event: SecurityEvent) {
  logger.warn('Security Event', {
    type: event.type,
    userId: event.userId,
    ip: event.ip,
    userAgent: event.userAgent,
    timestamp: new Date(),
    details: event.details
  });
}

// Real-time alerting
async function alertOnSuspiciousActivity(event: SecurityEvent) {
  if (event.severity === 'HIGH') {
    await sendAlert({
      to: process.env.SECURITY_TEAM_EMAIL,
      subject: `Security Alert: ${event.type}`,
      body: formatSecurityAlert(event)
    });
  }
}
```

## Compliance

### Regulatory Compliance

1. **GDPR (General Data Protection Regulation)**
   - Data protection by design
   - Privacy impact assessments
   - Data processing agreements
   - User consent management

2. **SOC 2 Type II**
   - Security controls
   - Availability measures
   - Processing integrity
   - Confidentiality
   - Privacy

3. **ISO 27001**
   - Information security management
   - Risk assessment
   - Control implementation
   - Continuous improvement

### Audit Trail

```typescript
// Comprehensive audit logging
interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string;
  action: string;
  resource: string;
  oldValue?: any;
  newValue?: any;
  ip: string;
  userAgent: string;
}

// Audit middleware
export function auditMiddleware(action: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const auditLog: AuditLog = {
      id: generateId(),
      timestamp: new Date(),
      userId: req.user?.id,
      action,
      resource: req.path,
      ip: req.ip,
      userAgent: req.headers['user-agent']
    };
    
    await saveAuditLog(auditLog);
    next();
  };
}
```

## Security Checklist

### Development

- [ ] Use latest framework versions
- [ ] Enable strict TypeScript
- [ ] Implement input validation
- [ ] Use parameterized queries
- [ ] Sanitize user input
- [ ] Implement CSRF protection
- [ ] Use secure random generators
- [ ] Handle errors securely
- [ ] Log security events
- [ ] Review dependencies

### Deployment

- [ ] Use HTTPS everywhere
- [ ] Configure security headers
- [ ] Enable CORS properly
- [ ] Set secure cookie flags
- [ ] Implement rate limiting
- [ ] Configure firewall rules
- [ ] Use latest TLS version
- [ ] Disable unnecessary services
- [ ] Monitor for vulnerabilities
- [ ] Have incident response plan

### Operations

- [ ] Regular security updates
- [ ] Monitor security logs
- [ ] Conduct security training
- [ ] Perform penetration testing
- [ ] Review access controls
- [ ] Audit API usage
- [ ] Update documentation
- [ ] Test backup/recovery
- [ ] Review third-party access
- [ ] Maintain security metrics

## Security Contacts

- **Security Team**: <EMAIL>
- **Incident Response**: <EMAIL>
- **Bug Bounty**: <EMAIL>
- **24/7 Hotline**: +1-XXX-XXX-XXXX

## Conclusion

Security is a continuous process, not a destination. This document will be regularly updated to reflect new threats, technologies, and best practices. All team members are responsible for maintaining the security of our platform and protecting our users' data.