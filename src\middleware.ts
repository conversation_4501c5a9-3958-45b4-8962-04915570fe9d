import { NextRequest, NextResponse } from 'next/server'
import { jwtVerify } from 'jose'

// IMPORTANT: In Edge Runtime, we need to hardcode the secret
// This is a known limitation of Next.js middleware
import { AUTH_CONFIG } from '@/lib/auth-config'

const JWT_SECRET = AUTH_CONFIG.MIDDLEWARE_JWT_SECRET

// Specify protected and public routes
const protectedRoutes = [
  '/dashboard',
  '/api/projects',
  '/api/takeoff',
  '/api/ai',
  '/api/auth/me',
  '/api/auth/signout',
]

const publicRoutes = [
  '/login',
  '/signup',
  '/api/auth/signin',
  '/api/auth/signup',
  '/',
]

interface JWTPayload {
  userId: string
  email: string
  role: string
  sessionId: string
}

export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname
  const isProtectedRoute = protectedRoutes.some(route => path.startsWith(route))
  const isPublicRoute = publicRoutes.some(route => path === route)
  const isApiRoute = path.startsWith('/api/')

  // Get the session token from cookie
  const cookie = req.cookies.get('session')?.value

  // Verify the session
  let isAuthenticated = false
  if (cookie) {
    try {
      // Convert secret to Uint8Array for jose
      const secret = new TextEncoder().encode(JWT_SECRET)

      // Verify JWT token using jose (Edge Runtime compatible)
      const { payload } = await jwtVerify(cookie, secret)

      // Check if token has required fields
      if (payload && payload.userId && payload.email) {
        isAuthenticated = true
      }
    } catch (error) {
      // Token is invalid or expired
      isAuthenticated = false
    }
  }

  // Handle protected routes
  if (isProtectedRoute && !isAuthenticated) {
    // For API routes, return 401 JSON response instead of redirecting
    if (isApiRoute) {
      return NextResponse.json(
        { 
          error: 'Unauthorized', 
          message: 'Authentication required',
          statusCode: 401 
        },
        { status: 401 }
      )
    }
    // For non-API routes, redirect to login
    return NextResponse.redirect(new URL('/login', req.nextUrl))
  }

  // Redirect to /dashboard if the user is authenticated on public routes
  if (
    isPublicRoute &&
    isAuthenticated &&
    !req.nextUrl.pathname.startsWith('/dashboard')
  ) {
    return NextResponse.redirect(new URL('/dashboard', req.nextUrl))
  }

  return NextResponse.next()
}

// Routes Middleware should not run on
export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)',]
}