import type { Redis as RedisType } from 'ioredis';

// Use dynamic import to avoid type issues
let Redis: any;
try {
  Redis = require('ioredis');
} catch {
  Redis = null;
}

declare global {
  var redis: RedisType | undefined;
}

// Create Redis client if REDIS_URL is configured
export const redis = global.redis || (
  process.env.REDIS_URL && Redis ? new Redis(process.env.REDIS_URL, {
    maxRetriesPerRequest: 3,
    retryStrategy: (times: number) => {
      const delay = Math.min(times * 50, 2000);
      return delay;
    },
    reconnectOnError: (err: Error) => {
      const targetError = 'READONLY';
      if (err.message.includes(targetError)) {
        // Only reconnect when the error contains "READONLY"
        return true;
      }
      return false;
    },
  }) : undefined
);

if (process.env.NODE_ENV !== 'production' && redis) {
  global.redis = redis;
}

// Export a ping function for health checks
export async function pingRedis(): Promise<boolean> {
  if (!redis) return false;
  
  try {
    const result = await redis.ping();
    return result === 'PONG';
  } catch {
    return false;
  }
}