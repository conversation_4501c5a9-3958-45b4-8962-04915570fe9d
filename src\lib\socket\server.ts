// Socket.io server setup for BIM collaboration

import { Server as HTTPServer } from 'http'
import { Server as SocketIOServer, Socket } from 'socket.io'
import { 
  ClientToServerEvents, 
  ServerToClientEvents, 
  InterServerEvents, 
  SocketData,
  UserPresence,
  CollaborationRoom,
  Annotation,
  ModelChange,
  SocketEvents,
  ElementSelection,
  BIMViewerState
} from './types'

// Color palette for user cursors/selections
const USER_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8',
  '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8B500', '#6C5CE7'
]

export class BIMCollaborationServer {
  private io: SocketIOServer<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>
  private rooms: Map<string, CollaborationRoom> = new Map()
  private userColorMap: Map<string, string> = new Map()
  private colorIndex = 0

  constructor(httpServer: HTTPServer, corsOrigin?: string) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: corsOrigin || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: Socket<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>) => {
      console.log(`Socket connected: ${socket.id}`)

      // Handle room joining
      socket.on(SocketEvents.JOIN_ROOM, async ({ roomId, userId, userName, userRole }) => {
        try {
          // Store user data on socket
          socket.data.userId = userId
          socket.data.userName = userName
          socket.data.userRole = userRole
          socket.data.roomId = roomId

          // Assign user color
          if (!this.userColorMap.has(userId)) {
            this.userColorMap.set(userId, USER_COLORS[this.colorIndex % USER_COLORS.length])
            this.colorIndex++
          }
          socket.data.color = this.userColorMap.get(userId)!

          // Join socket room
          socket.join(roomId)

          // Get or create collaboration room
          let room = this.rooms.get(roomId)
          if (!room) {
            room = this.createRoom(roomId)
            this.rooms.set(roomId, room)
          }

          // Create user presence
          const userPresence: UserPresence = {
            userId,
            userName,
            userRole,
            color: socket.data.color,
            isActive: true,
            lastActivity: new Date()
          }

          // Add user to room
          const existingUserIndex = room.users.findIndex(u => u.userId === userId)
          if (existingUserIndex >= 0) {
            room.users[existingUserIndex] = userPresence
          } else {
            room.users.push(userPresence)
          }

          // Send room state to joined user
          socket.emit(SocketEvents.ROOM_JOINED, { room })

          // Notify others in room
          socket.to(roomId).emit(SocketEvents.USER_JOINED, { user: userPresence })

          // Send current users list
          this.io.to(roomId).emit(SocketEvents.USERS_LIST, { users: room.users })

        } catch (error) {
          console.error('Error joining room:', error)
          socket.emit(SocketEvents.ERROR, { 
            code: 'JOIN_ROOM_ERROR', 
            message: 'Failed to join collaboration room' 
          })
        }
      })

      // Handle leaving room
      socket.on(SocketEvents.LEAVE_ROOM, ({ roomId, userId }) => {
        this.handleUserLeave(socket, roomId, userId)
      })

      // Handle cursor movement
      socket.on(SocketEvents.CURSOR_MOVE, (cursorData) => {
        const roomId = socket.data.roomId
        if (!roomId) return

        // Broadcast cursor position to others in room
        socket.to(roomId).emit(SocketEvents.CURSOR_UPDATE, {
          cursors: [{ ...cursorData, userId: socket.data.userId }]
        })
      })

      // Handle element selection
      socket.on(SocketEvents.ELEMENT_SELECT, (selectionData) => {
        const roomId = socket.data.roomId
        if (!roomId) return

        const room = this.rooms.get(roomId)
        if (!room) return

        // Update room state
        room.activeSelections.set(socket.data.userId, selectionData)

        // Broadcast to others
        socket.to(roomId).emit(SocketEvents.SELECTION_UPDATE, { selection: selectionData })
      })

      // Handle element deselection
      socket.on(SocketEvents.ELEMENT_DESELECT, ({ userId, elementIds }) => {
        const roomId = socket.data.roomId
        if (!roomId) return

        const room = this.rooms.get(roomId)
        if (!room) return

        // Update selection in room state
        const currentSelection = room.activeSelections.get(userId)
        if (currentSelection) {
          currentSelection.elementIds = currentSelection.elementIds.filter(
            id => !elementIds.includes(id)
          )
          
          if (currentSelection.elementIds.length === 0) {
            room.activeSelections.delete(userId)
            socket.to(roomId).emit(SocketEvents.SELECTION_CLEAR, { userId })
          } else {
            socket.to(roomId).emit(SocketEvents.SELECTION_UPDATE, { selection: currentSelection })
          }
        }
      })

      // Handle annotation creation
      socket.on(SocketEvents.ANNOTATION_CREATE, (annotationData) => {
        const roomId = socket.data.roomId
        if (!roomId) return

        const room = this.rooms.get(roomId)
        if (!room) return

        // Create annotation with generated ID and timestamps
        const annotation: Annotation = {
          ...annotationData,
          id: `ann_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date(),
          updatedAt: new Date()
        }

        // Add to room
        room.annotations.push(annotation)

        // Broadcast to all users in room
        this.io.to(roomId).emit(SocketEvents.ANNOTATIONS_SYNC, { annotations: room.annotations })
      })

      // Handle annotation update
      socket.on(SocketEvents.ANNOTATION_UPDATE, ({ id, updates }) => {
        const roomId = socket.data.roomId
        if (!roomId) return

        const room = this.rooms.get(roomId)
        if (!room) return

        // Find and update annotation
        const annotationIndex = room.annotations.findIndex(a => a.id === id)
        if (annotationIndex >= 0) {
          room.annotations[annotationIndex] = {
            ...room.annotations[annotationIndex],
            ...updates,
            updatedAt: new Date()
          }

          // Broadcast update
          this.io.to(roomId).emit(SocketEvents.ANNOTATIONS_SYNC, { annotations: room.annotations })
        }
      })

      // Handle annotation deletion
      socket.on(SocketEvents.ANNOTATION_DELETE, ({ id, userId }) => {
        const roomId = socket.data.roomId
        if (!roomId) return

        const room = this.rooms.get(roomId)
        if (!room) return

        // Remove annotation
        room.annotations = room.annotations.filter(a => a.id !== id)

        // Broadcast update
        this.io.to(roomId).emit(SocketEvents.ANNOTATIONS_SYNC, { annotations: room.annotations })
      })

      // Handle annotation reply
      socket.on(SocketEvents.ANNOTATION_REPLY, ({ annotationId, reply }) => {
        const roomId = socket.data.roomId
        if (!roomId) return

        const room = this.rooms.get(roomId)
        if (!room) return

        // Find annotation and add reply
        const annotation = room.annotations.find(a => a.id === annotationId)
        if (annotation) {
          const fullReply = {
            ...reply,
            id: `reply_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            createdAt: new Date()
          }

          if (!annotation.replies) {
            annotation.replies = []
          }
          annotation.replies.push(fullReply)
          annotation.updatedAt = new Date()

          // Broadcast update
          this.io.to(roomId).emit(SocketEvents.ANNOTATIONS_SYNC, { annotations: room.annotations })
        }
      })

      // Handle model changes
      socket.on(SocketEvents.MODEL_CHANGE, (changeData) => {
        const roomId = socket.data.roomId
        if (!roomId) return

        const change: ModelChange = {
          ...changeData,
          id: `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date()
        }

        // Broadcast to all users
        this.io.to(roomId).emit(SocketEvents.MODEL_SYNC, { changes: [change] })
      })

      // Handle view state changes
      socket.on(SocketEvents.VIEW_STATE_CHANGE, (viewStateData) => {
        const roomId = socket.data.roomId
        if (!roomId) return

        const room = this.rooms.get(roomId)
        if (!room) return

        // Update user's view state
        const currentState = room.viewStates.get(socket.data.userId) || {} as BIMViewerState
        room.viewStates.set(socket.data.userId, {
          ...currentState,
          ...viewStateData.viewState
        })

        // Convert Map to object for emission
        const viewStatesObj: Record<string, BIMViewerState> = {}
        room.viewStates.forEach((state, userId) => {
          viewStatesObj[userId] = state
        })

        // Broadcast to others
        socket.to(roomId).emit(SocketEvents.VIEW_STATE_SYNC, { viewStates: viewStatesObj })
      })

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`Socket disconnected: ${socket.id}`)
        if (socket.data.roomId && socket.data.userId) {
          this.handleUserLeave(socket, socket.data.roomId, socket.data.userId)
        }
      })
    })
  }

  private createRoom(roomId: string): CollaborationRoom {
    // Parse room ID to extract project and model IDs
    const [projectId, modelId] = roomId.split('_')
    
    return {
      roomId,
      projectId: projectId || roomId,
      modelId: modelId || 'default',
      users: [],
      annotations: [],
      activeSelections: new Map(),
      viewStates: new Map(),
      createdAt: new Date()
    }
  }

  private handleUserLeave(socket: Socket, roomId: string, userId: string) {
    const room = this.rooms.get(roomId)
    if (!room) return

    // Remove user from room
    room.users = room.users.filter(u => u.userId !== userId)
    
    // Clear user's selections
    room.activeSelections.delete(userId)
    
    // Clear user's view state
    room.viewStates.delete(userId)

    // Leave socket room
    socket.leave(roomId)

    // Notify others
    socket.to(roomId).emit(SocketEvents.USER_LEFT, { userId })
    socket.to(roomId).emit(SocketEvents.SELECTION_CLEAR, { userId })
    socket.to(roomId).emit(SocketEvents.USERS_LIST, { users: room.users })

    // Clean up empty rooms
    if (room.users.length === 0) {
      this.rooms.delete(roomId)
    }
  }

  // Public methods for external access
  public getRoomState(roomId: string): CollaborationRoom | undefined {
    return this.rooms.get(roomId)
  }

  public getRoomUsers(roomId: string): UserPresence[] {
    const room = this.rooms.get(roomId)
    return room ? room.users : []
  }

  public broadcastToRoom(roomId: string, event: string, data: any) {
    this.io.to(roomId).emit(event as any, data)
  }

  public getIO(): SocketIOServer {
    return this.io
  }
}

// Singleton instance
let collaborationServer: BIMCollaborationServer | null = null

export function initializeCollaborationServer(httpServer: HTTPServer, corsOrigin?: string): BIMCollaborationServer {
  if (!collaborationServer) {
    collaborationServer = new BIMCollaborationServer(httpServer, corsOrigin)
  }
  return collaborationServer
}

export function getCollaborationServer(): BIMCollaborationServer | null {
  return collaborationServer
}