import { GoogleGenerativeAI, GenerativeModel, ChatSession, Part, HarmCategory, HarmBlockThreshold } from '@google/generative-ai'
import type { AIMessage, Project, SafetyMetrics, Task, Schedule } from '@/types'
import { CompanyType, getCompanyAIContext } from '@/lib/company-types'
import { geminiLogger, logProcessingStep, logError } from '@/lib/services/logger-wrapper'

// Initialize Gemini AI
const apiKey = process.env.GEMINI_API_KEY || ''
const primaryModelName = process.env.GEMINI_MODEL || 'gemini-2.5-flash'

// Model fallback chain - smartest models first (June 2025)
const MODEL_FALLBACK_CHAIN = [
  // Gemini 2.5 models (latest stable versions - no suffix)
  'gemini-2.5-pro',                            // Best model, thinking capabilities, #1 on LMArena
  'gemini-2.5-flash',                          // Best price/performance with thinking
  
  // Gemini 2.0 models (stable fallbacks)
  'gemini-2.0-flash',                          // Current stable with best performance
  'gemini-2.0-pro',                            // Stable pro version
  
  // Gemini 1.5 models (being phased out but still available)
  'gemini-1.5-pro',                            // Pro version with advanced features
  'gemini-1.5-flash',                          // Fast stable version
  
  // Legacy models
  'gemini-1.0-pro'                             // Basic pro model (gemini-pro is alias)
].filter((model, index, self) => {
  // Remove duplicates
  return self.indexOf(model) === index
})

// Add user-specified model only if it's 2.0+
if (primaryModelName.includes('2.0') || primaryModelName.includes('2.5')) {
  MODEL_FALLBACK_CHAIN.push(primaryModelName)
}

if (!apiKey) {
  geminiLogger.warn('Gemini API key not found. AI features will be limited.')
} else {
  geminiLogger.info('Gemini AI initialized', { 
    primaryModel: primaryModelName,
    fallbackModels: MODEL_FALLBACK_CHAIN.slice(1)
  })
}

const genAI = new GoogleGenerativeAI(apiKey)

// Multimodal content types
export interface MultimodalContent {
  text?: string
  images?: Array<{
    data: string // base64 or URL
    mimeType: string
    description?: string
  }>
  documents?: Array<{
    content: string
    type: 'pdf' | 'text' | 'csv'
    name: string
  }>
}

// Base construction system instruction
const BASE_CONSTRUCTION_SYSTEM_INSTRUCTION = `You are an expert AI assistant for construction management. You have deep knowledge of:
- Construction project management and scheduling
- Building Information Modeling (BIM) and 3D visualization
- Construction safety protocols and OSHA regulations
- Cost estimation and budget management
- Progress tracking and quality control
- Construction equipment and materials
- Weather impact on construction activities
- Risk assessment and mitigation strategies
- Construction industry best practices and standards

Your responses should be:
- Professional and construction-industry focused
- Data-driven with specific metrics when possible
- Safety-conscious and regulation-compliant
- Practical and actionable for construction professionals
- Clear and concise, avoiding unnecessary jargon

When analyzing construction data, provide insights on:
- Schedule optimization opportunities
- Cost-saving recommendations
- Safety improvements
- Quality control measures
- Risk mitigation strategies
- Resource allocation efficiency`

// Rate limiting configuration
interface RateLimitConfig {
  maxRequestsPerMinute: number
  maxRequestsPerHour: number
}

// Rate limit tracker
interface RateLimitTracker {
  minuteRequests: { timestamp: number }[]
  hourRequests: { timestamp: number }[]
}

export class GeminiService {
  private models: Map<string, GenerativeModel> = new Map()
  private chatSessions: Map<string, ChatSession> = new Map()
  private rateLimits: RateLimitConfig = {
    maxRequestsPerMinute: 10, // Default for gemini-2.0-flash-exp
    maxRequestsPerHour: 100
  }
  private rateLimitTracker: RateLimitTracker = {
    minuteRequests: [],
    hourRequests: []
  }
  private currentModelIndex: number = 0
  private modelFailureCount: Map<string, number> = new Map()
  private lastModelSwitch: number = 0
  private modelCooldowns: Map<string, number> = new Map() // Track when models can be retried
  private readonly MODEL_COOLDOWN_MS = 5 * 60 * 1000 // 5 minutes cooldown

  constructor() {
    // Initialize with base model
    this.getOrCreateModel(null, MODEL_FALLBACK_CHAIN[0])
    geminiLogger.debug('GeminiService initialized', {
      primaryModel: MODEL_FALLBACK_CHAIN[0],
      fallbackChain: MODEL_FALLBACK_CHAIN
    })
    
    // Adjust rate limits based on initial model
    const initialModel = MODEL_FALLBACK_CHAIN[0]
    if (initialModel.includes('2.5')) {
      // Gemini 2.5 models have good rate limits
      this.rateLimits.maxRequestsPerMinute = 60
      this.rateLimits.maxRequestsPerHour = 1000
    } else if (initialModel.includes('preview') || initialModel.includes('-lite')) {
      this.rateLimits.maxRequestsPerMinute = 60
      this.rateLimits.maxRequestsPerHour = 1000
    } else if (initialModel.includes('pro')) {
      this.rateLimits.maxRequestsPerMinute = 30
      this.rateLimits.maxRequestsPerHour = 500
    } else {
      // Default conservative limits
      this.rateLimits.maxRequestsPerMinute = 15
      this.rateLimits.maxRequestsPerHour = 200
    }
  }

  /**
   * Check rate limits and update tracker
   */
  private checkRateLimit(): { allowed: boolean; retryAfter?: number } {
    const now = Date.now()
    const oneMinuteAgo = now - 60 * 1000
    const oneHourAgo = now - 60 * 60 * 1000
    
    // Clean up old requests
    this.rateLimitTracker.minuteRequests = this.rateLimitTracker.minuteRequests.filter(
      req => req.timestamp > oneMinuteAgo
    )
    this.rateLimitTracker.hourRequests = this.rateLimitTracker.hourRequests.filter(
      req => req.timestamp > oneHourAgo
    )
    
    // Check minute limit
    if (this.rateLimitTracker.minuteRequests.length >= this.rateLimits.maxRequestsPerMinute) {
      const oldestRequest = this.rateLimitTracker.minuteRequests[0]
      const retryAfter = Math.ceil((oldestRequest.timestamp + 60 * 1000 - now) / 1000)
      geminiLogger.warn('Rate limit exceeded (per minute)', {
        current: this.rateLimitTracker.minuteRequests.length,
        limit: this.rateLimits.maxRequestsPerMinute,
        retryAfterSeconds: retryAfter
      })
      return { allowed: false, retryAfter }
    }
    
    // Check hour limit
    if (this.rateLimitTracker.hourRequests.length >= this.rateLimits.maxRequestsPerHour) {
      const oldestRequest = this.rateLimitTracker.hourRequests[0]
      const retryAfter = Math.ceil((oldestRequest.timestamp + 60 * 60 * 1000 - now) / 1000)
      geminiLogger.warn('Rate limit exceeded (per hour)', {
        current: this.rateLimitTracker.hourRequests.length,
        limit: this.rateLimits.maxRequestsPerHour,
        retryAfterSeconds: retryAfter
      })
      return { allowed: false, retryAfter }
    }
    
    return { allowed: true }
  }
  
  /**
   * Track a request for rate limiting
   */
  private trackRequest(): void {
    const now = Date.now()
    this.rateLimitTracker.minuteRequests.push({ timestamp: now })
    this.rateLimitTracker.hourRequests.push({ timestamp: now })
  }

  /**
   * Get current model name
   */
  private getCurrentModelName(): string {
    return MODEL_FALLBACK_CHAIN[this.currentModelIndex]
  }

  /**
   * Check if we can retry a higher-tier model after cooldown
   */
  private checkModelCooldowns(): boolean {
    const now = Date.now()
    
    // Check if any higher-tier models have cooled down
    for (let i = 0; i < this.currentModelIndex; i++) {
      const model = MODEL_FALLBACK_CHAIN[i]
      const cooldownEnd = this.modelCooldowns.get(model) || 0
      
      if (now > cooldownEnd) {
        // This model has cooled down, we can retry it
        this.currentModelIndex = i
        this.lastModelSwitch = now
        
        geminiLogger.info('Retrying higher-tier model after cooldown', {
          model,
          previousIndex: this.currentModelIndex,
          newIndex: i
        })
        
        // Clear existing models to force recreation
        this.models.clear()
        this.chatSessions.clear()
        
        // Update rate limits
        this.updateRateLimitsForModel(model)
        
        return true
      }
    }
    
    return false
  }

  /**
   * Update rate limits based on model
   */
  private updateRateLimitsForModel(model: string): void {
    if (model.includes('2.5')) {
      // Gemini 2.5 models have good rate limits
      this.rateLimits.maxRequestsPerMinute = 60
      this.rateLimits.maxRequestsPerHour = 1000
    } else if (model.includes('preview') || model.includes('-lite')) {
      this.rateLimits.maxRequestsPerMinute = 60
      this.rateLimits.maxRequestsPerHour = 1000
    } else if (model.includes('pro')) {
      this.rateLimits.maxRequestsPerMinute = 30
      this.rateLimits.maxRequestsPerHour = 500
    } else {
      this.rateLimits.maxRequestsPerMinute = 15
      this.rateLimits.maxRequestsPerHour = 200
    }
  }

  /**
   * Switch to next fallback model
   */
  private switchToNextModel(): boolean {
    // First check if we can retry a higher-tier model
    if (this.checkModelCooldowns()) {
      return true
    }
    
    // Otherwise, move to the next fallback
    if (this.currentModelIndex < MODEL_FALLBACK_CHAIN.length - 1) {
      // Mark current model for cooldown
      const currentModel = MODEL_FALLBACK_CHAIN[this.currentModelIndex]
      this.modelCooldowns.set(currentModel, Date.now() + this.MODEL_COOLDOWN_MS)
      
      this.currentModelIndex++
      this.lastModelSwitch = Date.now()
      
      const newModel = MODEL_FALLBACK_CHAIN[this.currentModelIndex]
      geminiLogger.warn('Switching to fallback model', {
        previousModel: currentModel,
        newModel,
        modelIndex: this.currentModelIndex,
        totalModels: MODEL_FALLBACK_CHAIN.length
      })
      
      // Clear existing models to force recreation with new model
      this.models.clear()
      this.chatSessions.clear()
      
      // Update rate limits
      this.updateRateLimitsForModel(newModel)
      
      return true
    }
    
    // If we've exhausted all models, check if we can cycle back to the beginning
    if (this.currentModelIndex === MODEL_FALLBACK_CHAIN.length - 1) {
      const firstModelCooldown = this.modelCooldowns.get(MODEL_FALLBACK_CHAIN[0]) || 0
      if (Date.now() > firstModelCooldown) {
        // Cycle back to the beginning
        this.currentModelIndex = 0
        this.lastModelSwitch = Date.now()
        
        geminiLogger.info('Cycling back to primary model', {
          model: MODEL_FALLBACK_CHAIN[0]
        })
        
        // Clear existing models
        this.models.clear()
        this.chatSessions.clear()
        
        // Update rate limits
        this.updateRateLimitsForModel(MODEL_FALLBACK_CHAIN[0])
        
        return true
      }
    }
    
    return false
  }

  /**
   * Get or create a model with company-specific instructions
   */
  private getOrCreateModel(companyType: CompanyType | null, modelName?: string): GenerativeModel {
    const actualModelName = modelName || this.getCurrentModelName()
    const key = `${companyType || 'base'}-${actualModelName}`
    
    if (!this.models.has(key)) {
      const companyContext = getCompanyAIContext(companyType)
      const systemInstruction = companyContext 
        ? `${BASE_CONSTRUCTION_SYSTEM_INSTRUCTION}\n\n${companyContext}`
        : BASE_CONSTRUCTION_SYSTEM_INSTRUCTION

      const model = genAI.getGenerativeModel({
        model: actualModelName,
        systemInstruction,
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
          responseMimeType: 'text/plain',
        },
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
      })

      this.models.set(key, model)
      
      geminiLogger.debug('Created new model', {
        key,
        modelName: actualModelName,
        hasCompanyContext: !!companyContext,
        temperature: 0.7,
        maxOutputTokens: 8192
      })
    }

    return this.models.get(key)!
  }

  /**
   * Get or create a chat session for a user with company-specific context
   */
  private getChatSession(userId: string, companyType: CompanyType | null): ChatSession {
    const sessionKey = `${userId}-${companyType || 'base'}`
    
    if (!this.chatSessions.has(sessionKey)) {
      const model = this.getOrCreateModel(companyType)
      const chat = model.startChat({
        history: [],
      })
      this.chatSessions.set(sessionKey, chat)
    }
    return this.chatSessions.get(sessionKey)!
  }

  /**
   * Send a message to Gemini and get a response
   */
  async sendMessage(
    message: string,
    userId: string,
    context?: {
      projectData?: Project
      safetyMetrics?: SafetyMetrics
      scheduleData?: Schedule
      attachments?: any[]
      companyType?: CompanyType | null
    }
  ): Promise<string> {
    const startTime = Date.now()
    const messageId = `msg-${Date.now()}`
    
    geminiLogger.info('Processing message', {
      messageId,
      userId,
      messageLength: message.length,
      hasContext: !!context,
      companyType: context?.companyType
    })
    
    // Log warning if company type is missing in critical services
    if (!context?.companyType && ['takeoff', 'material-detection', 'pdf-material-extraction', 'price-intelligence', 'market-analysis'].includes(userId)) {
      geminiLogger.warn('Company type missing for critical AI service', {
        messageId,
        service: userId,
        message: 'AI response may not be tailored to specific contractor type'
      })
    }
    
    // Check rate limits before proceeding
    const rateLimitCheck = this.checkRateLimit()
    if (!rateLimitCheck.allowed) {
      geminiLogger.warn('Rate limit reached, waiting before retry', {
        messageId,
        retryAfterSeconds: rateLimitCheck.retryAfter
      })
      return `I'm currently rate limited. Please try again in ${rateLimitCheck.retryAfter} seconds.`
    }
    
    // Retry logic with model fallback
    let lastError: any = null
    let retryCount = 0
    const maxRetries = 3
    
    while (retryCount < maxRetries) {
      try {
        const companyType = context?.companyType || null
        const currentModel = this.getCurrentModelName()
        
        geminiLogger.debug('Attempting request', {
          messageId,
          model: currentModel,
          retryCount,
          modelIndex: this.currentModelIndex
        })
        
        // Track the request
        this.trackRequest()
        
        const chat = this.getChatSession(userId, companyType)
        
        // Build context-aware prompt
        let enhancedPrompt = message
        
        if (context) {
          enhancedPrompt = this.buildContextualPrompt(message, context)
          logProcessingStep(geminiLogger, 'Built contextual prompt', {
            messageId,
            originalLength: message.length,
            enhancedLength: enhancedPrompt.length
          }, startTime)
        }

        const result = await chat.sendMessage(enhancedPrompt)
        const response = await result.response
        const responseText = response.text()
        
        geminiLogger.info('Message processed successfully', {
          messageId,
          model: currentModel,
          responseLength: responseText.length,
          processingTime: Date.now() - startTime,
          retryCount
        })
        
        // Reset failure count on success
        this.modelFailureCount.set(currentModel, 0)
        
        return responseText
      } catch (error: any) {
        lastError = error
        retryCount++
        
        const currentModel = this.getCurrentModelName()
        const failureCount = (this.modelFailureCount.get(currentModel) || 0) + 1
        this.modelFailureCount.set(currentModel, failureCount)
        
        logError(geminiLogger, error, 'Request failed', {
          messageId,
          userId,
          model: currentModel,
          retryCount,
          failureCount,
          processingTime: Date.now() - startTime
        })
        
        // Check if it's a quota error
        if (error.message?.includes('quota') || error.message?.includes('429')) {
          // Try switching to next model
          if (this.switchToNextModel()) {
            geminiLogger.info('Switched to fallback model due to quota error', {
              messageId,
              newModel: this.getCurrentModelName()
            })
            retryCount = 0 // Reset retry count for new model
            continue
          }
        }
        
        // Implement exponential backoff
        if (retryCount < maxRetries) {
          const backoffMs = Math.min(1000 * Math.pow(2, retryCount), 10000)
          geminiLogger.debug('Implementing backoff before retry', {
            messageId,
            backoffMs,
            retryCount
          })
          await new Promise(resolve => setTimeout(resolve, backoffMs))
          
          // Try switching model after 2 failures
          if (retryCount >= 2 && this.switchToNextModel()) {
            geminiLogger.info('Switched to fallback model after retries', {
              messageId,
              newModel: this.getCurrentModelName()
            })
            retryCount = 0 // Reset retry count for new model
          }
        }
      }
    }
    
    // All retries exhausted
    logError(geminiLogger, lastError, 'All retries exhausted', {
      messageId,
      userId,
      totalRetries: retryCount,
      processingTime: Date.now() - startTime,
      currentModel: this.getCurrentModelName()
    })
    
    return this.handleError(lastError)
  }

  /**
   * Send multimodal content (text + images/documents) to Gemini
   */
  async sendMultimodalMessage(
    content: MultimodalContent,
    userId: string,
    context?: {
      projectData?: Project
      safetyMetrics?: SafetyMetrics
      scheduleData?: Schedule
      companyType?: CompanyType | null
    }
  ): Promise<string> {
    try {
      const companyType = context?.companyType || null
      const model = this.getOrCreateModel(companyType)
      
      // Build multimodal parts
      const parts: Part[] = []
      
      // Add text part with context
      if (content.text) {
        const enhancedText = context 
          ? this.buildContextualPrompt(content.text, context)
          : content.text
        parts.push({ text: enhancedText })
      }
      
      // Add image parts
      if (content.images) {
        for (const image of content.images) {
          parts.push({
            inlineData: {
              data: image.data,
              mimeType: image.mimeType
            }
          })
          
          if (image.description) {
            parts.push({ text: `Image description: ${image.description}` })
          }
        }
      }
      
      // Add document content
      if (content.documents) {
        for (const doc of content.documents) {
          parts.push({ 
            text: `Document: ${doc.name} (${doc.type})\nContent:\n${doc.content}` 
          })
        }
      }
      
      // Generate content with all parts
      const result = await model.generateContent(parts)
      const response = await result.response
      return response.text()
    } catch (error) {
      console.error('Multimodal Gemini API error:', error)
      return this.handleError(error)
    }
  }

  /**
   * Analyze construction drawings using multimodal AI
   */
  async analyzeDrawing(
    imageData: string,
    mimeType: string,
    analysisType: 'takeoff' | 'safety' | 'progress' | 'quality',
    context?: {
      projectData?: Project
      drawingType?: string
      specifications?: any
      companyType?: CompanyType | null
    }
  ): Promise<any> {
    const prompts = {
      takeoff: `Analyze this construction drawing for quantity takeoff:
1. Identify all materials and components
2. Extract dimensions and measurements
3. Calculate quantities for each material
4. Note any specifications or grades
5. Identify areas requiring clarification
6. Suggest any missing items based on typical construction

Provide a detailed breakdown with material types, quantities, units, and confidence levels.`,
      
      safety: `Analyze this construction drawing for safety concerns:
1. Identify potential safety hazards
2. Check for missing safety features
3. Verify compliance with OSHA standards
4. Highlight high-risk areas
5. Suggest safety improvements
6. Note any access or egress issues

Provide specific safety recommendations with risk levels.`,
      
      progress: `Analyze this construction photo/drawing for progress tracking:
1. Identify completed work elements
2. Compare with planned progress
3. Estimate completion percentage
4. Note any deviations from plans
5. Identify quality issues
6. Suggest areas needing attention

Provide progress percentages by trade/area.`,
      
      quality: `Analyze this construction image for quality control:
1. Identify visible defects or issues
2. Check alignment and dimensions
3. Verify material specifications
4. Note workmanship quality
5. Identify non-conformances
6. Suggest corrective actions

Provide quality scores and specific defect locations.`
    }
    
    try {
      const model = this.getOrCreateModel(context?.companyType || null)
      
      // Add drawing context if provided
      let fullPrompt = prompts[analysisType]
      if (context?.drawingType) {
        fullPrompt = `Drawing Type: ${context.drawingType}\n\n${fullPrompt}`
      }
      if (context?.specifications) {
        fullPrompt += `\n\nProject Specifications:\n${JSON.stringify(context.specifications, null, 2)}`
      }
      
      const parts: Part[] = [
        { text: fullPrompt },
        {
          inlineData: {
            data: imageData,
            mimeType: mimeType
          }
        }
      ]
      
      const result = await model.generateContent(parts)
      const response = await result.response
      return this.parseDrawingAnalysis(response.text(), analysisType)
    } catch (error) {
      console.error(`Drawing analysis error (${analysisType}):`, error)
      throw error
    }
  }

  /**
   * Generate content with visual context
   */
  async generateVisualContent(
    contentType: 'estimate' | 'report' | 'proposal',
    data: any,
    images?: Array<{ data: string; mimeType: string; caption?: string }>,
    companyType?: CompanyType | null
  ): Promise<string> {
    const templates = {
      estimate: `Based on the provided drawings and specifications, generate a detailed cost estimate including:
1. Material quantities and costs
2. Labor requirements and costs
3. Equipment needs
4. Overhead and profit margins
5. Contingencies
6. Exclusions and clarifications

Data: ${JSON.stringify(data, null, 2)}`,
      
      report: `Create a comprehensive progress report based on the site photos and data:
1. Executive summary
2. Progress by area/trade
3. Schedule status
4. Budget status
5. Quality observations
6. Safety observations
7. Issues and risks
8. Recommendations

Data: ${JSON.stringify(data, null, 2)}`,
      
      proposal: `Generate a professional proposal based on the project drawings:
1. Scope of work
2. Methodology
3. Timeline
4. Cost breakdown
5. Terms and conditions
6. Value propositions
7. Company qualifications

Data: ${JSON.stringify(data, null, 2)}`
    }
    
    try {
      const model = this.getOrCreateModel(companyType || null)
      const parts: Part[] = [{ text: templates[contentType] }]
      
      // Add images with captions
      if (images) {
        for (const image of images) {
          if (image.caption) {
            parts.push({ text: `Image: ${image.caption}` })
          }
          parts.push({
            inlineData: {
              data: image.data,
              mimeType: image.mimeType
            }
          })
        }
      }
      
      const result = await model.generateContent(parts)
      const response = await result.response
      return response.text()
    } catch (error) {
      console.error(`Visual content generation error (${contentType}):`, error)
      throw error
    }
  }

  /**
   * Parse drawing analysis response
   */
  private parseDrawingAnalysis(responseText: string, analysisType: string): any {
    try {
      // Try to extract JSON if the response contains it
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Otherwise, structure the response based on type
      const lines = responseText.split('\n').filter(line => line.trim());
      const result: any = {
        type: analysisType,
        timestamp: new Date(),
        findings: []
      };
      
      let currentSection: { title: string; details: string[] } | null = null;
      lines.forEach(line => {
        if (line.match(/^\d+\./)) {
          currentSection = {
            title: line.replace(/^\d+\.\s*/, ''),
            details: []
          };
          result.findings.push(currentSection);
        } else if (currentSection && line.trim()) {
          currentSection.details.push(line.trim());
        }
      });
      
      return result;
    } catch (error) {
      console.warn('Failed to parse structured response, returning raw text');
      return {
        type: analysisType,
        timestamp: new Date(),
        rawResponse: responseText
      };
    }
  }

  /**
   * Build a contextual prompt with project data
   */
  private buildContextualPrompt(
    message: string,
    context: {
      projectData?: Project
      safetyMetrics?: SafetyMetrics
      scheduleData?: Schedule
      attachments?: any[]
    }
  ): string {
    let contextInfo = ''

    if (context.projectData) {
      const project = context.projectData
      contextInfo += `
Current Project Context:
- Project: ${project.name}
- Type: ${project.projectType || 'Not specified'}
- Status: ${project.status}
- Progress: ${project.progressPercentage || 0}%
- Budget: $${project.budget.toLocaleString()} (Spent: $${(project.actualCost || 0).toLocaleString()})
- Schedule: ${new Date(project.startDate).toLocaleDateString()} to ${new Date(project.endDate).toLocaleDateString()}
- Safety Score: ${project.safetyScore}/100
- Risk Score: ${project.riskScore}/100
`
    }

    if (context.safetyMetrics) {
      const safety = context.safetyMetrics
      contextInfo += `
Safety Metrics:
- Total Incidents: ${safety.totalIncidents}
- Lost Time Incidents: ${safety.lostTimeIncidents}
- Near Misses: ${safety.nearMisses}
- PPE Compliance: ${safety.ppeCompliance}%
- Hazards Identified: ${safety.hazardsIdentified} (Resolved: ${safety.hazardsResolved})
`
    }

    if (context.scheduleData) {
      const schedule = context.scheduleData
      contextInfo += `
Schedule Information:
- Task: ${schedule.taskName}
- Status: ${schedule.status}
- Priority: ${schedule.priority}
- Schedule: ${new Date(schedule.startDate).toLocaleDateString()} to ${new Date(schedule.endDate).toLocaleDateString()}
`
    }

    return contextInfo ? `${contextInfo}\n\nUser Question: ${message}` : message
  }

  /**
   * Handle specific construction-related queries
   */
  async analyzeConstructionData(
    queryType: 'progress' | 'safety' | 'schedule' | 'cost' | 'quality',
    data: any,
    companyType?: CompanyType | null
  ): Promise<any> {
    const model = this.getOrCreateModel(companyType || null)
    const prompts = {
      progress: `Analyze the following construction progress data and provide insights on:
1. Overall progress status and trends
2. Areas ahead or behind schedule
3. Potential bottlenecks or delays
4. Recommendations for acceleration
5. Resource reallocation opportunities

Data: ${JSON.stringify(data)}`,

      safety: `Analyze the following construction safety data and provide:
1. Safety performance assessment
2. High-risk areas or activities
3. Compliance issues
4. Predictive risk analysis
5. Specific safety improvement recommendations

Data: ${JSON.stringify(data)}`,

      schedule: `Analyze the following construction schedule and provide:
1. Critical path analysis
2. Schedule optimization opportunities
3. Resource leveling recommendations
4. Weather impact assessment
5. Acceleration strategies with cost implications

Data: ${JSON.stringify(data)}`,

      cost: `Analyze the following construction cost data and provide:
1. Budget performance analysis
2. Cost overrun areas and causes
3. Value engineering opportunities
4. Cost reduction strategies
5. Financial risk assessment

Data: ${JSON.stringify(data)}`,

      quality: `Analyze the following construction quality data and provide:
1. Quality metrics assessment
2. Non-conformance patterns
3. Root cause analysis
4. Quality improvement recommendations
5. Preventive measures

Data: ${JSON.stringify(data)}`,
    }

    try {
      const prompt = prompts[queryType]
      const result = await model.generateContent(prompt)
      const response = await result.response
      return this.parseAnalysisResponse(response.text(), queryType)
    } catch (error) {
      console.error(`Error analyzing ${queryType} data:`, error)
      throw error
    }
  }

  /**
   * Parse analysis response into structured format
   */
  private parseAnalysisResponse(text: string, queryType: string): any {
    // Basic parsing - in production, this would be more sophisticated
    const sections = text.split('\n\n')
    const insights: string[] = []
    const recommendations: string[] = []
    const metrics: Record<string, any> = {}

    sections.forEach(section => {
      if (section.includes('Recommendation') || section.includes('recommendation')) {
        recommendations.push(section)
      } else if (section.includes('%') || section.includes('$')) {
        // Extract metrics
        const matches = section.match(/(\d+(?:\.\d+)?%?|\$[\d,]+(?:\.\d+)?)/g)
        if (matches) {
          metrics[queryType] = matches
        }
      } else {
        insights.push(section)
      }
    })

    return {
      queryType,
      insights,
      recommendations,
      metrics,
      rawResponse: text,
    }
  }

  /**
   * Generate construction-specific content
   */
  async generateContent(
    contentType: 'safety_report' | 'progress_report' | 'risk_assessment' | 'schedule_optimization',
    data: any,
    companyType?: CompanyType | null
  ): Promise<string> {
    const model = this.getOrCreateModel(companyType || null)
    const templates = {
      safety_report: `Generate a comprehensive construction safety report including:
1. Executive Summary
2. Incident Analysis
3. Hazard Identification
4. Compliance Status
5. Training Recommendations
6. Action Items

Based on: ${JSON.stringify(data)}`,

      progress_report: `Generate a detailed construction progress report including:
1. Overall Project Status
2. Milestone Achievement
3. Schedule Performance
4. Budget Status
5. Key Issues and Risks
6. Next Period Outlook

Based on: ${JSON.stringify(data)}`,

      risk_assessment: `Generate a construction risk assessment including:
1. Risk Identification
2. Risk Analysis (probability x impact)
3. Risk Prioritization
4. Mitigation Strategies
5. Contingency Plans
6. Monitoring Recommendations

Based on: ${JSON.stringify(data)}`,

      schedule_optimization: `Generate schedule optimization recommendations including:
1. Critical Path Analysis
2. Resource Optimization
3. Parallel Activity Opportunities
4. Time-Cost Trade-offs
5. Implementation Plan
6. Expected Benefits

Based on: ${JSON.stringify(data)}`,
    }

    try {
      const prompt = templates[contentType]
      const result = await model.generateContent(prompt)
      const response = await result.response
      return response.text()
    } catch (error) {
      console.error(`Error generating ${contentType}:`, error)
      throw error
    }
  }

  /**
   * Handle errors gracefully
   */
  private handleError(error: any): string {
    if (error.message?.includes('API key')) {
      return 'I apologize, but I need a valid API key to provide AI-powered insights. Please configure your Gemini API key in the environment settings.'
    } else if (error.message?.includes('quota')) {
      return 'I\'ve reached the API quota limit. Please try again later or upgrade your API plan for continued service.'
    } else if (error.message?.includes('timeout')) {
      return 'The request took too long to process. Please try again with a simpler query or check your internet connection.'
    } else {
      return 'I encountered an error while processing your request. Please try again or contact support if the issue persists.'
    }
  }

  /**
   * Clear chat history for a user
   */
  clearChatHistory(userId: string, companyType?: CompanyType | null): void {
    if (companyType !== undefined) {
      const sessionKey = `${userId}-${companyType || 'base'}`
      this.chatSessions.delete(sessionKey)
    } else {
      // Clear all sessions for this user
      const keysToDelete = Array.from(this.chatSessions.keys()).filter(key => key.startsWith(`${userId}-`))
      keysToDelete.forEach(key => this.chatSessions.delete(key))
    }
  }

  /**
   * Get chat history for a user
   */
  getChatHistory(userId: string): any[] {
    const session = this.chatSessions.get(userId)
    if (!session) return []
    
    // Note: The actual implementation would need to track history manually
    // as the Gemini SDK doesn't expose chat history directly
    return []
  }
}

// Export singleton instance
export const geminiService = new GeminiService()
