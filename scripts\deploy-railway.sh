#!/bin/bash

# Railway Deployment Script for AI Construction Management Platform
# This script deploys the application to Railway

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting Railway deployment...${NC}"

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo -e "${RED}Railway CLI is not installed. Please install it first:${NC}"
    echo -e "${YELLOW}npm install -g @railway/cli${NC}"
    exit 1
fi

# Create Railway configuration
echo -e "${YELLOW}Creating Railway configuration...${NC}"
cat > railway.toml <<EOF
[build]
builder = "NIXPACKS"
buildCommand = "npm ci && npx prisma generate && npm run build"

[deploy]
startCommand = "npm start"
healthcheckPath = "/api/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 3

[[services]]
name = "web"
port = 3000

[services.web]
environmentVariables = [
  "NODE_ENV=production",
  "PORT=3000"
]
EOF

# Create nixpacks configuration
cat > nixpacks.toml <<EOF
[phases.setup]
nixPkgs = ["nodejs-20_x", "npm-9_x"]

[phases.install]
cmds = ["npm ci"]

[phases.build]
cmds = ["npx prisma generate", "npm run build"]

[start]
cmd = "npm start"
EOF

# Link to Railway project
echo -e "${YELLOW}Linking to Railway project...${NC}"
railway link

# Deploy to Railway
echo -e "${YELLOW}Deploying to Railway...${NC}"
railway up

# Set environment variables
echo -e "${YELLOW}Setting environment variables...${NC}"
railway variables set NODE_ENV=production
railway variables set PORT=3000

# Deploy database if needed
echo -e "${YELLOW}Do you want to provision a PostgreSQL database? (y/n)${NC}"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    railway add postgresql
    echo -e "${GREEN}PostgreSQL database provisioned!${NC}"
    echo -e "${YELLOW}Running database migrations...${NC}"
    railway run npx prisma migrate deploy
fi

# Deploy Redis if needed
echo -e "${YELLOW}Do you want to provision a Redis instance? (y/n)${NC}"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    railway add redis
    echo -e "${GREEN}Redis instance provisioned!${NC}"
fi

# Get deployment info
echo -e "${YELLOW}Getting deployment information...${NC}"
DEPLOYMENT_URL=$(railway status --json | jq -r '.deployment.url')

# Health check
echo -e "${YELLOW}Waiting for deployment to be ready...${NC}"
sleep 30

echo -e "${YELLOW}Running health check...${NC}"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://${DEPLOYMENT_URL}/api/health")

if [ "$HTTP_STATUS" == "200" ]; then
    echo -e "${GREEN}Health check passed!${NC}"
else
    echo -e "${RED}Health check failed with status: ${HTTP_STATUS}${NC}"
    echo -e "${YELLOW}Check logs with: railway logs${NC}"
fi

# Cleanup
rm -f railway.toml nixpacks.toml

echo -e "${GREEN}Railway deployment completed!${NC}"
echo -e "${GREEN}Visit your deployment at: https://${DEPLOYMENT_URL}${NC}"
echo -e "${YELLOW}View logs: railway logs${NC}"
echo -e "${YELLOW}Open dashboard: railway open${NC}"