// Core types for AI Construction Management Platform

// Import standardized UserRole from centralized location
import { UserRole } from '@/lib/user-role-types'

export interface User {
  id: string
  email: string
  name: string
  passwordHash?: string
  role: UserRole
  companyType: string
  companyName?: string
  emailVerified: boolean
  avatar?: string
  lastLogin?: Date
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Re-export UserRole for convenience
export type { UserRole }

// Session model interface
export interface Session {
  id: string
  sessionToken: string
  userId: string
  expires: Date
  user?: User
}

// AI Chat model interface
export interface AIChat {
  id: string
  userId: string
  user?: User
  role: 'user' | 'assistant'
  content: string
  projectId?: string
  context?: string
  createdAt: Date
}

// Processing Job model interface
export interface ProcessingJob {
  id: string
  userId: string
  user?: User
  projectId?: string
  project?: Project
  status: 'pending' | 'processing' | 'completed' | 'failed'
  totalFiles: number
  processedFiles: number
  failedFiles: number
  startedAt?: Date
  completedAt?: Date
  companyType?: string
  projectContext: string // JSON string
  totalItems: number
  totalCost: number
  createdAt: Date
  updatedAt: Date
  files?: ProcessingFile[]
  takeoffs?: Takeoff[]
}

// Processing File model interface
export interface ProcessingFile {
  id: string
  jobId: string
  job?: ProcessingJob
  fileName: string
  fileSize: number
  mimeType: string
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed'
  progress: number
  storageUrl?: string
  thumbnailUrl?: string
  itemsDetected: number
  processingTime?: number
  errorMessage?: string
  createdAt: Date
  updatedAt: Date
}

// Activity model interface
export interface Activity {
  id: string
  userId: string
  user?: User
  projectId?: string
  project?: Project
  action: string
  description: string
  metadata?: string // JSON string
  createdAt: Date
}

export interface Project {
  id: string
  name: string
  description?: string
  startDate: Date
  endDate: Date
  budget: number
  status: string // Matches Prisma schema
  userId: string
  user?: User
  createdAt: Date
  updatedAt: Date
  // Construction-specific fields
  projectType: string
  location: string
  siteArea?: number
  constructionType?: string
  // Computed/additional fields for frontend use
  actualEndDate?: Date
  actualCost?: number
  progressPercentage?: number
  riskScore?: number
  safetyScore?: number
  // Relations
  schedules?: Schedule[]
  safety?: SafetyReport[]
  progressReports?: ProgressReport[]
  takeoffs?: Takeoff[]
  documents?: Document[]
  activities?: Activity[]
  processingJobs?: ProcessingJob[]
}

// Keep these as type aliases for backward compatibility and type safety
export type ProjectType = 'commercial' | 'residential' | 'infrastructure' | 'industrial' | 'institutional'
export type ProjectStatus = 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled'

// Location interface for structured location data
export interface Location {
  address: string
  city: string
  state: string
  country: string
  zipCode: string
  coordinates: {
    lat: number
    lng: number
  }
}

// Scheduling Types
export interface Schedule {
  id: string
  projectId: string
  project?: Project
  taskName: string
  description?: string
  startDate: Date
  endDate: Date
  status: string // Defaults to "NOT_STARTED" in Prisma
  priority: string // Defaults to "MEDIUM" in Prisma
  dependencies: string // Comma-separated list
  assignedTo: string // Comma-separated list
  createdAt: Date
  updatedAt: Date
}

export interface Task {
  id: string
  scheduleId: string
  name: string
  description?: string
  type: TaskType
  status: TaskStatus
  plannedStartDate: Date
  plannedEndDate: Date
  actualStartDate?: Date
  actualEndDate?: Date
  duration: number
  progress: number
  dependencies: string[]
  resources: Resource[]
  constraints: Constraint[]
  riskFactors: RiskFactor[]
}

export type TaskType = 'milestone' | 'work_package' | 'activity'
export type TaskStatus = 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'blocked'

export interface Resource {
  id: string
  type: 'labor' | 'equipment' | 'material'
  name: string
  quantity: number
  cost: number
  availability: Availability[]
}

export interface Availability {
  startDate: Date
  endDate: Date
  capacity: number
}

export interface Constraint {
  type: 'date' | 'resource' | 'dependency' | 'weather'
  description: string
  impact: 'low' | 'medium' | 'high'
}

export interface RiskFactor {
  id: string
  description: string
  probability: number
  impact: number
  mitigation: string
}

// Progress Tracking Types
export interface ProgressCapture {
  id: string
  projectId: string
  captureDate: Date
  type: 'photo_360' | 'drone' | 'lidar' | 'manual'
  location: CaptureLocation
  data: CaptureData
  aiAnalysis: AIAnalysis
  createdBy: string
  createdAt: Date
}

export interface CaptureLocation {
  zone: string
  floor?: number
  coordinates?: {
    x: number
    y: number
    z: number
  }
}

export interface CaptureData {
  url: string
  format: string
  size: number
  metadata: Record<string, any>
}

export interface AIAnalysis {
  elementsDetected: DetectedElement[]
  progressPercentage: number
  qualityScore: number
  deviations: Deviation[]
  recommendations: string[]
}

export interface DetectedElement {
  type: string
  confidence: number
  location: BoundingBox
  status: 'planned' | 'in_progress' | 'completed'
  matchesBIM: boolean
}

export interface DetectedMaterial {
  name: string;
  type: string;
  category: string;
  description?: string;
  quantity: number;
  unit: string;
  specifications?: string;
  location?: string;
  confidence: number;
  source?: string;
  pageNumber?: number;
}

export interface BoundingBox {
  x: number
  y: number
  width: number
  height: number
}

export interface Deviation {
  type: 'missing' | 'incorrect' | 'damaged'
  severity: 'low' | 'medium' | 'high'
  description: string
  element: DetectedElement
}

// Safety Types
export interface SafetyIncident {
  id: string
  projectId: string
  type: IncidentType
  severity: 'minor' | 'moderate' | 'severe' | 'critical'
  description: string
  location: string
  dateTime: Date
  reportedBy: string
  involvedPersonnel: string[]
  rootCause?: string
  correctiveActions: string[]
  status: 'open' | 'investigating' | 'resolved' | 'closed'
  aiDetected: boolean
  createdAt: Date
  updatedAt: Date
}

export type IncidentType = 'injury' | 'near_miss' | 'property_damage' | 'environmental' | 'violation'

export interface SafetyObservation {
  id: string
  projectId: string
  type: 'positive' | 'negative'
  category: SafetyCategory
  description: string
  location: string
  imageUrl?: string
  aiDetected: boolean
  confidence?: number
  createdBy: string
  createdAt: Date
}

export type SafetyCategory = 'ppe' | 'fall_hazard' | 'equipment' | 'housekeeping' | 'procedure' | 'other'

export interface SafetyMetrics {
  projectId: string
  totalIncidents: number
  lostTimeIncidents: number
  nearMisses: number
  safetyScore: number
  ppeCompliance: number
  hazardsIdentified: number
  hazardsResolved: number
  trainingCompletion: number
  lastUpdated: Date
}

// Safety Report model from Prisma
export interface SafetyReport {
  id: string
  projectId: string
  project?: Project
  reportDate: Date
  score: number // 0-100
  violations: string // JSON string
  incidents: string // JSON string
  ppeCompliance: number
  observations: string
  recommendations: string // Comma-separated list
  createdAt: Date
}

// Progress Report model from Prisma
export interface ProgressReport {
  id: string
  projectId: string
  project?: Project
  reportDate: Date
  completion: number // 0-100
  milestones: string // JSON string
  issues: string // JSON string
  photoUrls: string // Comma-separated list
  notes?: string
  weatherConditions?: string
  workersOnSite?: number
  createdAt: Date
}

// Takeoff & Estimating Types
export interface Takeoff {
  id: string
  projectId: string
  project?: Project
  userId: string
  user?: User
  drawingUrl?: string
  status: string // defaults to "processing" in Prisma
  totalCost?: number
  confidence?: number
  items?: TakeoffItem[]
  processingJob?: ProcessingJob
  processingJobId?: string
  createdAt: Date
  updatedAt: Date
}

export interface TakeoffItem {
  id: string
  category: string
  description: string
  quantity: number
  unit: string
  unitCost: number
  totalCost: number
  aiDetected: boolean
  confidence?: number
  location?: string
  specifications?: string
  materialCost?: number
  laborCost?: number
  equipmentCost?: number
  priceSource?: 'rsmeans' | 'market' | 'estimated'
  validationNotes?: string
  notes?: string
  sourceFile?: string
  sourceFileIndex?: number
  materialType?: string
  supplier?: string
  leadTime?: number
}

export interface Estimate {
  id: string
  projectId: string
  takeoffId: string
  name: string
  version: number
  status: 'draft' | 'submitted' | 'approved' | 'rejected'
  laborCost: number
  materialCost: number
  equipmentCost: number
  subcontractorCost: number
  overhead: number
  profit: number
  totalCost: number
  contingency: number
  notes?: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

// Processed Drawing Type for Vision Analysis
export interface ProcessedDrawing {
  id: string
  fileName: string
  pages: Array<{
    pageNumber: number
    imageUrl: string
    analysis: any // Vision analysis result
  }>
  materials: Array<{
    type: string
    name: string
    confidence: number
    location: any
    specifications?: string
    quantity?: number
    unit?: string
  }>
  totalConfidence: number
  processingTime: number
  timestamp: Date
}

// BIM Model Types
export interface BIMModel {
  id: string
  projectId: string
  name: string
  format: 'ifc' | 'gltf' | 'obj' | 'fbx'
  url: string
  size: number
  elements: BIMElement[]
  metadata: BIMMetadata
  createdAt: Date
  updatedAt: Date
}

export interface BIMElement {
  id: string
  guid: string
  type: string
  name: string
  properties: Record<string, any>
  geometry?: BIMGeometry
  material?: string
  parent?: string
  children: string[]
  ifcType?: string
  progress?: number
  issues?: string[]
  position?: { x: number; y: number; z: number }
  floor?: number
}

export interface BIMGeometry {
  vertices: number[]
  faces: number[]
  normals?: number[]
  uvs?: number[]
}

export interface BIMMetadata {
  author: string
  software: string
  version: string
  units: 'metric' | 'imperial'
  coordinates?: {
    lat: number
    lng: number
  }
  buildingType?: string
  levels?: number
}

export interface BIMViewerState {
  selectedElements: string[]
  hiddenElements: string[]
  viewMode: '3D' | 'floor' | 'section' | 'elevation'
  cameraPosition: { x: number; y: number; z: number }
  cameraTarget: { x: number; y: number; z: number }
  activeFloor?: number
  clippingPlanes?: ClippingPlane[]
}

export interface ClippingPlane {
  id: string
  normal: { x: number; y: number; z: number }
  position: { x: number; y: number; z: number }
  active: boolean
}

// Field Operations Types
export interface FieldLayout {
  id: string
  projectId: string
  zone: string
  floor?: number
  type: 'walls' | 'mep' | 'equipment' | 'safety'
  status: 'pending' | 'printed' | 'verified'
  printDate?: Date
  verifiedBy?: string
  deviations: LayoutDeviation[]
  createdAt: Date
}

export interface LayoutDeviation {
  type: 'missing' | 'incorrect_position' | 'wrong_dimension'
  description: string
  location: {
    x: number
    y: number
  }
  resolution?: string
}

// AI Assistant Types
export interface AIConversation {
  id: string
  userId: string
  projectId?: string
  messages: AIMessage[]
  context: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface AIMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  attachments?: Attachment[]
  toolCalls?: ToolCall[]
  timestamp: Date
}

export interface Attachment {
  type: 'image' | 'document' | 'drawing' | 'schedule'
  url: string
  name: string
}

export interface ToolCall {
  tool: string
  parameters: Record<string, any>
  result: any
}

// Analytics Types
export interface ProjectAnalytics {
  projectId: string
  kpis: KPI[]
  trends: Trend[]
  predictions: Prediction[]
  benchmarks: Benchmark[]
  lastUpdated: Date
}

export interface KPI {
  name: string
  value: number
  target: number
  unit: string
  trend: 'up' | 'down' | 'stable'
  status: 'on_track' | 'at_risk' | 'off_track'
}

export interface Trend {
  metric: string
  dataPoints: DataPoint[]
  forecast: DataPoint[]
}

export interface DataPoint {
  date: Date
  value: number
}

export interface Prediction {
  type: 'completion_date' | 'cost_overrun' | 'safety_incident' | 'weather_delay'
  probability: number
  impact: string
  recommendedAction: string
}

export interface Benchmark {
  metric: string
  projectValue: number
  industryAverage: number
  percentile: number
}

// Real-time collaboration types
export interface Notification {
  id: string
  userId: string
  type: NotificationType
  title: string
  message: string
  relatedId?: string
  relatedType?: string
  read: boolean
  createdAt: Date
}

export type NotificationType = 
  | 'task_assigned' 
  | 'schedule_change' 
  | 'safety_alert' 
  | 'progress_update' 
  | 'ai_insight' 
  | 'approval_required'
  | 'comment_mention'

export interface Comment {
  id: string
  entityId: string
  entityType: string
  userId: string
  content: string
  attachments?: Attachment[]
  mentions: string[]
  createdAt: Date
  updatedAt: Date
}

// Weather Integration
export interface WeatherForecast {
  date: Date
  condition: WeatherCondition
  temperature: {
    min: number
    max: number
  }
  precipitation: number
  windSpeed: number
  impact: WeatherImpact[]
}

export type WeatherCondition = 'clear' | 'cloudy' | 'rain' | 'storm' | 'snow' | 'extreme'

export interface WeatherImpact {
  activity: string
  severity: 'low' | 'medium' | 'high'
  recommendation: string
}

// Document Management
export interface Document {
  id: string
  projectId: string
  project?: Project
  name: string
  type: string
  url: string
  size: number
  uploadedBy: string
  version: number
  tags: string // Comma-separated list
  createdAt: Date
  updatedAt: Date
}

export type DocumentType = 
  | 'drawing' 
  | 'specification' 
  | 'contract' 
  | 'rfi' 
  | 'submittal' 
  | 'report' 
  | 'photo' 
  | 'other'

export interface Permission {
  userId: string
  level: 'view' | 'comment' | 'edit' | 'admin'
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: ApiError
  pagination?: Pagination
}

export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
}

export interface Pagination {
  page: number
  pageSize: number
  total: number
  totalPages: number
}

// Form Types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'number' | 'date' | 'select' | 'multiselect' | 'textarea' | 'file'
  required: boolean
  options?: SelectOption[]
  validation?: ValidationRule[]
}

export interface SelectOption {
  value: string
  label: string
}

export interface ValidationRule {
  type: 'min' | 'max' | 'pattern' | 'custom'
  value: any
  message: string
}
