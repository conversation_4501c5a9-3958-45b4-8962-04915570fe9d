#!/usr/bin/env pwsh

# AI Construction Management - Git Commit and <PERSON><PERSON> Script
# This script commits all changes and pushes to the GitHub repository

Write-Host "=====================================" -ForegroundColor Cyan
Write-Host " Git Commit and Push" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in a git repository
if (-not (Test-Path ".git")) {
    Write-Host "[ERROR] Not in a git repository!" -ForegroundColor Red
    exit 1
}

# Step 1: Check git status
Write-Host "[1/5] Checking git status..." -ForegroundColor Yellow
git status --porcelain | Out-String | ForEach-Object {
    if ($_ -match "^\?\?") {
        Write-Host "   New: $_" -ForegroundColor Green
    } elseif ($_ -match "^ M") {
        Write-Host "   Modified: $_" -ForegroundColor Yellow
    } elseif ($_ -match "^D ") {
        Write-Host "   Deleted: $_" -ForegroundColor Red
    } else {
        Write-Host "   $_" -ForegroundColor Gray
    }
}

# Step 2: Stage all changes
Write-Host ""
Write-Host "[2/5] Staging all changes..." -ForegroundColor Yellow
git add -A
Write-Host "   ✓ All changes staged" -ForegroundColor Green

# Step 3: Show what will be committed
Write-Host ""
Write-Host "[3/5] Changes to be committed:" -ForegroundColor Yellow
git diff --cached --name-status | Out-String | ForEach-Object {
    Write-Host "   $_" -ForegroundColor Gray
}

# Step 4: Create commit
Write-Host ""
Write-Host "[4/5] Creating commit..." -ForegroundColor Yellow

# Create a detailed commit message
$commitMessage = @"
fix: resolve CompanyType export error and improve build stability

- Fixed CompanyType re-export error in CompanyContext.tsx
- Removed redundant export statement that was causing build failures
- Ensured all imports from company-types module are correct
- Verified TypeScript compilation passes without errors
- Updated ace.md with latest fix documentation

The application now builds successfully with the Company Type specialization
feature working as intended. AI responses are properly tailored to the
selected construction trade type.
"@

git commit -m $commitMessage
if ($LASTEXITCODE -ne 0) {
    Write-Host "[ERROR] Failed to create commit" -ForegroundColor Red
    exit 1
}
Write-Host "   ✓ Commit created successfully" -ForegroundColor Green

# Step 5: Push to remote
Write-Host ""
Write-Host "[5/5] Pushing to GitHub..." -ForegroundColor Yellow

# Check if the token push script exists
$tokenScript = ".\scripts\git-push-with-token.ps1"
if (Test-Path $tokenScript) {
    Write-Host "   Using token authentication script..." -ForegroundColor Gray
    & $tokenScript
} else {
    Write-Host "   Using standard git push..." -ForegroundColor Gray
    git push origin main
}

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "=====================================" -ForegroundColor Cyan
    Write-Host " ✅ Successfully pushed to GitHub!" -ForegroundColor Green
    Write-Host "=====================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Repository: https://github.com/mikeaper323/AI-Construction" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host ""
    Write-Host "[ERROR] Failed to push to GitHub" -ForegroundColor Red
    Write-Host "Please check your credentials or network connection" -ForegroundColor Yellow
    exit 1
}
