# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Next.js
.next
out
build
dist

# Testing
coverage
.nyc_output

# Environment files
.env
.env.*
!.env.example

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Git
.git
.gitignore
.github

# Documentation
README.md
CLAUDE.md
docs

# Development files
.husky
.eslintcache
.prettierignore
*.log

# Temporary files
tmp
temp
*.tmp
*.temp

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Scripts (except healthcheck)
scripts/*
!scripts/healthcheck.js

# Test files
__tests__
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx
jest.config.js
cypress
playwright

# Misc
.vercel
.turbo
CHANGELOG.md