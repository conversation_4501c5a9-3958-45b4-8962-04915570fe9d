/**
 * Material Filter Service
 * Ensures company-specific material filtering and validation
 */

import type { CompanyType } from '@/lib/company-types'
import type { TakeoffItem } from '@/types'

export interface MaterialCategory {
  name: string
  keywords: string[]
  subcategories?: string[]
}

// Define which material categories each contractor type should handle
export const TRADE_MATERIAL_CATEGORIES: Record<CompanyType, MaterialCategory[]> = {
  'General Contractor': [
    { name: 'Concrete', keywords: ['concrete', 'cement', 'rebar', 'form'], subcategories: ['Foundation', 'Slab', 'Structural'] },
    { name: 'Steel', keywords: ['steel', 'beam', 'column', 'joist', 'deck'], subcategories: ['Structural', 'Miscellaneous', 'Stairs'] },
    { name: 'Masonry', keywords: ['brick', 'block', 'cmu', 'mortar'], subcategories: ['Wall', 'Veneer'] },
    { name: 'Carpentry', keywords: ['wood', 'lumber', 'plywood', 'osb'], subcategories: ['Framing', 'Finish'] },
    { name: 'Insulation', keywords: ['insulation', 'vapor barrier'], subcategories: ['Thermal', 'Sound'] },
    { name: 'Doors', keywords: ['door', 'frame', 'hardware'], subcategories: ['Interior', 'Exterior', 'Overhead'] },
    { name: 'Windows', keywords: ['window', 'glazing', 'storefront'], subcategories: ['Fixed', 'Operable', 'Skylights'] },
    { name: 'Finishes', keywords: ['paint', 'wallcovering', 'ceiling'], subcategories: ['Interior', 'Exterior'] }
  ],
  
  'Electrical Contractor': [
    { name: 'Service & Distribution', keywords: ['panel', 'switchboard', 'transformer', 'generator', 'distribution', 'breaker'], subcategories: ['Main', 'Sub-panels'] },
    { name: 'Conduit & Raceways', keywords: ['conduit', 'emt', 'pvc', 'tray', 'wireway', 'raceway', 'flexible'], subcategories: ['Rigid', 'Flexible'] },
    { name: 'Wire & Cable', keywords: ['wire', 'cable', 'thhn', 'mc cable', 'thwn', 'conductor', 'awg'], subcategories: ['Power', 'Control', 'Data'] },
    { name: 'Lighting', keywords: ['fixture', 'lamp', 'led', 'ballast', 'luminaire', 'troffer', 'light', 'lighting'], subcategories: ['Interior', 'Exterior', 'Emergency'] },
    { name: 'Lighting Controls', keywords: ['occupancy sensor', 'dimmer', 'daylight sensor', 'control panel', 'time clock', 'relay module', 'photocell', 'switch'], subcategories: ['Sensors', 'Panels', 'Switches'] },
    { name: 'Devices', keywords: ['receptacle', 'switch', 'outlet', 'gfci', 'device', 'junction box'], subcategories: ['Standard', 'Special Purpose'] },
    { name: 'Fire Alarm', keywords: ['fire alarm', 'smoke detector', 'pull station', 'strobe', 'horn', 'notification'], subcategories: ['Initiating', 'Notification'] },
    { name: 'Low Voltage', keywords: ['data', 'telecom', 'security', 'av', 'control wire', 'low voltage'], subcategories: ['Structured Cabling', 'Access Control'] },
    { name: 'Emergency & Exit', keywords: ['emergency', 'exit sign', 'emergency light', 'battery backup'], subcategories: ['Exit', 'Emergency'] },
    { name: 'Grounding', keywords: ['ground', 'bond', 'rod', 'wire', 'grounding'], subcategories: ['System', 'Equipment'] }
  ],
  
  'Plumbing Contractor': [
    { name: 'Piping', keywords: ['pipe', 'copper', 'pvc', 'cpvc', 'pex'], subcategories: ['Water', 'Waste', 'Vent'] },
    { name: 'Fixtures', keywords: ['lavatory', 'water closet', 'urinal', 'sink', 'faucet'], subcategories: ['Commercial', 'Residential'] },
    { name: 'Valves', keywords: ['valve', 'ball', 'gate', 'check', 'backflow'], subcategories: ['Shutoff', 'Control'] },
    { name: 'Equipment', keywords: ['water heater', 'pump', 'tank', 'boiler'], subcategories: ['Heating', 'Storage'] },
    { name: 'Drainage', keywords: ['drain', 'cleanout', 'trap', 'floor drain'], subcategories: ['Interior', 'Exterior'] },
    { name: 'Insulation', keywords: ['pipe insulation', 'fiberglass', 'foam'], subcategories: ['Hot Water', 'Cold Water'] },
    { name: 'Supports', keywords: ['hanger', 'clamp', 'strap', 'bracket'], subcategories: ['Pipe', 'Equipment'] },
    { name: 'Gas', keywords: ['gas pipe', 'regulator', 'meter'], subcategories: ['Natural Gas', 'Propane'] }
  ],
  
  'HVAC Contractor': [
    { name: 'Equipment', keywords: ['rtu', 'ahu', 'vav', 'fan coil', 'chiller', 'boiler'], subcategories: ['Heating', 'Cooling', 'Ventilation'] },
    { name: 'Ductwork', keywords: ['duct', 'sheet metal', 'spiral', 'flexible'], subcategories: ['Supply', 'Return', 'Exhaust'] },
    { name: 'Diffusers & Grilles', keywords: ['diffuser', 'grille', 'register', 'louver'], subcategories: ['Supply', 'Return'] },
    { name: 'Piping', keywords: ['refrigerant', 'chilled water', 'hot water', 'condensate'], subcategories: ['Refrigerant', 'Hydronic'] },
    { name: 'Controls', keywords: ['thermostat', 'sensor', 'actuator', 'vfd', 'bms'], subcategories: ['Zone', 'System'] },
    { name: 'Insulation', keywords: ['duct insulation', 'pipe insulation'], subcategories: ['Duct', 'Pipe'] },
    { name: 'Accessories', keywords: ['damper', 'coil', 'filter', 'humidifier'], subcategories: ['Air', 'Water'] },
    { name: 'Exhaust', keywords: ['exhaust fan', 'hood', 'vent'], subcategories: ['General', 'Kitchen', 'Lab'] }
  ],
  
  'Roofing Contractor': [
    { name: 'Membrane', keywords: ['tpo', 'epdm', 'pvc', 'modified bitumen'], subcategories: ['Single-ply', 'Built-up'] },
    { name: 'Shingles', keywords: ['shingle', 'asphalt', 'architectural', 'slate'], subcategories: ['Asphalt', 'Wood', 'Slate'] },
    { name: 'Metal Roofing', keywords: ['metal roof', 'standing seam', 'corrugated'], subcategories: ['Standing Seam', 'Panel'] },
    { name: 'Insulation', keywords: ['roof insulation', 'polyiso', 'eps'], subcategories: ['Board', 'Spray'] },
    { name: 'Accessories', keywords: ['flashing', 'coping', 'fascia', 'gutter'], subcategories: ['Edge Metal', 'Penetrations'] },
    { name: 'Underlayment', keywords: ['underlayment', 'felt', 'synthetic'], subcategories: ['Felt', 'Synthetic'] },
    { name: 'Drainage', keywords: ['drain', 'scupper', 'downspout'], subcategories: ['Internal', 'External'] },
    { name: 'Walkways', keywords: ['walkpad', 'pavers'], subcategories: ['Maintenance', 'Access'] }
  ],
  
  'Concrete Contractor': [
    { name: 'Concrete Mix', keywords: ['concrete', 'ready mix', 'psi', 'mix design'], subcategories: ['Structural', 'Architectural'] },
    { name: 'Reinforcement', keywords: ['rebar', 'mesh', 'fiber', 'post tension'], subcategories: ['Steel', 'Synthetic'] },
    { name: 'Formwork', keywords: ['form', 'shoring', 'plywood', 'steel form'], subcategories: ['Wall', 'Slab', 'Column'] },
    { name: 'Admixtures', keywords: ['admixture', 'accelerator', 'retarder', 'plasticizer'], subcategories: ['Chemical', 'Mineral'] },
    { name: 'Finishing', keywords: ['finish', 'sealer', 'cure', 'hardener'], subcategories: ['Surface', 'Joint'] },
    { name: 'Accessories', keywords: ['anchor', 'embed', 'waterstop', 'joint'], subcategories: ['Embedded', 'Surface'] },
    { name: 'Repair', keywords: ['patch', 'grout', 'epoxy'], subcategories: ['Structural', 'Cosmetic'] },
    { name: 'Equipment', keywords: ['pump', 'vibrator', 'screed'], subcategories: ['Placement', 'Finishing'] }
  ],
  
  'Steel/Metal Contractor': [
    { name: 'Structural Steel', keywords: ['beam', 'column', 'girder', 'angle'], subcategories: ['Primary', 'Secondary'] },
    { name: 'Decking', keywords: ['deck', 'composite', 'form deck'], subcategories: ['Roof', 'Floor'] },
    { name: 'Miscellaneous', keywords: ['angle', 'channel', 'tube', 'plate'], subcategories: ['Supports', 'Frames'] },
    { name: 'Stairs', keywords: ['stair', 'tread', 'riser', 'stringer'], subcategories: ['Interior', 'Exterior'] },
    { name: 'Railings', keywords: ['railing', 'handrail', 'guardrail'], subcategories: ['Interior', 'Exterior'] },
    { name: 'Fasteners', keywords: ['bolt', 'weld', 'anchor', 'screw'], subcategories: ['Structural', 'Sheet Metal'] },
    { name: 'Ornamental', keywords: ['ornamental', 'decorative', 'screen'], subcategories: ['Interior', 'Exterior'] },
    { name: 'Studs', keywords: ['stud', 'track', 'framing'], subcategories: ['Load-bearing', 'Non-bearing'] }
  ],
  
  'Masonry Contractor': [
    { name: 'Block', keywords: ['cmu', 'block', 'concrete masonry'], subcategories: ['Standard', 'Architectural'] },
    { name: 'Brick', keywords: ['brick', 'face brick', 'veneer'], subcategories: ['Modular', 'Oversized'] },
    { name: 'Stone', keywords: ['stone', 'granite', 'limestone', 'marble'], subcategories: ['Natural', 'Manufactured'] },
    { name: 'Mortar', keywords: ['mortar', 'grout', 'type s', 'type n'], subcategories: ['Setting', 'Pointing'] },
    { name: 'Reinforcement', keywords: ['wire', 'ladder', 'rebar', 'tie'], subcategories: ['Horizontal', 'Vertical'] },
    { name: 'Accessories', keywords: ['anchor', 'flashing', 'weep', 'vent'], subcategories: ['Structural', 'Moisture'] },
    { name: 'Sealants', keywords: ['sealant', 'caulk', 'backer rod'], subcategories: ['Joint', 'Surface'] },
    { name: 'Cleaning', keywords: ['cleaner', 'sealer', 'water repellent'], subcategories: ['New', 'Restoration'] }
  ],
  
  'Painting Contractor': [
    { name: 'Paint', keywords: ['paint', 'primer', 'coating'], subcategories: ['Interior', 'Exterior', 'Special'] },
    { name: 'Stains', keywords: ['stain', 'wood stain', 'concrete stain'], subcategories: ['Transparent', 'Solid'] },
    { name: 'Primers', keywords: ['primer', 'sealer', 'block filler'], subcategories: ['Metal', 'Wood', 'Masonry'] },
    { name: 'Special Coatings', keywords: ['epoxy', 'urethane', 'intumescent'], subcategories: ['Floor', 'Fire-resistive'] },
    { name: 'Preparation', keywords: ['sandpaper', 'putty', 'compound'], subcategories: ['Surface', 'Patching'] },
    { name: 'Application', keywords: ['brush', 'roller', 'spray'], subcategories: ['Manual', 'Spray'] },
    { name: 'Protection', keywords: ['tape', 'plastic', 'drop cloth'], subcategories: ['Masking', 'Floor'] },
    { name: 'Wallcovering', keywords: ['wallpaper', 'vinyl', 'fabric'], subcategories: ['Commercial', 'Residential'] }
  ],
  
  'Flooring Contractor': [
    { name: 'Carpet', keywords: ['carpet', 'tile', 'broadloom', 'pad'], subcategories: ['Commercial', 'Residential'] },
    { name: 'Hardwood', keywords: ['hardwood', 'oak', 'maple', 'engineered'], subcategories: ['Solid', 'Engineered'] },
    { name: 'Tile', keywords: ['ceramic', 'porcelain', 'marble', 'granite'], subcategories: ['Floor', 'Wall'] },
    { name: 'Resilient', keywords: ['vinyl', 'lvt', 'vct', 'rubber'], subcategories: ['Sheet', 'Tile', 'Plank'] },
    { name: 'Concrete', keywords: ['epoxy', 'polish', 'stain', 'seal'], subcategories: ['Decorative', 'Industrial'] },
    { name: 'Underlayment', keywords: ['underlayment', 'moisture barrier', 'cushion'], subcategories: ['Moisture', 'Sound'] },
    { name: 'Base', keywords: ['base', 'cove', 'wood base', 'rubber base'], subcategories: ['Wood', 'Rubber', 'Vinyl'] },
    { name: 'Transitions', keywords: ['transition', 'threshold', 'reducer'], subcategories: ['Metal', 'Wood'] }
  ],
  
  'Landscaping Contractor': [
    { name: 'Plants', keywords: ['tree', 'shrub', 'perennial', 'grass'], subcategories: ['Trees', 'Shrubs', 'Groundcover'] },
    { name: 'Hardscape', keywords: ['paver', 'retaining wall', 'concrete', 'stone'], subcategories: ['Paving', 'Walls'] },
    { name: 'Irrigation', keywords: ['sprinkler', 'drip', 'controller', 'valve'], subcategories: ['Spray', 'Drip'] },
    { name: 'Soil', keywords: ['topsoil', 'mulch', 'compost', 'amendment'], subcategories: ['Planting', 'Mulch'] },
    { name: 'Drainage', keywords: ['drain', 'french drain', 'catch basin'], subcategories: ['Surface', 'Subsurface'] },
    { name: 'Lighting', keywords: ['landscape light', 'path light', 'transformer'], subcategories: ['Path', 'Accent'] },
    { name: 'Edging', keywords: ['edging', 'border', 'steel', 'plastic'], subcategories: ['Metal', 'Plastic'] },
    { name: 'Accessories', keywords: ['fabric', 'stake', 'fertilizer'], subcategories: ['Installation', 'Maintenance'] }
  ],
  
  'Demolition Contractor': [
    { name: 'Equipment', keywords: ['excavator', 'loader', 'hammer', 'saw'], subcategories: ['Heavy', 'Hand Tools'] },
    { name: 'Safety', keywords: ['barrier', 'fence', 'protection', 'scaffold'], subcategories: ['Barricades', 'Protection'] },
    { name: 'Disposal', keywords: ['dumpster', 'container', 'recycling'], subcategories: ['Waste', 'Recycling'] },
    { name: 'Abatement', keywords: ['asbestos', 'lead', 'mold'], subcategories: ['Hazardous', 'Environmental'] },
    { name: 'Salvage', keywords: ['salvage', 'reclaim', 'reuse'], subcategories: ['Materials', 'Fixtures'] },
    { name: 'Protection', keywords: ['plastic', 'plywood', 'temporary'], subcategories: ['Dust', 'Adjacent'] },
    { name: 'Utilities', keywords: ['cap', 'disconnect', 'temporary'], subcategories: ['Electrical', 'Plumbing'] },
    { name: 'Permits', keywords: ['permit', 'inspection', 'notification'], subcategories: ['Building', 'Environmental'] }
  ],
  
  'Excavation/Earthwork Contractor': [
    { name: 'Excavation', keywords: ['excavate', 'dig', 'trench', 'grade'], subcategories: ['Mass', 'Trench'] },
    { name: 'Fill', keywords: ['fill', 'backfill', 'structural fill', 'select'], subcategories: ['Structural', 'General'] },
    { name: 'Compaction', keywords: ['compact', 'density', 'proctor'], subcategories: ['Soil', 'Aggregate'] },
    { name: 'Shoring', keywords: ['shoring', 'sheet pile', 'bracing'], subcategories: ['Temporary', 'Permanent'] },
    { name: 'Dewatering', keywords: ['dewater', 'pump', 'well point'], subcategories: ['Surface', 'Deep'] },
    { name: 'Erosion Control', keywords: ['silt fence', 'erosion', 'sediment'], subcategories: ['Temporary', 'Permanent'] },
    { name: 'Rock', keywords: ['rock', 'blast', 'rip', 'boulder'], subcategories: ['Excavation', 'Placement'] },
    { name: 'Utilities', keywords: ['locate', 'protect', 'relocate'], subcategories: ['Existing', 'New'] }
  ],
  
  'Glass & Glazing Contractor': [
    { name: 'Glass', keywords: ['glass', 'tempered', 'laminated', 'insulated'], subcategories: ['Clear', 'Tinted', 'Low-E'] },
    { name: 'Frames', keywords: ['aluminum', 'storefront', 'curtainwall'], subcategories: ['Storefront', 'Curtainwall'] },
    { name: 'Doors', keywords: ['door', 'entrance', 'revolving', 'sliding'], subcategories: ['Swing', 'Sliding'] },
    { name: 'Hardware', keywords: ['handle', 'closer', 'panic', 'lock'], subcategories: ['Operating', 'Locking'] },
    { name: 'Sealants', keywords: ['silicone', 'sealant', 'gasket'], subcategories: ['Wet', 'Dry'] },
    { name: 'Accessories', keywords: ['sill', 'mullion', 'transom'], subcategories: ['Structural', 'Decorative'] },
    { name: 'Specialty', keywords: ['mirror', 'bullet resistant', 'fire rated'], subcategories: ['Safety', 'Decorative'] },
    { name: 'Film', keywords: ['film', 'tint', 'security', 'solar'], subcategories: ['Solar', 'Security'] }
  ],
  
  'Insulation Contractor': [
    { name: 'Batt', keywords: ['batt', 'fiberglass', 'mineral wool'], subcategories: ['Wall', 'Ceiling'] },
    { name: 'Blown', keywords: ['blown', 'cellulose', 'fiberglass'], subcategories: ['Attic', 'Wall'] },
    { name: 'Rigid', keywords: ['rigid', 'foam', 'polyiso', 'xps'], subcategories: ['Wall', 'Roof'] },
    { name: 'Spray Foam', keywords: ['spray foam', 'closed cell', 'open cell'], subcategories: ['Closed Cell', 'Open Cell'] },
    { name: 'Mechanical', keywords: ['pipe insulation', 'duct wrap'], subcategories: ['Pipe', 'Duct'] },
    { name: 'Fire Stopping', keywords: ['firestop', 'smoke seal', 'intumescent'], subcategories: ['Penetrations', 'Joints'] },
    { name: 'Vapor Barriers', keywords: ['vapor barrier', 'retarder'], subcategories: ['Wall', 'Crawlspace'] },
    { name: 'Accessories', keywords: ['tape', 'fastener', 'adhesive'], subcategories: ['Attachment', 'Sealing'] }
  ],
  
  'Drywall Contractor': [
    { name: 'Gypsum Board', keywords: ['drywall', 'gypsum', 'sheetrock'], subcategories: ['Regular', 'Moisture Resistant', 'Fire Rated'] },
    { name: 'Framing', keywords: ['stud', 'track', 'framing'], subcategories: ['Metal', 'Wood'] },
    { name: 'Finishing', keywords: ['mud', 'compound', 'tape', 'texture'], subcategories: ['Taping', 'Texturing'] },
    { name: 'Trim', keywords: ['corner bead', 'j-bead', 'reveal'], subcategories: ['Corner', 'Edge'] },
    { name: 'Fasteners', keywords: ['screw', 'nail', 'adhesive'], subcategories: ['Mechanical', 'Adhesive'] },
    { name: 'Acoustical', keywords: ['sound board', 'resilient channel'], subcategories: ['Board', 'Isolation'] },
    { name: 'Accessories', keywords: ['access door', 'control joint'], subcategories: ['Access', 'Movement'] },
    { name: 'Tools', keywords: ['taping knife', 'sander', 'lift'], subcategories: ['Hand', 'Power'] }
  ],
  
  'Fire Protection Contractor': [
    { name: 'Sprinkler Heads', keywords: ['sprinkler', 'head', 'pendant', 'upright'], subcategories: ['Standard', 'Special'] },
    { name: 'Piping', keywords: ['pipe', 'fitting', 'groove', 'thread'], subcategories: ['Steel', 'CPVC'] },
    { name: 'Valves', keywords: ['valve', 'control', 'check', 'alarm'], subcategories: ['Control', 'Check'] },
    { name: 'Fire Pump', keywords: ['pump', 'controller', 'jockey'], subcategories: ['Main', 'Jockey'] },
    { name: 'Alarm', keywords: ['alarm', 'bell', 'horn', 'strobe'], subcategories: ['Audible', 'Visual'] },
    { name: 'Standpipe', keywords: ['standpipe', 'hose', 'cabinet'], subcategories: ['Wet', 'Dry'] },
    { name: 'Extinguishers', keywords: ['extinguisher', 'cabinet', 'bracket'], subcategories: ['Portable', 'Wheeled'] },
    { name: 'Suppression', keywords: ['suppression', 'clean agent', 'foam'], subcategories: ['Gas', 'Foam'] }
  ],
  
  'Elevator Contractor': [
    { name: 'Equipment', keywords: ['cab', 'motor', 'controller', 'rail'], subcategories: ['Traction', 'Hydraulic'] },
    { name: 'Doors', keywords: ['door', 'operator', 'track', 'sill'], subcategories: ['Car', 'Hoistway'] },
    { name: 'Controls', keywords: ['button', 'indicator', 'controller'], subcategories: ['Car', 'Hall'] },
    { name: 'Safety', keywords: ['governor', 'buffer', 'safety'], subcategories: ['Mechanical', 'Electrical'] },
    { name: 'Cables', keywords: ['rope', 'cable', 'traveling'], subcategories: ['Hoist', 'Compensation'] },
    { name: 'Hydraulic', keywords: ['cylinder', 'pump', 'valve'], subcategories: ['Power Unit', 'Cylinder'] },
    { name: 'Finishes', keywords: ['panel', 'handrail', 'floor'], subcategories: ['Interior', 'Entrance'] },
    { name: 'Modernization', keywords: ['upgrade', 'modernization', 'retrofit'], subcategories: ['Control', 'Equipment'] }
  ],
  
  'Solar/Renewable Energy Contractor': [
    { name: 'Solar Panels', keywords: ['panel', 'module', 'photovoltaic', 'solar'], subcategories: ['Monocrystalline', 'Polycrystalline'] },
    { name: 'Inverters', keywords: ['inverter', 'micro', 'string', 'power'], subcategories: ['String', 'Micro'] },
    { name: 'Racking', keywords: ['rack', 'rail', 'mount', 'ballast'], subcategories: ['Roof', 'Ground'] },
    { name: 'Electrical', keywords: ['combiner', 'disconnect', 'meter'], subcategories: ['DC', 'AC'] },
    { name: 'Battery', keywords: ['battery', 'storage', 'backup'], subcategories: ['Lithium', 'Lead Acid'] },
    { name: 'Monitoring', keywords: ['monitor', 'meter', 'data'], subcategories: ['Production', 'Consumption'] },
    { name: 'Wire', keywords: ['pv wire', 'mc4', 'connector'], subcategories: ['DC', 'AC'] },
    { name: 'Grounding', keywords: ['ground', 'lug', 'weeb'], subcategories: ['Equipment', 'System'] }
  ],
  
  'Marine/Underwater Contractor': [
    { name: 'Piling', keywords: ['pile', 'sheet', 'h-pile', 'timber'], subcategories: ['Steel', 'Concrete', 'Timber'] },
    { name: 'Dredging', keywords: ['dredge', 'excavate', 'sediment'], subcategories: ['Mechanical', 'Hydraulic'] },
    { name: 'Concrete', keywords: ['underwater concrete', 'tremie', 'grout'], subcategories: ['Cast-in-place', 'Precast'] },
    { name: 'Diving', keywords: ['dive', 'equipment', 'umbilical'], subcategories: ['Equipment', 'Support'] },
    { name: 'Marine Hardware', keywords: ['cleat', 'bollard', 'fender'], subcategories: ['Mooring', 'Protection'] },
    { name: 'Erosion', keywords: ['riprap', 'geotextile', 'mattress'], subcategories: ['Protection', 'Filter'] },
    { name: 'Dock', keywords: ['dock', 'float', 'gangway'], subcategories: ['Fixed', 'Floating'] },
    { name: 'Equipment', keywords: ['barge', 'crane', 'vessel'], subcategories: ['Marine', 'Support'] }
  ]
}

import { intelligentClassifier, type MaterialContext } from './intelligent-material-classifier'

// Re-export MaterialContext for other services
export type { MaterialContext }

export class MaterialFilterService {
  /**
   * Filter materials based on company type using intelligent classification
   */
  async filterMaterialsByTrade(
    materials: TakeoffItem[],
    companyType: CompanyType,
    context?: MaterialContext
  ): Promise<TakeoffItem[]> {
    // Use intelligent classifier for validation
    const validation = await intelligentClassifier.validateTradeScope(
      materials,
      companyType,
      context || {}
    )
    
    // Log recommendations for transparency
    if (validation.recommendations.length > 0) {
      console.log(`Material classification recommendations for ${companyType}:`)
      validation.recommendations.forEach(rec => console.log(`- ${rec}`))
    }
    
    // Log missing materials if any
    if (validation.missingMaterials.length > 0) {
      console.log(`Potentially missing materials for ${companyType}:`)
      validation.missingMaterials.forEach(mat => console.log(`- ${mat}`))
    }
    
    return validation.validMaterials
  }
  
  /**
   * Legacy synchronous filter method - kept for backward compatibility
   * Prefer using filterMaterialsByTrade for better accuracy
   */
  filterMaterialsByTradeSync(
    materials: TakeoffItem[],
    companyType: CompanyType
  ): TakeoffItem[] {
    const allowedCategories = TRADE_MATERIAL_CATEGORIES[companyType] || []
    
    return materials.filter(material => {
      const materialCategoryLower = material.category.toLowerCase()
      const materialDescLower = material.description.toLowerCase()
      
      return allowedCategories.some(category => {
        // Check category name
        if (materialCategoryLower.includes(category.name.toLowerCase())) {
          return true
        }
        
        // Check keywords
        return category.keywords.some(keyword => 
          materialDescLower.includes(keyword.toLowerCase()) ||
          materialCategoryLower.includes(keyword.toLowerCase())
        )
      })
    })
  }
  
  /**
   * Get relevant material categories for a company type
   */
  getTradeCategories(companyType: CompanyType): string[] {
    const categories = TRADE_MATERIAL_CATEGORIES[companyType] || []
    return categories.map(cat => cat.name)
  }
  
  /**
   * Validate if a material is appropriate for a trade using intelligent classification
   */
  async isValidForTrade(
    materialCategory: string,
    materialDescription: string,
    companyType: CompanyType,
    context?: MaterialContext
  ): Promise<boolean> {
    const classification = await intelligentClassifier.classifyMaterial(
      materialDescription,
      companyType,
      context || {}
    )
    
    return classification.isValidForTrade && classification.confidence > 0.6
  }
  
  /**
   * Legacy synchronous validation - kept for backward compatibility
   */
  isValidForTradeSync(
    materialCategory: string,
    materialDescription: string,
    companyType: CompanyType
  ): boolean {
    const allowedCategories = TRADE_MATERIAL_CATEGORIES[companyType] || []
    const categoryLower = materialCategory.toLowerCase()
    const descLower = materialDescription.toLowerCase()
    
    return allowedCategories.some(category => {
      if (categoryLower.includes(category.name.toLowerCase())) {
        return true
      }
      
      return category.keywords.some(keyword => 
        descLower.includes(keyword.toLowerCase()) ||
        categoryLower.includes(keyword.toLowerCase())
      )
    })
  }
  
  /**
   * Perform contextual validation using intelligent classification
   */
  async validateMaterialContext(
    material: TakeoffItem,
    companyType: CompanyType,
    context?: MaterialContext
  ): Promise<{ isValid: boolean; suggestedCategory?: string; reason?: string }> {
    const classification = await intelligentClassifier.classifyMaterial(
      material,
      companyType,
      context || {}
    )
    
    if (!classification.isValidForTrade) {
      return {
        isValid: false,
        suggestedCategory: classification.csiDivisionName,
        reason: classification.reasoning
      }
    }
    
    return { isValid: true }
  }
  
  /**
   * Get typical material list for a trade and project type
   */
  getTypicalMaterials(
    companyType: CompanyType,
    projectType: 'commercial' | 'residential' | 'industrial' | 'institutional',
    projectSize: number // in square feet
  ): Array<{
    category: string
    description: string
    typicalQuantity: number
    unit: string
  }> {
    const materials: Array<any> = []
    const categories = TRADE_MATERIAL_CATEGORIES[companyType] || []
    
    // Calculate scale factor based on project size
    const scaleFactor = projectSize / 10000 // Base on 10,000 SF
    
    categories.forEach(category => {
      // Add typical materials based on category and project type
      switch (companyType) {
        case 'Electrical Contractor':
          if (category.name === 'Service & Distribution') {
            materials.push({
              category: category.name,
              description: 'Main Distribution Panel - 600A',
              typicalQuantity: projectType === 'commercial' ? 1 : 1,
              unit: 'EA'
            })
          }
          if (category.name === 'Wire & Cable') {
            materials.push({
              category: category.name,
              description: '#12 THHN Wire',
              typicalQuantity: Math.round(scaleFactor * 5000),
              unit: 'LF'
            })
          }
          if (category.name === 'Lighting') {
            materials.push({
              category: category.name,
              description: '2x4 LED Troffer',
              typicalQuantity: Math.round(scaleFactor * 20),
              unit: 'EA'
            })
          }
          break
          
        case 'Plumbing Contractor':
          if (category.name === 'Piping') {
            materials.push({
              category: category.name,
              description: '1" Copper Pipe Type L',
              typicalQuantity: Math.round(scaleFactor * 200),
              unit: 'LF'
            })
          }
          if (category.name === 'Fixtures') {
            materials.push({
              category: category.name,
              description: 'Water Closet - Commercial',
              typicalQuantity: Math.round(scaleFactor * 3),
              unit: 'EA'
            })
          }
          break
          
        case 'HVAC Contractor':
          if (category.name === 'Equipment') {
            materials.push({
              category: category.name,
              description: '10 Ton Rooftop Unit',
              typicalQuantity: Math.round(scaleFactor * 0.4),
              unit: 'EA'
            })
          }
          if (category.name === 'Ductwork') {
            materials.push({
              category: category.name,
              description: 'Sheet Metal Ductwork',
              typicalQuantity: Math.round(scaleFactor * 500),
              unit: 'LB'
            })
          }
          break
          
        case 'General Contractor':
          if (category.name === 'Concrete') {
            materials.push({
              category: category.name,
              description: 'Concrete 4000 PSI',
              typicalQuantity: Math.round(scaleFactor * 5), // Reduced: ~5 CY per 1000 SF
              unit: 'CY'
            })
          }
          if (category.name === 'Steel') {
            materials.push({
              category: category.name,
              description: 'Structural Steel',
              typicalQuantity: Math.round(scaleFactor * 1.5), // Reduced: ~1.5 TON per 1000 SF
              unit: 'TON'
            })
          }
          break
      }
    })
    
    return materials
  }
  
  /**
   * Suggest missing materials for a trade
   */
  suggestMissingMaterials(
    existingMaterials: TakeoffItem[],
    companyType: CompanyType,
    projectType: 'commercial' | 'residential' | 'industrial' | 'institutional'
  ): string[] {
    const suggestions: string[] = []
    const categories = TRADE_MATERIAL_CATEGORIES[companyType] || []
    
    // Check which categories are missing
    categories.forEach(category => {
      const hasCategory = existingMaterials.some(material => 
        material.category.toLowerCase() === category.name.toLowerCase() ||
        category.keywords.some(keyword => 
          material.description.toLowerCase().includes(keyword.toLowerCase())
        )
      )
      
      if (!hasCategory) {
        // Suggest based on company type and category
        switch (companyType) {
          case 'Electrical Contractor':
            if (category.name === 'Grounding') {
              suggestions.push('Ground rods and grounding wire (NEC required)')
            }
            if (category.name === 'Fire Alarm' && projectType === 'commercial') {
              suggestions.push('Fire alarm system components may be required')
            }
            break
            
          case 'Plumbing Contractor':
            if (category.name === 'Insulation') {
              suggestions.push('Pipe insulation for hot water and HVAC piping (energy code requirement)')
            }
            if (category.name === 'Valves') {
              suggestions.push('Shutoff valves and backflow preventers')
            }
            break
            
          case 'HVAC Contractor':
            if (category.name === 'Controls') {
              suggestions.push('Temperature controls and BMS components')
            }
            if (category.name === 'Insulation') {
              suggestions.push('Duct and pipe insulation')
            }
            break
        }
      }
    })
    
    return suggestions
  }
}

// Export singleton instance
export const materialFilter = new MaterialFilterService()