/**
 * Example usage of company-type-map utilities
 * This file demonstrates how to use the mapping functions in various scenarios
 */

import { abbreviatedToFull, fullToAbbreviated } from './company-type-map'
import { getCompanyAIContext } from './company-types'

// Example 1: Converting form values to system types
// When a user submits the signup form with companyType: 'ELECTRICAL'
function handleSignupFormSubmit(formData: { companyType: string }) {
  const fullCompanyType = abbreviatedToFull(formData.companyType)
  
  if (!fullCompanyType) {
    throw new Error('Invalid company type selected')
  }
  
  // Now you can use the full company type for:
  // 1. Storing in database
  // 2. Getting AI context
  const aiContext = getCompanyAIContext(fullCompanyType)
  
  return {
    companyType: fullCompanyType, // 'Electrical Contractor'
    aiContext,
  }
}

// Example 2: Converting database values back to form values
// When loading user data to populate a settings form
function prepareSettingsFormData(userData: { companyType: string }) {
  const abbreviatedType = fullToAbbreviated(userData.companyType)
  
  if (!abbreviatedType) {
    console.warn('Unknown company type in database:', userData.companyType)
    return { companyType: 'GENERAL' } // Default fallback
  }
  
  return {
    companyType: abbreviatedType, // 'ELECTRICAL' for form select value
  }
}

// Example 3: API endpoint usage
// In your API route handler
export async function POST(request: Request) {
  const { companyType, ...otherData } = await request.json()
  
  // Convert abbreviated type from form to full type for database
  const fullType = abbreviatedToFull(companyType)
  
  if (!fullType) {
    return new Response(
      JSON.stringify({ error: 'Invalid company type' }),
      { status: 400 }
    )
  }
  
  // Save to database with full company type
  // await saveUser({ ...otherData, companyType: fullType })
  
  return new Response(
    JSON.stringify({ success: true, companyType: fullType }),
    { status: 200 }
  )
}

// Example 4: Type-safe usage with TypeScript
import type { AbbreviatedCompanyType } from './company-type-map'
import type { CompanyType } from './company-types'

interface SignupFormData {
  email: string
  companyType: AbbreviatedCompanyType
}

interface UserProfile {
  email: string
  companyType: CompanyType
}

function transformSignupToProfile(formData: SignupFormData): UserProfile | null {
  const fullType = abbreviatedToFull(formData.companyType)
  
  if (!fullType) return null
  
  return {
    email: formData.email,
    companyType: fullType,
  }
}