'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Camera,
  Upload,
  Play,
  Pause,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Grid,
  Calendar,
  Building2,
  Eye,
  Brain,
  CheckCircle2,
  AlertTriangle,
  XCircle,
  Clock,
  TrendingUp,
  Download,
  Share2,
  Filter,
  MapPin,
  Layers,
  Activity,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { ProgressCapture, DetectedElement } from '@/types'

// Mock data for demonstration
const mockCaptures: ProgressCapture[] = [
  {
    id: '1',
    projectId: 'project-1',
    captureDate: new Date('2024-06-20T10:00:00'),
    type: 'photo_360',
    location: {
      zone: 'Zone A',
      floor: 3,
      coordinates: { x: 100, y: 200, z: 30 }
    },
    data: {
      url: '/api/captures/360-image-1',
      format: '360',
      size: 15728640,
      metadata: {
        camera: 'Insta360 Pro 2',
        resolution: '8K',
        capturedBy: '<PERSON>'
      }
    },
    aiAnalysis: {
      elementsDetected: [
        {
          type: 'concrete_slab',
          confidence: 0.98,
          location: { x: 50, y: 100, width: 200, height: 150 },
          status: 'completed',
          matchesBIM: true
        },
        {
          type: 'steel_beam',
          confidence: 0.95,
          location: { x: 120, y: 80, width: 100, height: 20 },
          status: 'in_progress',
          matchesBIM: true
        },
        {
          type: 'hvac_duct',
          confidence: 0.87,
          location: { x: 200, y: 50, width: 80, height: 60 },
          status: 'planned',
          matchesBIM: false
        }
      ],
      progressPercentage: 67,
      qualityScore: 92,
      deviations: [
        {
          type: 'missing',
          severity: 'medium',
          description: 'HVAC duct not installed as per schedule',
          element: {
            type: 'hvac_duct',
            confidence: 0.87,
            location: { x: 200, y: 50, width: 80, height: 60 },
            status: 'planned',
            matchesBIM: false
          }
        }
      ],
      recommendations: [
        'Schedule HVAC installation for Zone A Floor 3',
        'Verify beam alignment with structural plans',
        'Quality check required for concrete surface finish'
      ]
    },
    createdBy: 'user-1',
    createdAt: new Date('2024-06-20T10:05:00')
  }
]

const zones = ['All Zones', 'Zone A', 'Zone B', 'Zone C', 'Zone D']
const floors = ['All Floors', 'Ground', 'Floor 1', 'Floor 2', 'Floor 3', 'Floor 4']

export default function ProgressTrackingPage() {
  const [selectedProject] = useState('Downtown Tower')
  const [selectedZone, setSelectedZone] = useState('All Zones')
  const [selectedFloor, setSelectedFloor] = useState('All Floors')
  const [viewMode, setViewMode] = useState<'split' | 'full'>('split')
  const [compareMode, setCompareMode] = useState(false)
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [compareDate, setCompareDate] = useState(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
  const [is360View, setIs360View] = useState(true)
  const [showAIOverlay, setShowAIOverlay] = useState(true)
  const [selectedCapture, setSelectedCapture] = useState<ProgressCapture | null>(mockCaptures[0])

  // Simulated progress data
  const overallProgress = 67
  const weeklyProgress = 5.2
  const deviationCount = 3
  const qualityScore = 94

  const progressByZone = [
    { zone: 'Zone A', progress: 75, status: 'on-track' },
    { zone: 'Zone B', progress: 62, status: 'on-track' },
    { zone: 'Zone C', progress: 58, status: 'at-risk' },
    { zone: 'Zone D', progress: 45, status: 'delayed' }
  ]

  const recentCaptures = [
    { id: '1', location: 'Zone A - Floor 3', time: '2 hours ago', status: 'processed' },
    { id: '2', location: 'Zone B - Floor 2', time: '4 hours ago', status: 'processed' },
    { id: '3', location: 'Zone C - Floor 1', time: '6 hours ago', status: 'processing' },
    { id: '4', location: 'Zone D - Ground', time: '1 day ago', status: 'processed' }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-500'
      case 'in_progress': return 'text-blue-500'
      case 'planned': return 'text-gray-400'
      default: return 'text-gray-500'
    }
  }

  const getProgressColor = (status: string) => {
    switch (status) {
      case 'on-track': return 'bg-green-500'
      case 'at-risk': return 'bg-yellow-500'
      case 'delayed': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Progress Tracking
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            AI-powered 360° reality capture and progress monitoring
          </p>
        </div>
        <div className="flex space-x-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-4 py-2 bg-construction-blue text-white rounded-lg shadow-sm hover:bg-construction-blue/90 transition-colors"
          >
            <Upload className="w-5 h-5 mr-2" />
            Upload Capture
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Camera className="w-5 h-5 mr-2" />
            Live Capture
          </motion.button>
        </div>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <Activity className="w-8 h-8 text-construction-blue" />
            <span className="text-sm text-green-500 font-medium">+{weeklyProgress}%</span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {overallProgress}%
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Overall Progress</p>
          <div className="mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div className="bg-construction-blue h-2 rounded-full" style={{ width: `${overallProgress}%` }} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <Brain className="w-8 h-8 text-purple-500" />
            <span className="text-sm text-yellow-500 font-medium">{deviationCount} found</span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            AI Analysis
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Deviations Detected</p>
          <button className="mt-4 text-sm text-purple-500 hover:text-purple-600 font-medium">
            View Details →
          </button>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <CheckCircle2 className="w-8 h-8 text-green-500" />
            <span className="text-sm text-green-500 font-medium">Excellent</span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {qualityScore}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Quality Score</p>
          <div className="mt-4 flex space-x-1">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className={cn(
                  "h-6 w-1 rounded-full",
                  i < Math.floor(qualityScore / 20) ? "bg-green-500" : "bg-gray-300 dark:bg-gray-700"
                )}
              />
            ))}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <Clock className="w-8 h-8 text-blue-500" />
            <span className="text-sm text-blue-500 font-medium">Today</span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            24
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Captures Today</p>
          <button className="mt-4 text-sm text-blue-500 hover:text-blue-600 font-medium">
            View All →
          </button>
        </motion.div>
      </div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 360° Viewer */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
          >
            {/* Viewer Controls */}
            <div className="border-b border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <select
                    value={selectedZone}
                    onChange={(e) => setSelectedZone(e.target.value)}
                    className="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    {zones.map(zone => (
                      <option key={zone} value={zone}>{zone}</option>
                    ))}
                  </select>
                  <select
                    value={selectedFloor}
                    onChange={(e) => setSelectedFloor(e.target.value)}
                    className="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    {floors.map(floor => (
                      <option key={floor} value={floor}>{floor}</option>
                    ))}
                  </select>
                  <button
                    onClick={() => setCompareMode(!compareMode)}
                    className={cn(
                      "px-3 py-1.5 text-sm rounded-lg transition-colors",
                      compareMode 
                        ? "bg-construction-blue text-white" 
                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                    )}
                  >
                    Compare
                  </button>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowAIOverlay(!showAIOverlay)}
                    className={cn(
                      "p-2 rounded-lg transition-colors",
                      showAIOverlay 
                        ? "bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300" 
                        : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                    )}
                  >
                    <Brain className="w-5 h-5" />
                  </button>
                  <button className="p-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600">
                    <ZoomIn className="w-5 h-5" />
                  </button>
                  <button className="p-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600">
                    <ZoomOut className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => setViewMode(viewMode === 'split' ? 'full' : 'split')}
                    className="p-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    <Maximize2 className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* 360° Image Viewer (Simulated) */}
            <div className="relative h-[600px] bg-gray-100 dark:bg-gray-900">
              {compareMode ? (
                <div className="flex h-full">
                  {/* Before View */}
                  <div className="flex-1 relative">
                    <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded-lg text-sm">
                      {compareDate.toLocaleDateString()}
                    </div>
                    <div className="h-full bg-gradient-to-br from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                      <Camera className="w-16 h-16 text-gray-500 dark:text-gray-600" />
                    </div>
                  </div>
                  {/* Divider */}
                  <div className="w-1 bg-construction-blue" />
                  {/* After View */}
                  <div className="flex-1 relative">
                    <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-lg text-sm">
                      {selectedDate.toLocaleDateString()}
                    </div>
                    <div className="h-full bg-gradient-to-br from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center relative">
                      <Camera className="w-16 h-16 text-gray-500 dark:text-gray-600" />
                      {showAIOverlay && selectedCapture && (
                        <div className="absolute inset-0">
                          {selectedCapture.aiAnalysis.elementsDetected.map((element, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: index * 0.1 }}
                              className="absolute border-2 border-dashed"
                              style={{
                                left: `${element.location.x}px`,
                                top: `${element.location.y}px`,
                                width: `${element.location.width}px`,
                                height: `${element.location.height}px`,
                                borderColor: element.status === 'completed' ? '#10b981' : 
                                           element.status === 'in_progress' ? '#3b82f6' : '#9ca3af'
                              }}
                            >
                              <div className={cn(
                                "absolute -top-6 left-0 text-xs px-2 py-1 rounded",
                                element.status === 'completed' && "bg-green-500 text-white",
                                element.status === 'in_progress' && "bg-blue-500 text-white",
                                element.status === 'planned' && "bg-gray-500 text-white"
                              )}>
                                {element.type.replace('_', ' ')} ({Math.round(element.confidence * 100)}%)
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="h-full bg-gradient-to-br from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center relative">
                  <Camera className="w-16 h-16 text-gray-500 dark:text-gray-600" />
                  {showAIOverlay && selectedCapture && (
                    <div className="absolute inset-0">
                      {selectedCapture.aiAnalysis.elementsDetected.map((element, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.1 }}
                          className="absolute border-2 border-dashed"
                          style={{
                            left: `${element.location.x}px`,
                            top: `${element.location.y}px`,
                            width: `${element.location.width}px`,
                            height: `${element.location.height}px`,
                            borderColor: element.status === 'completed' ? '#10b981' : 
                                       element.status === 'in_progress' ? '#3b82f6' : '#9ca3af'
                          }}
                        >
                          <div className={cn(
                            "absolute -top-6 left-0 text-xs px-2 py-1 rounded",
                            element.status === 'completed' && "bg-green-500 text-white",
                            element.status === 'in_progress' && "bg-blue-500 text-white",
                            element.status === 'planned' && "bg-gray-500 text-white"
                          )}>
                            {element.type.replace('_', ' ')} ({Math.round(element.confidence * 100)}%)
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                  {/* Simulated 360 navigation */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    <button className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70">
                      <RotateCw className="w-5 h-5" />
                    </button>
                    <button className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70">
                      <Play className="w-5 h-5" />
                    </button>
                    <button className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70">
                      <Grid className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Viewer Footer */}
            <div className="border-t border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    <MapPin className="w-4 h-4 inline mr-1" />
                    Zone A - Floor 3
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    {selectedDate.toLocaleDateString()}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    <Camera className="w-4 h-4 inline mr-1" />
                    360° Capture
                  </span>
                </div>
                <div className="flex space-x-2">
                  <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                    <Share2 className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                    <Download className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Side Panel */}
        <div className="space-y-6">
          {/* AI Analysis */}
          {selectedCapture && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <Brain className="w-5 h-5 mr-2 text-purple-500" />
                AI Analysis
              </h3>
              
              {/* Detected Elements */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Detected Elements
                </h4>
                <div className="space-y-2">
                  {selectedCapture.aiAnalysis.elementsDetected.map((element, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center">
                        <div className={cn("w-2 h-2 rounded-full mr-2", getStatusColor(element.status).replace('text-', 'bg-'))} />
                        <span className="text-sm text-gray-900 dark:text-white">
                          {element.type.replace('_', ' ')}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {Math.round(element.confidence * 100)}%
                        </span>
                        {element.matchesBIM ? (
                          <CheckCircle2 className="w-4 h-4 text-green-500" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Deviations */}
              {selectedCapture.aiAnalysis.deviations.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Deviations Found
                  </h4>
                  <div className="space-y-2">
                    {selectedCapture.aiAnalysis.deviations.map((deviation, index) => (
                      <div key={index} className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <div className="flex items-start">
                          <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                          <div>
                            <p className="text-sm text-gray-900 dark:text-white font-medium">
                              {deviation.description}
                            </p>
                            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                              Severity: {deviation.severity}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendations */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  AI Recommendations
                </h4>
                <div className="space-y-2">
                  {selectedCapture.aiAnalysis.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start">
                      <TrendingUp className="w-4 h-4 text-construction-blue mr-2 flex-shrink-0 mt-0.5" />
                      <p className="text-sm text-gray-600 dark:text-gray-300">{rec}</p>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {/* Progress by Zone */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Progress by Zone
            </h3>
            <div className="space-y-3">
              {progressByZone.map((zone) => (
                <div key={zone.zone}>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {zone.zone}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {zone.progress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className={cn("h-2 rounded-full transition-all duration-500", getProgressColor(zone.status))}
                      style={{ width: `${zone.progress}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Recent Captures */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Recent Captures
              </h3>
              <button className="text-sm text-construction-blue hover:text-construction-blue/80">
                View All
              </button>
            </div>
            <div className="space-y-3">
              {recentCaptures.map((capture) => (
                <div 
                  key={capture.id}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors"
                >
                  <div className="flex items-center">
                    <Camera className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {capture.location}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {capture.time}
                      </p>
                    </div>
                  </div>
                  <div className={cn(
                    "text-xs font-medium px-2 py-1 rounded",
                    capture.status === 'processed' 
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                      : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                  )}>
                    {capture.status}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Timeline View */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Progress Timeline
          </h3>
          <div className="flex items-center space-x-4">
            <button className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
              Week View
            </button>
            <button className="text-sm text-construction-blue font-medium">
              Month View
            </button>
            <button className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
              Quarter View
            </button>
          </div>
        </div>

        {/* Timeline visualization would go here */}
        <div className="h-48 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-lg flex items-center justify-center">
          <BarChart3 className="w-12 h-12 text-gray-400 dark:text-gray-500" />
        </div>
      </motion.div>
    </div>
  )
}
