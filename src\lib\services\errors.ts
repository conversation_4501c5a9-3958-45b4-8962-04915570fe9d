/**
 * Custom Error Classes for Better Error Handling
 */

export class BaseError extends Error {
  public readonly code: string
  public readonly statusCode: number
  public readonly context?: any
  public readonly timestamp: Date

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    statusCode: number = 500,
    context?: any
  ) {
    super(message)
    this.name = this.constructor.name
    this.code = code
    this.statusCode = statusCode
    this.context = context
    this.timestamp = new Date()
    
    // Capture stack trace
    Error.captureStackTrace(this, this.constructor)
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      context: this.context,
      timestamp: this.timestamp,
      stack: this.stack
    }
  }
}

// Vision Service Errors
export class VisionServiceError extends BaseError {
  constructor(message: string, code: string = 'VISION_ERROR', context?: any) {
    super(message, code, 500, context)
  }
}

export class PDFNotSupportedError extends VisionServiceError {
  constructor(fileName?: string) {
    super(
      'PDF files are not supported by vision analysis. Please convert to an image format (PNG, JPEG, WebP) first.',
      'PDF_NOT_SUPPORTED',
      {
        fileName,
        supportedFormats: ['PNG', 'JPEG', 'WebP']
      }
    )
  }
}

export class ImageProcessingError extends VisionServiceError {
  constructor(message: string, context?: any) {
    super(message, 'IMAGE_PROCESSING_ERROR', context)
  }
}

// Takeoff Service Errors
export class TakeoffServiceError extends BaseError {
  constructor(message: string, code: string = 'TAKEOFF_ERROR', context?: any) {
    super(message, code, 500, context)
  }
}

export class MaterialNotFoundError extends TakeoffServiceError {
  constructor(materialName: string, context?: any) {
    super(
      `Material "${materialName}" not found in supplier database`,
      'MATERIAL_NOT_FOUND',
      { materialName, ...context }
    )
  }
}

export class PricingError extends TakeoffServiceError {
  constructor(message: string, context?: any) {
    super(message, 'PRICING_ERROR', context)
  }
}

// API Errors
export class APIError extends BaseError {
  constructor(
    message: string,
    code: string = 'API_ERROR',
    statusCode: number = 500,
    context?: any
  ) {
    super(message, code, statusCode, context)
  }
}

export class ValidationError extends APIError {
  constructor(message: string, fields?: string[]) {
    super(message, 'VALIDATION_ERROR', 400, { fields })
  }
}

export class AuthenticationError extends APIError {
  constructor(message: string = 'Authentication required') {
    super(message, 'AUTHENTICATION_ERROR', 401)
  }
}

export class AuthorizationError extends APIError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 'AUTHORIZATION_ERROR', 403)
  }
}

export class NotFoundError extends APIError {
  constructor(resource: string, id?: string) {
    super(
      `${resource} not found`,
      'NOT_FOUND',
      404,
      { resource, id }
    )
  }
}

// Gemini AI Errors
export class GeminiError extends BaseError {
  constructor(message: string, code: string = 'GEMINI_ERROR', context?: any) {
    super(message, code, 500, context)
  }
}

export class GeminiAPIKeyError extends GeminiError {
  constructor() {
    super(
      'Gemini API key not found. Please configure GEMINI_API_KEY in environment variables.',
      'API_KEY_MISSING'
    )
  }
}

export class GeminiQuotaError extends GeminiError {
  constructor(context?: any) {
    super(
      'Gemini API quota exceeded. Please try again later or upgrade your plan.',
      'QUOTA_EXCEEDED',
      context
    )
  }
}

export class GeminiTimeoutError extends GeminiError {
  constructor(timeout: number) {
    super(
      `Gemini API request timed out after ${timeout}ms`,
      'TIMEOUT',
      { timeout }
    )
  }
}

// Error handler utility
export function handleError(error: unknown): {
  message: string
  code: string
  statusCode: number
  details?: any
} {
  if (error instanceof BaseError) {
    return {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      details: error.context
    }
  }
  
  if (error instanceof Error) {
    // Check for specific error patterns
    if (error.message.includes('API key')) {
      return {
        message: 'API key configuration error',
        code: 'API_KEY_ERROR',
        statusCode: 500
      }
    }
    
    if (error.message.includes('quota')) {
      return {
        message: 'API quota exceeded',
        code: 'QUOTA_EXCEEDED',
        statusCode: 429
      }
    }
    
    if (error.message.includes('timeout')) {
      return {
        message: 'Request timeout',
        code: 'TIMEOUT',
        statusCode: 504
      }
    }
    
    return {
      message: error.message,
      code: 'UNKNOWN_ERROR',
      statusCode: 500
    }
  }
  
  return {
    message: 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR',
    statusCode: 500
  }
}

// Retry logic for transient errors
export async function retryOperation<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number
    retryDelay?: number
    backoff?: boolean
    shouldRetry?: (error: any) => boolean
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    backoff = true,
    shouldRetry = (error) => {
      // Retry on network errors and rate limits
      if (error instanceof GeminiTimeoutError) return true
      if (error instanceof GeminiQuotaError) return false // Don't retry quota errors
      if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') return true
      if (error.statusCode === 429 || error.statusCode === 503) return true
      return false
    }
  } = options
  
  let lastError: any
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error
      
      if (attempt === maxRetries || !shouldRetry(error)) {
        throw error
      }
      
      const delay = backoff ? retryDelay * Math.pow(2, attempt) : retryDelay
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}