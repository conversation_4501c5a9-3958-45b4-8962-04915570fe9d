import { NextRequest } from 'next/server'
import { jobProcessor } from '@/lib/services/job-processor'

// Server-Sent Events for real-time job progress
export async function GET(request: NextRequest) {
  const url = new URL(request.url)
  const jobId = url.searchParams.get('jobId')
  
  if (!jobId) {
    return new Response('Job ID is required', { status: 400 })
  }

  // Create a new TransformStream for SSE
  const encoder = new TextEncoder()
  const stream = new TransformStream()
  const writer = stream.writable.getWriter()

  // Set up SSE headers
  const response = new Response(stream.readable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  })

  // Send initial connection message
  writer.write(encoder.encode(`data: ${JSON.stringify({ type: 'connected', jobId })}\n\n`))

  // Set up progress listener
  jobProcessor.onProgress(jobId, (progress) => {
    const data = JSON.stringify({
      type: 'progress',
      ...progress
    })
    writer.write(encoder.encode(`data: ${data}\n\n`))

    // Close stream when job is completed or failed
    if (progress.status === 'completed' || progress.status === 'failed') {
      setTimeout(() => {
        writer.close()
      }, 1000)
    }
  })

  // Clean up on disconnect
  request.signal.addEventListener('abort', () => {
    jobProcessor.offProgress(jobId)
    writer.close()
  })

  return response
}