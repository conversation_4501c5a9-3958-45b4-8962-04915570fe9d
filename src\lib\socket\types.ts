// Socket.io event types for BIM collaboration

import { BIMElement, BIMViewerState } from '@/types'

// Re-export BIMViewerState for convenience
export type { BIMViewerState }

// User presence
export interface UserPresence {
  userId: string
  userName: string
  userRole: string
  color: string // User's cursor/selection color
  isActive: boolean
  lastActivity: Date
}

// 3D cursor position
export interface Cursor3D {
  userId: string
  position: {
    x: number
    y: number
    z: number
  }
  direction: {
    x: number
    y: number
    z: number
  }
  screenPosition?: {
    x: number
    y: number
  }
}

// Element selection
export interface ElementSelection {
  userId: string
  elementIds: string[]
  mode: 'single' | 'multiple' | 'box'
  timestamp: Date
}

// Annotation types
export interface Annotation {
  id: string
  userId: string
  userName: string
  elementId?: string
  position: {
    x: number
    y: number
    z: number
  }
  content: string
  type: 'comment' | 'issue' | 'rfi' | 'dimension'
  status: 'open' | 'resolved' | 'closed'
  attachments?: AnnotationAttachment[]
  replies?: AnnotationReply[]
  createdAt: Date
  updatedAt: Date
}

export interface AnnotationAttachment {
  id: string
  url: string
  name: string
  type: string
  size: number
}

export interface AnnotationReply {
  id: string
  userId: string
  userName: string
  content: string
  createdAt: Date
}

// Model changes
export interface ModelChange {
  id: string
  userId: string
  type: 'element_update' | 'element_add' | 'element_delete' | 'property_change'
  elementId: string
  previousState?: Partial<BIMElement>
  newState?: Partial<BIMElement>
  description: string
  timestamp: Date
}

// View state changes
export interface ViewStateChange {
  userId: string
  viewState: Partial<BIMViewerState>
  timestamp: Date
}

// Collaboration room
export interface CollaborationRoom {
  roomId: string
  projectId: string
  modelId: string
  users: UserPresence[]
  annotations: Annotation[]
  activeSelections: Map<string, ElementSelection>
  viewStates: Map<string, BIMViewerState>
  createdAt: Date
}

// Socket event names
export enum SocketEvents {
  // Connection events
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  ERROR = 'error',
  
  // Room events
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_JOINED = 'room_joined',
  ROOM_LEFT = 'room_left',
  ROOM_STATE = 'room_state',
  
  // User presence events
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  USER_PRESENCE_UPDATE = 'user_presence_update',
  USERS_LIST = 'users_list',
  
  // Cursor events
  CURSOR_MOVE = 'cursor_move',
  CURSOR_UPDATE = 'cursor_update',
  
  // Selection events
  ELEMENT_SELECT = 'element_select',
  ELEMENT_DESELECT = 'element_deselect',
  SELECTION_UPDATE = 'selection_update',
  SELECTION_CLEAR = 'selection_clear',
  
  // Annotation events
  ANNOTATION_CREATE = 'annotation_create',
  ANNOTATION_UPDATE = 'annotation_update',
  ANNOTATION_DELETE = 'annotation_delete',
  ANNOTATION_REPLY = 'annotation_reply',
  ANNOTATIONS_SYNC = 'annotations_sync',
  
  // Model change events
  MODEL_CHANGE = 'model_change',
  MODEL_SYNC = 'model_sync',
  MODEL_CONFLICT = 'model_conflict',
  
  // View state events
  VIEW_STATE_CHANGE = 'view_state_change',
  VIEW_STATE_SYNC = 'view_state_sync',
  
  // Collaboration events
  COLLABORATION_START = 'collaboration_start',
  COLLABORATION_END = 'collaboration_end',
  COLLABORATION_LOCK = 'collaboration_lock',
  COLLABORATION_UNLOCK = 'collaboration_unlock',
}

// Socket message types
export interface SocketMessage<T = any> {
  event: SocketEvents
  data: T
  timestamp: Date
  userId: string
}

// Error types
export interface SocketError {
  code: string
  message: string
  details?: any
}

// Client to server events
export interface ClientToServerEvents {
  [SocketEvents.JOIN_ROOM]: (data: { roomId: string; userId: string; userName: string; userRole: string }) => void
  [SocketEvents.LEAVE_ROOM]: (data: { roomId: string; userId: string }) => void
  [SocketEvents.CURSOR_MOVE]: (data: Cursor3D) => void
  [SocketEvents.ELEMENT_SELECT]: (data: ElementSelection) => void
  [SocketEvents.ELEMENT_DESELECT]: (data: { userId: string; elementIds: string[] }) => void
  [SocketEvents.ANNOTATION_CREATE]: (data: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'>) => void
  [SocketEvents.ANNOTATION_UPDATE]: (data: { id: string; updates: Partial<Annotation> }) => void
  [SocketEvents.ANNOTATION_DELETE]: (data: { id: string; userId: string }) => void
  [SocketEvents.ANNOTATION_REPLY]: (data: { annotationId: string; reply: Omit<AnnotationReply, 'id' | 'createdAt'> }) => void
  [SocketEvents.MODEL_CHANGE]: (data: Omit<ModelChange, 'id' | 'timestamp'>) => void
  [SocketEvents.VIEW_STATE_CHANGE]: (data: ViewStateChange) => void
}

// Server to client events
export interface ServerToClientEvents {
  [SocketEvents.ROOM_JOINED]: (data: { room: CollaborationRoom }) => void
  [SocketEvents.ROOM_LEFT]: (data: { roomId: string; userId: string }) => void
  [SocketEvents.ROOM_STATE]: (data: { room: CollaborationRoom }) => void
  [SocketEvents.USER_JOINED]: (data: { user: UserPresence }) => void
  [SocketEvents.USER_LEFT]: (data: { userId: string }) => void
  [SocketEvents.USER_PRESENCE_UPDATE]: (data: { user: UserPresence }) => void
  [SocketEvents.USERS_LIST]: (data: { users: UserPresence[] }) => void
  [SocketEvents.CURSOR_UPDATE]: (data: { cursors: Cursor3D[] }) => void
  [SocketEvents.SELECTION_UPDATE]: (data: { selection: ElementSelection }) => void
  [SocketEvents.SELECTION_CLEAR]: (data: { userId: string }) => void
  [SocketEvents.ANNOTATIONS_SYNC]: (data: { annotations: Annotation[] }) => void
  [SocketEvents.MODEL_SYNC]: (data: { changes: ModelChange[] }) => void
  [SocketEvents.MODEL_CONFLICT]: (data: { conflict: ModelChange; resolution?: ModelChange }) => void
  [SocketEvents.VIEW_STATE_SYNC]: (data: { viewStates: Record<string, BIMViewerState> }) => void
  [SocketEvents.ERROR]: (data: SocketError) => void
}

// Inter-server events (for scaling)
export interface InterServerEvents {
  ping: () => void
}

// Socket data attached to each socket instance
export interface SocketData {
  userId: string
  userName: string
  userRole: string
  roomId?: string
  color?: string
}