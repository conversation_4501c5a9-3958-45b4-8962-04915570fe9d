/**
 * Google Cloud Vision Service for Construction Drawing Analysis
 * Provides AI-powered image analysis for construction takeoff
 */

import { GoogleGenerativeAI } from '@google/generative-ai'
import { spatialAnalyzer, type SpatialAnalysisResult } from './spatial-analyzer'
import { visionLogger, logProcessingStep, logError } from './logger-wrapper'
import { VisionServiceError, PDFNotSupportedError, ImageProcessingError } from './errors'
import type { CompanyType } from '@/lib/company-types'
import type { DetectedMaterial } from '@/types'
import { materialFilter } from './material-filter'

// Types for Vision Analysis
export interface VisionAnalysisResult {
  materials: DetectedMaterial[]
  text: ExtractedText[]
  dimensions: Dimension[]
  annotations: Annotation[]
  confidence: number
  processingTime: number
  spatialAnalysis?: SpatialAnalysisResult
  symbols?: any[] // From computer vision analyzer
  measurements?: any[] // From computer vision analyzer
  lines?: any[] // From computer vision analyzer
}


export interface ExtractedText {
  text: string
  confidence: number
  location: BoundingBox
  type: 'dimension' | 'label' | 'specification' | 'note'
}

export interface Dimension {
  value: number
  unit: string
  type: 'length' | 'area' | 'volume'
  location: BoundingBox
  confidence: number
}

export interface Annotation {
  type: string
  description: string
  location: BoundingBox
}

export interface BoundingBox {
  x: number
  y: number
  width: number
  height: number
}

export interface ProcessedDrawing {
  id: string
  fileName: string
  pages: DrawingPage[]
  materials: DetectedMaterial[]
  totalConfidence: number
  processingTime: number
  timestamp: Date
}

export interface DrawingPage {
  pageNumber: number
  imageUrl: string
  analysis: VisionAnalysisResult
}

// Construction-specific material patterns
const MATERIAL_PATTERNS = {
  concrete: {
    keywords: ['concrete', 'slab', 'foundation', 'footing', 'column', 'beam', 'wall'],
    patterns: /\b(\d+)\s*(cy|cubic\s*yard|m3|cubic\s*meter)\s*concrete\b/i,
    specifications: ['psi', 'mpa', 'strength', 'mix', 'reinforced']
  },
  steel: {
    keywords: ['steel', 'rebar', 'beam', 'column', 'decking', 'stud', 'joist'],
    patterns: /\b(w\d+x\d+|hss\s*\d+x\d+|#\d+\s*rebar)\b/i,
    specifications: ['astm', 'grade', 'yield', 'gauge']
  },
  drywall: {
    keywords: ['drywall', 'gypsum', 'board', 'sheetrock', 'partition'],
    patterns: /\b(\d+\/\d+"\s*(type\s*[x|c])?)\s*drywall\b/i,
    specifications: ['type', 'thickness', 'fire-rated', 'moisture']
  },
  electrical: {
    keywords: ['outlet', 'switch', 'panel', 'conduit', 'wire', 'circuit'],
    patterns: /\b(\d+)\s*(amp|a)\s*(circuit|breaker|panel)\b/i,
    specifications: ['voltage', 'amperage', 'phase', 'awg']
  },
  plumbing: {
    keywords: ['pipe', 'valve', 'fixture', 'drain', 'vent', 'water'],
    patterns: /\b(\d+)"\s*(pvc|copper|pex|cast\s*iron)\s*pipe\b/i,
    specifications: ['diameter', 'material', 'pressure', 'schedule']
  },
  hvac: {
    keywords: ['duct', 'vav', 'diffuser', 'grille', 'unit', 'thermostat'],
    patterns: /\b(\d+)"\s*x\s*(\d+)"\s*duct\b/i,
    specifications: ['cfm', 'tons', 'btu', 'gauge']
  }
}

// Dimension extraction patterns
const DIMENSION_PATTERNS = [
  /(\d+(?:\.\d+)?)\s*'(?:\s*-?\s*(\d+(?:\.\d+)?)\s*")?/g, // Feet and inches
  /(\d+(?:\.\d+)?)\s*(ft|feet|')/gi,
  /(\d+(?:\.\d+)?)\s*(in|inch|inches|")/gi,
  /(\d+(?:\.\d+)?)\s*(m|meter|meters)/gi,
  /(\d+(?:\.\d+)?)\s*(mm|millimeter|millimeters)/gi,
  /(\d+(?:\.\d+)?)\s*(cm|centimeter|centimeters)/gi
]

export class VisionService {
  private genAI: GoogleGenerativeAI | null = null
  private apiKey: string
  private isInitialized: boolean = false
  
  constructor() {
    // Only access environment variables on the server side
    if (typeof window === 'undefined') {
      this.apiKey = process.env.GEMINI_API_KEY || ''
      if (this.apiKey) {
        this.genAI = new GoogleGenerativeAI(this.apiKey)
        this.isInitialized = true
        visionLogger.info('Vision service initialized successfully', {
          model: 'gemini-2.5-flash',  // Cost-optimized Flash model
          hasApiKey: true
        })
      } else {
        visionLogger.warn('Vision service initialization failed: No API key provided')
      }
    } else {
      // Client side - API key not available
      this.apiKey = ''
      visionLogger.debug('Vision service not available on client side')
    }
  }
  
  /**
   * Check if the service is available
   */
  private checkAvailability(): void {
    if (!this.isInitialized || !this.genAI) {
      throw new VisionServiceError(
        'Vision service is not available. Please ensure the Gemini API key is configured and you are running on the server side.',
        'SERVICE_NOT_AVAILABLE',
        { isInitialized: this.isInitialized, hasGenAI: !!this.genAI }
      )
    }
  }
  
  /**
   * Analyze a construction drawing using AI vision
   */
  async analyzeDrawing(imageData: string | Uint8Array, companyType?: CompanyType | null): Promise<VisionAnalysisResult> {
    this.checkAvailability()
    const startTime = Date.now()
    const analysisId = `analysis-${Date.now()}`
    
    visionLogger.info('Starting drawing analysis', {
      analysisId,
      dataType: typeof imageData,
      dataLength: typeof imageData === 'string' ? imageData.length : imageData.length,
      companyType
    })
    
    try {
      // Validate input
      if (!imageData) {
        throw new Error('No image data provided')
      }
      
      logProcessingStep(visionLogger, 'Input validation completed', { analysisId }, startTime)
      
      // Use Gemini's multimodal capabilities with cost-optimized Flash model
      const model = this.genAI!.getGenerativeModel({
        model: 'gemini-2.5-flash',
        generationConfig: {
          temperature: 0.4,  // Optimized for cost efficiency and consistency
          topK: 20,
          topP: 0.8,
          maxOutputTokens: 8192,
          responseMimeType: 'text/plain',
        }
      })
      
      // Prepare the image - ensure we have clean base64 data
      let base64Data: string
      if (typeof imageData === 'string') {
        // Remove data URL prefix if present
        if (imageData.startsWith('data:')) {
          base64Data = imageData.split(',')[1] || imageData
        } else {
          base64Data = imageData
        }
      } else {
        base64Data = Buffer.from(imageData).toString('base64')
      }
      
      // Detect MIME type from base64 data if possible
      let mimeType = 'image/png'
      if (typeof imageData === 'string' && imageData.startsWith('data:')) {
        const mimeMatch = imageData.match(/data:([^;]+);/)
        if (mimeMatch) {
          mimeType = mimeMatch[1]
        }
      }
      
      // Check if this is a PDF (base64 PDFs start with JVBERi0 which is %PDF-)
      if (base64Data.startsWith('JVBERi0')) {
        visionLogger.warn('PDF file detected - not supported by vision analysis', {
          analysisId,
          mimeType,
          first10Chars: base64Data.substring(0, 10)
        })
        throw new PDFNotSupportedError(mimeType)
      }
      
      // Validate mime type is supported
      const supportedMimeTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/webp']
      if (!supportedMimeTypes.includes(mimeType)) {
        // Check if it's a PDF mime type
        if (mimeType.includes('pdf')) {
          throw new Error('PDF files are not supported by vision analysis. Please convert to an image format (PNG, JPEG, WebP) first.')
        }
        mimeType = 'image/png' // Default to PNG if unsupported
      }
      
      const image = {
        inlineData: {
          data: base64Data,
          mimeType: mimeType
        }
      }
      
      // Get company-specific prompt based on company type
      const prompt = this.getCompanySpecificPrompt(companyType)
      
      logProcessingStep(visionLogger, 'Prepared Gemini prompt', {
        analysisId,
        promptLength: prompt.length,
        imageSize: base64Data.length,
        mimeType,
        companyType
      }, startTime)
      
      // Get AI analysis
      visionLogger.debug('Sending request to Gemini AI', { analysisId })
      const result = await model.generateContent([prompt, image])
      const response = await result.response
      const text = response.text()
      
      logProcessingStep(visionLogger, 'Received Gemini response', {
        analysisId,
        responseLength: text.length,
        responsePreview: text.substring(0, 200) + '...'
      }, startTime)
      
      // Parse AI response and extract structured data
      const analysis = this.parseAIResponse(text)
      
      visionLogger.debug('Parsed AI response', {
        analysisId,
        materialsFound: analysis.materials.length,
        dimensionsFound: analysis.dimensions.length,
        textFound: analysis.text.length,
        annotationsFound: analysis.annotations.length
      })
      
      // Filter materials by company type if specified
      if (companyType) {
        const filteredMaterials = analysis.materials.filter(material =>
          materialFilter.isValidForTradeSync(
            material.type || material.category,
            material.name,
            companyType
          )
        )
        
        visionLogger.info('Filtered materials by company type', {
          analysisId,
          companyType,
          beforeCount: analysis.materials.length,
          afterCount: filteredMaterials.length
        })
        
        analysis.materials = filteredMaterials
      }
      
      // Enhance with pattern matching
      const enhancedAnalysis = await this.enhanceWithPatternMatching(analysis, text, companyType)
      
      logProcessingStep(visionLogger, 'Enhanced with pattern matching', {
        analysisId,
        enhancedMaterials: enhancedAnalysis.materials.length,
        enhancedDimensions: enhancedAnalysis.dimensions.length
      }, startTime)
      
      const processingTime = Date.now() - startTime
      
      // Perform spatial analysis on the detected materials
      const imageWidth = 1920 // Default assumption, should be extracted from image metadata
      const imageHeight = 1080 // Default assumption
      const spatialAnalysis = spatialAnalyzer.analyzeSpatialRelationships(
        enhancedAnalysis.materials,
        enhancedAnalysis.dimensions,
        imageWidth,
        imageHeight
      )
      
      visionLogger.info('Drawing analysis completed successfully', {
        analysisId,
        processingTime,
        totalMaterials: enhancedAnalysis.materials.length,
        confidence: enhancedAnalysis.confidence,
        spatialWarnings: spatialAnalysis?.warnings.length || 0,
        companyType
      })
      
      return {
        ...enhancedAnalysis,
        processingTime,
        spatialAnalysis
      }
    } catch (error) {
      logError(visionLogger, error, 'Drawing analysis failed', {
        analysisId,
        processingTime: Date.now() - startTime
      })
      throw new Error(`Failed to analyze drawing: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  /**
   * Get company-specific prompt for Gemini analysis
   */
  private getCompanySpecificPrompt(companyType?: CompanyType | null): string {
    const basePrompt = `Analyze this construction drawing and identify:\n`
    
    if (!companyType || companyType === 'General Contractor') {
      return basePrompt + `
1. All construction materials (concrete, steel, drywall, MEP components, etc.)
2. Dimensions and measurements with units
3. Text labels and specifications
4. Material quantities where visible
5. Drawing annotations and notes

For each material found, provide:
- Material type and name
- Location in the image (approximate bounding box)
- Any visible specifications or grades
- Quantities if shown
- Confidence level (0-1)

For dimensions, extract:
- Numeric value and unit
- What the dimension refers to
- Location in the drawing

Format the response as structured JSON with categories: materials, dimensions, text, annotations.`
    }
    
    // Company-specific prompts
    const tradePrompts: Record<string, string> = {
      'Electrical Contractor': `
1. ELECTRICAL COMPONENTS ONLY:
   - Electrical panels and switchboards (NOT concrete pads)
   - Conduits (EMT, PVC, rigid) with sizes
   - Wire and cable runs with AWG sizes
   - Lighting fixtures (troffers, LED panels, strips)
   - Lighting controls (occupancy sensors, dimmers, daylight sensors)
   - Switches, receptacles, and outlets
   - Emergency and exit lighting
   - Fire alarm devices
   - Data/telecom equipment
   - Junction boxes and device boxes
   - Grounding systems (ground wire, NOT concrete pads)
   - Low voltage control systems
2. Electrical dimensions and circuit information
3. Electrical specifications and schedules
4. Panel schedules and load calculations
5. Electrical notes and symbols

CRITICAL: DO NOT INCLUDE:
- Transformer pads or equipment pads (these are CONCRETE, not electrical)
- Concrete foundations or slabs
- Structural steel, beams, or columns
- Masonry, cement, or mortar
- Any civil/structural work
- Excavation or grading

If you see "transformer pad", classify it as Structural/Concrete, NOT Electrical.`,
      
      'Plumbing Contractor': `
1. PLUMBING COMPONENTS ONLY:
   - Pipes (copper, PVC, CPVC, PEX) with sizes
   - Plumbing fixtures (toilets, sinks, faucets)
   - Valves and fittings
   - Water heaters and pumps
   - Drainage systems
   - Vents and stacks
   - Gas piping
   - Insulation for pipes
2. Pipe dimensions and slopes
3. Plumbing specifications and schedules
4. Fixture counts and locations
5. Plumbing notes and symbols

IGNORE: Electrical, HVAC, structural steel, concrete unless directly related to plumbing work.`,
      
      'HVAC Contractor': `
1. HVAC COMPONENTS ONLY:
   - Air handling units and RTUs
   - Ductwork with dimensions
   - Diffusers, grilles, and registers
   - VAV boxes and dampers
   - Refrigerant piping
   - Controls and thermostats
   - Exhaust fans and equipment
   - Insulation for ducts
2. Airflow rates (CFM) and duct sizes
3. Equipment specifications and schedules
4. Control diagrams and sequences
5. HVAC notes and symbols

IGNORE: Electrical, plumbing, structural elements unless directly related to HVAC work.`,
      
      'Concrete Contractor': `
1. CONCRETE ELEMENTS ONLY:
   - Foundations and footings
   - Slabs with thickness
   - Concrete walls and columns
   - Reinforcement (rebar) details
   - Concrete beams
   - Expansion joints
   - Concrete finishes
2. Concrete dimensions and elevations
3. Concrete mix specifications (PSI)
4. Reinforcement schedules
5. Concrete notes and details

IGNORE: MEP systems, finishes, steel framing unless directly related to concrete work.`,
      
      'Steel/Metal Contractor': `
1. STEEL/METAL COMPONENTS ONLY:
   - Structural steel beams and columns
   - Metal decking
   - Steel joists and girders
   - Metal stairs and railings
   - Steel connections and bolts
   - Metal studs and framing
   - Miscellaneous metals
2. Steel member sizes and weights
3. Connection details and specifications
4. Steel schedules and details
5. Welding symbols and notes

IGNORE: Concrete, MEP systems, finishes unless directly related to steel work.`
    }
    
    const specificPrompt = tradePrompts[companyType] || tradePrompts['General Contractor']
    
    return basePrompt + specificPrompt + `

For each ${companyType} material found, provide:
- Material type and name (specific to ${companyType})
- Location in the image (approximate bounding box)
- Any visible specifications or grades
- Quantities if shown
- Confidence level (0-1)

For dimensions, extract:
- Numeric value and unit
- What the dimension refers to
- Location in the drawing

Format the response as structured JSON with categories: materials, dimensions, text, annotations.
CRITICAL: Only include materials relevant to ${companyType} work.`
  }
  
  /**
   * Process multiple pages of a drawing
   */
  async processDrawingPages(pages: Array<{ data: string | Uint8Array, pageNumber: number }>, companyType?: CompanyType | null): Promise<ProcessedDrawing> {
    const startTime = Date.now()
    const fileName = `drawing-${Date.now()}`
    
    // Process pages in parallel for better performance
    const pageAnalyses = await Promise.all(
      pages.map(async (page) => {
        const analysis = await this.analyzeDrawing(page.data, companyType)
        return {
          pageNumber: page.pageNumber,
          imageUrl: `data:image/png;base64,${typeof page.data === 'string' ? page.data : Buffer.from(page.data).toString('base64')}`,
          analysis
        }
      })
    )
    
    // Aggregate materials across all pages
    const allMaterials = pageAnalyses.flatMap(page => page.analysis.materials)
    const uniqueMaterials = this.deduplicateMaterials(allMaterials)
    
    // Calculate overall confidence
    const totalConfidence = pageAnalyses.reduce((sum, page) => sum + page.analysis.confidence, 0) / pageAnalyses.length
    
    return {
      id: `processed-${Date.now()}`,
      fileName,
      pages: pageAnalyses,
      materials: uniqueMaterials,
      totalConfidence,
      processingTime: Date.now() - startTime,
      timestamp: new Date()
    }
  }
  
  /**
   * Parse AI response into structured format
   */
  private parseAIResponse(responseText: string): VisionAnalysisResult {
    try {
      // Try to extract JSON from the response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return this.convertToVisionResult(parsed);
      }
    } catch (error) {
      console.warn('Failed to parse JSON response, using fallback parsing', error);
    }
    
    // Fallback parsing for non-JSON responses
    return this.fallbackParsing(responseText);
  }
  
  /**
   * Convert parsed JSON to VisionAnalysisResult
   */
  private convertToVisionResult(parsed: any): VisionAnalysisResult {
    const materials: DetectedMaterial[] = (parsed.materials || []).map((m: any) => ({
      name: m.name || m.material || '',
      type: m.type || 'unknown',
      category: m.category || m.type || 'General',
      quantity: m.quantity || 1,
      unit: m.unit || 'EA',
      specifications: m.specifications || m.specs,
      location: this.locationToString(m.location),
      confidence: m.confidence || 0.7,
      source: 'vision-analysis',
      pageNumber: 1
    }));
    
    const text: ExtractedText[] = (parsed.text || parsed.labels || []).map((t: any) => ({
      text: t.text || t.content || '',
      confidence: t.confidence || 0.8,
      location: t.location || { x: 0, y: 0, width: 50, height: 20 },
      type: this.classifyText(t.text || t.content || '')
    }));
    
    const dimensions: Dimension[] = (parsed.dimensions || []).map((d: any) => ({
      value: parseFloat(d.value || d.number || 0),
      unit: d.unit || 'ft',
      type: d.type || 'length',
      location: d.location || { x: 0, y: 0, width: 40, height: 20 },
      confidence: d.confidence || 0.85
    }));
    
    const annotations: Annotation[] = (parsed.annotations || parsed.notes || []).map((a: any) => ({
      type: a.type || 'note',
      description: a.description || a.text || '',
      location: a.location || { x: 0, y: 0, width: 100, height: 50 }
    }));
    
    return {
      materials,
      text,
      dimensions,
      annotations,
      confidence: parsed.confidence || 0.8,
      processingTime: 0 // Will be set by caller
    };
  }
  
  /**
   * Fallback parsing when JSON extraction fails
   */
  private fallbackParsing(text: string): VisionAnalysisResult {
    const materials: DetectedMaterial[] = [];
    const dimensions: Dimension[] = [];
    const extractedText: ExtractedText[] = [];
    
    // Extract materials using patterns
    Object.entries(MATERIAL_PATTERNS).forEach(([type, config]) => {
      config.keywords.forEach(keyword => {
        if (text.toLowerCase().includes(keyword)) {
          materials.push({
            name: keyword,
            type: type,
            category: type,
            quantity: 1,
            unit: 'EA',
            specifications: undefined,
            location: 'See drawings',
            confidence: 0.6,
            source: 'vision-analysis',
            pageNumber: 1
          });
        }
      });
    });
    
    // Extract dimensions
    DIMENSION_PATTERNS.forEach(pattern => {
      const matches = Array.from(text.matchAll(pattern));
      for (const match of matches) {
        dimensions.push({
          value: parseFloat(match[1]),
          unit: match[2] || 'ft',
          type: 'length',
          location: { x: 0, y: 0, width: 50, height: 20 },
          confidence: 0.7
        });
      }
    });
    
    return {
      materials,
      text: extractedText,
      dimensions,
      annotations: [],
      confidence: 0.5,
      processingTime: 0 // Will be set by caller
    };
  }
  
  /**
   * Enhance analysis with pattern matching
   */
  private async enhanceWithPatternMatching(
    analysis: VisionAnalysisResult,
    rawText: string,
    companyType?: CompanyType | null
  ): Promise<VisionAnalysisResult> {
    // Extract additional materials using regex patterns
    const additionalMaterials: DetectedMaterial[] = [];
    
    // Only use patterns relevant to the company type
    const relevantPatterns = companyType ? this.getRelevantPatterns(companyType) : MATERIAL_PATTERNS;
    
    Object.entries(relevantPatterns).forEach(([type, config]) => {
      const matches = Array.from(rawText.matchAll(config.patterns));
      for (const match of matches) {
        const existingMaterial = analysis.materials.find(m => 
          m.type === type && m.name.includes(match[0])
        );
        
        if (!existingMaterial) {
          const newMaterial: DetectedMaterial = {
            name: match[0],
            type: type,
            category: type,
            quantity: match[1] ? parseFloat(match[1]) : 1,
            unit: match[2] || 'EA',
            specifications: undefined,
            location: 'See drawings',
            confidence: 0.75,
            source: 'vision-analysis',
            pageNumber: 1
          };
          
          // Only add if valid for the trade
          if (!companyType || materialFilter.isValidForTradeSync(type, newMaterial.name, companyType)) {
            additionalMaterials.push(newMaterial);
          }
        }
      }
    });
    
    // Extract additional dimensions
    const additionalDimensions: Dimension[] = [];
    DIMENSION_PATTERNS.forEach(pattern => {
      const matches = Array.from(rawText.matchAll(pattern));
      for (const match of matches) {
        const value = parseFloat(match[1]);
        if (!isNaN(value)) {
          additionalDimensions.push({
            value,
            unit: match[2] || 'ft',
            type: 'length',
            location: { x: 0, y: 0, width: 50, height: 20 },
            confidence: 0.8
          });
        }
      }
    });
    
    return {
      ...analysis,
      materials: [...analysis.materials, ...additionalMaterials],
      dimensions: this.deduplicateDimensions([...analysis.dimensions, ...additionalDimensions])
    };
  }
  
  /**
   * Get relevant material patterns for a specific company type
   */
  private getRelevantPatterns(companyType: CompanyType): typeof MATERIAL_PATTERNS {
    const patterns: any = {};
    
    switch (companyType) {
      case 'Electrical Contractor':
        patterns.electrical = MATERIAL_PATTERNS.electrical;
        break;
      case 'Plumbing Contractor':
        patterns.plumbing = MATERIAL_PATTERNS.plumbing;
        break;
      case 'HVAC Contractor':
        patterns.hvac = MATERIAL_PATTERNS.hvac;
        break;
      case 'Concrete Contractor':
        patterns.concrete = MATERIAL_PATTERNS.concrete;
        break;
      case 'Steel/Metal Contractor':
        patterns.steel = MATERIAL_PATTERNS.steel;
        break;
      case 'Drywall Contractor':
        patterns.drywall = MATERIAL_PATTERNS.drywall;
        break;
      default:
        // General contractor gets all patterns
        return MATERIAL_PATTERNS;
    }
    
    return patterns;
  }
  
  /**
   * Classify text type based on content
   */
  private classifyText(text: string): 'dimension' | 'label' | 'specification' | 'note' {
    const lowerText = text.toLowerCase();
    
    // Check for dimensions
    if (DIMENSION_PATTERNS.some(pattern => pattern.test(text))) {
      return 'dimension';
    }
    
    // Check for specifications
    if (lowerText.includes('type') || lowerText.includes('grade') || 
        lowerText.includes('astm') || lowerText.includes('gauge')) {
      return 'specification';
    }
    
    // Check for labels
    if (text.length < 20 && /^[A-Z]/.test(text)) {
      return 'label';
    }
    
    return 'note';
  }
  
  /**
   * Convert location object to string
   */
  private locationToString(location?: any): string {
    if (!location) return 'See drawings'
    if (typeof location === 'string') return location
    if (location.x !== undefined && location.y !== undefined) {
      return `Zone at (${location.x}, ${location.y})`
    }
    return 'See drawings'
  }

  /**
   * Deduplicate materials based on similarity
   */
  private deduplicateMaterials(materials: DetectedMaterial[]): DetectedMaterial[] {
    const unique: DetectedMaterial[] = [];
    
    materials.forEach(material => {
      const similar = unique.find(m => 
        m.type === material.type && 
        this.calculateSimilarity(m.name, material.name) > 0.8
      );
      
      if (!similar) {
        unique.push(material);
      } else if (material.confidence > similar.confidence) {
        // Replace with higher confidence version
        const index = unique.indexOf(similar);
        unique[index] = material;
      }
    });
    
    return unique;
  }
  
  /**
   * Deduplicate dimensions
   */
  private deduplicateDimensions(dimensions: Dimension[]): Dimension[] {
    const unique: Dimension[] = [];
    
    dimensions.forEach(dim => {
      const similar = unique.find(d => 
        Math.abs(d.value - dim.value) < 0.01 && 
        d.unit === dim.unit
      );
      
      if (!similar) {
        unique.push(dim);
      }
    });
    
    return unique;
  }
  
  /**
   * Calculate string similarity (simple implementation)
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }
  
  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }
}

// Create a factory function instead of a singleton to avoid client-side initialization
let visionServiceInstance: VisionService | null = null

export const getVisionService = (): VisionService => {
  if (!visionServiceInstance) {
    visionServiceInstance = new VisionService()
  }
  return visionServiceInstance
}

// Create a mock service for client-side
class MockVisionService {
  async analyzeDrawing(imageData: string | Uint8Array, companyType?: CompanyType | null): Promise<VisionAnalysisResult> {
    console.warn('Vision analysis is only available on the server side, returning mock data')
    return {
      materials: [],
      text: [],
      dimensions: [],
      annotations: [],
      confidence: 0,
      processingTime: 0
    }
  }
  
  async processDrawingPages(pages: Array<{ data: string | Uint8Array, pageNumber: number }>, companyType?: CompanyType | null): Promise<ProcessedDrawing> {
    console.warn('Vision analysis is only available on the server side, returning mock data')
    return {
      id: `mock-${Date.now()}`,
      fileName: 'mock-drawing',
      pages: [],
      materials: [],
      totalConfidence: 0,
      processingTime: 0,
      timestamp: new Date()
    }
  }
}

// Export appropriate service based on environment
export const visionService = typeof window === 'undefined' ? getVisionService() : new MockVisionService()
