/**
 * Scale Detection and Validation Service for Construction Drawings
 * Accurately detects and validates drawing scales for proper measurements
 */

export interface DrawingScale {
  type: 'architectural' | 'engineering' | 'metric' | 'custom'
  ratio: string // e.g., "1/4\" = 1'-0\"", "1:50", "1\" = 10'"
  scaleFactor: number // Multiplier to convert drawing units to real units
  unit: 'imperial' | 'metric'
  confidence: number
  source: 'detected' | 'user' | 'default'
}

export interface ScaleValidation {
  isValid: boolean
  issues: string[]
  suggestions: string[]
  alternativeScales: DrawingScale[]
}

export interface MeasurementValidation {
  dimension: number
  unit: string
  realWorldValue: number
  realWorldUnit: string
  isRealistic: boolean
  reason?: string
}

export class ScaleDetector {
  private readonly COMMON_SCALES = {
    architectural: [
      { ratio: '1/16" = 1\'-0"', factor: 192, display: '1/16" = 1\'' },
      { ratio: '1/8" = 1\'-0"', factor: 96, display: '1/8" = 1\'' },
      { ratio: '3/16" = 1\'-0"', factor: 64, display: '3/16" = 1\'' },
      { ratio: '1/4" = 1\'-0"', factor: 48, display: '1/4" = 1\'' },
      { ratio: '3/8" = 1\'-0"', factor: 32, display: '3/8" = 1\'' },
      { ratio: '1/2" = 1\'-0"', factor: 24, display: '1/2" = 1\'' },
      { ratio: '3/4" = 1\'-0"', factor: 16, display: '3/4" = 1\'' },
      { ratio: '1" = 1\'-0"', factor: 12, display: '1" = 1\'' },
      { ratio: '1 1/2" = 1\'-0"', factor: 8, display: '1 1/2" = 1\'' },
      { ratio: '3" = 1\'-0"', factor: 4, display: '3" = 1\'' }
    ],
    engineering: [
      { ratio: '1" = 10\'', factor: 120, display: '1" = 10\'' },
      { ratio: '1" = 20\'', factor: 240, display: '1" = 20\'' },
      { ratio: '1" = 30\'', factor: 360, display: '1" = 30\'' },
      { ratio: '1" = 40\'', factor: 480, display: '1" = 40\'' },
      { ratio: '1" = 50\'', factor: 600, display: '1" = 50\'' },
      { ratio: '1" = 60\'', factor: 720, display: '1" = 60\'' },
      { ratio: '1" = 100\'', factor: 1200, display: '1" = 100\'' }
    ],
    metric: [
      { ratio: '1:1', factor: 1, display: '1:1' },
      { ratio: '1:5', factor: 5, display: '1:5' },
      { ratio: '1:10', factor: 10, display: '1:10' },
      { ratio: '1:20', factor: 20, display: '1:20' },
      { ratio: '1:25', factor: 25, display: '1:25' },
      { ratio: '1:50', factor: 50, display: '1:50' },
      { ratio: '1:100', factor: 100, display: '1:100' },
      { ratio: '1:200', factor: 200, display: '1:200' },
      { ratio: '1:500', factor: 500, display: '1:500' },
      { ratio: '1:1000', factor: 1000, display: '1:1000' }
    ]
  }
  
  private readonly SCALE_PATTERNS = [
    // Architectural scales
    /(\d+\/\d+)["']?\s*=\s*1['"](?:\s*-\s*0["'])?/i,
    /(\d+)["']?\s*=\s*1['"](?:\s*-\s*0["'])?/i,
    // Engineering scales
    /1["']?\s*=\s*(\d+)['"]?/i,
    // Metric scales
    /1\s*:\s*(\d+)/i,
    /scale\s*:\s*1\s*:\s*(\d+)/i,
    // Generic scale notation
    /scale\s*[=:]\s*([^\s,]+)/i
  ]
  
  private readonly TYPICAL_DIMENSIONS = {
    door_width: { min: 2.0, max: 4.0, unit: 'ft' },
    corridor_width: { min: 4.0, max: 12.0, unit: 'ft' },
    ceiling_height: { min: 8.0, max: 20.0, unit: 'ft' },
    parking_space: { min: 8.5, max: 9.5, unit: 'ft' },
    column_spacing: { min: 15.0, max: 30.0, unit: 'ft' },
    window_height: { min: 3.0, max: 8.0, unit: 'ft' },
    stair_width: { min: 3.0, max: 6.0, unit: 'ft' }
  }
  
  /**
   * Detect scale from drawing text and annotations
   */
  detectScale(
    text: string[],
    dimensions: Array<{ value: number; unit: string; description?: string }>
  ): DrawingScale | null {
    // First, try to find explicit scale notation
    for (const textItem of text) {
      for (const pattern of this.SCALE_PATTERNS) {
        const match = textItem.match(pattern)
        if (match) {
          return this.parseScaleMatch(match, textItem)
        }
      }
    }
    
    // If no explicit scale found, try to infer from dimensions
    const inferredScale = this.inferScaleFromDimensions(dimensions)
    if (inferredScale) {
      return inferredScale
    }
    
    return null
  }
  
  /**
   * Parse scale from regex match
   */
  private parseScaleMatch(match: RegExpMatchArray, fullText: string): DrawingScale {
    const matchedText = match[0].toLowerCase()
    
    // Architectural scale (e.g., "1/4" = 1'-0")
    if (matchedText.includes('"') && matchedText.includes('=')) {
      const fraction = match[1]
      const [numerator, denominator] = fraction.split('/').map(n => parseInt(n))
      const inchesPerFoot = denominator ? (numerator / denominator) : parseInt(match[1])
      const factor = 12 / inchesPerFoot
      
      return {
        type: 'architectural',
        ratio: match[0],
        scaleFactor: factor,
        unit: 'imperial',
        confidence: 0.95,
        source: 'detected'
      }
    }
    
    // Engineering scale (e.g., 1" = 20')
    if (matchedText.includes('=') && !matchedText.includes(':')) {
      const feet = parseInt(match[1])
      const factor = feet * 12
      
      return {
        type: 'engineering',
        ratio: `1" = ${feet}'`,
        scaleFactor: factor,
        unit: 'imperial',
        confidence: 0.95,
        source: 'detected'
      }
    }
    
    // Metric scale (e.g., 1:50)
    if (matchedText.includes(':')) {
      const factor = parseInt(match[1])
      
      return {
        type: 'metric',
        ratio: `1:${factor}`,
        scaleFactor: factor,
        unit: 'metric',
        confidence: 0.95,
        source: 'detected'
      }
    }
    
    // Default fallback
    return {
      type: 'custom',
      ratio: match[0],
      scaleFactor: 48, // Default to 1/4" = 1'
      unit: 'imperial',
      confidence: 0.5,
      source: 'detected'
    }
  }
  
  /**
   * Infer scale from known dimension patterns
   */
  private inferScaleFromDimensions(
    dimensions: Array<{ value: number; unit: string; description?: string }>
  ): DrawingScale | null {
    const doorDimensions = dimensions.filter(d => 
      d.description?.toLowerCase().includes('door') ||
      (d.value >= 2.5 && d.value <= 3.5 && d.unit === 'ft')
    )
    
    if (doorDimensions.length > 0) {
      // Standard door widths help determine scale
      const avgDoorWidth = doorDimensions.reduce((sum, d) => sum + d.value, 0) / doorDimensions.length
      
      if (Math.abs(avgDoorWidth - 3.0) < 0.5) {
        // Likely architectural scale
        return {
          type: 'architectural',
          ratio: '1/4" = 1\'-0"',
          scaleFactor: 48,
          unit: 'imperial',
          confidence: 0.7,
          source: 'detected'
        }
      }
    }
    
    // Check for metric patterns
    const metricDimensions = dimensions.filter(d => 
      d.unit === 'm' || d.unit === 'mm' || d.unit === 'cm'
    )
    
    if (metricDimensions.length > dimensions.length / 2) {
      // Likely metric drawing
      return {
        type: 'metric',
        ratio: '1:50',
        scaleFactor: 50,
        unit: 'metric',
        confidence: 0.6,
        source: 'detected'
      }
    }
    
    return null
  }
  
  /**
   * Validate detected scale against drawing content
   */
  validateScale(
    scale: DrawingScale,
    dimensions: Array<{ value: number; unit: string; description?: string }>,
    materials: Array<{ type: string; name: string }>
  ): ScaleValidation {
    const issues: string[] = []
    const suggestions: string[] = []
    const alternativeScales: DrawingScale[] = []
    
    // Validate dimensions against typical values
    for (const dim of dimensions) {
      const validation = this.validateDimension(dim, scale)
      if (!validation.isRealistic) {
        issues.push(validation.reason || `Dimension ${dim.value} ${dim.unit} seems unrealistic`)
      }
    }
    
    // Check scale consistency
    const scaleType = this.determineDrawingType(materials)
    const recommendedScales = this.getRecommendedScales(scaleType)
    
    // Find best matching scale
    let bestMatch = scale
    let bestScore = this.scoreScale(scale, dimensions)
    
    for (const testScale of recommendedScales) {
      const score = this.scoreScale(testScale, dimensions)
      if (score > bestScore) {
        bestScore = score
        bestMatch = testScale
        alternativeScales.push(testScale)
      }
    }
    
    if (bestMatch.ratio !== scale.ratio) {
      suggestions.push(`Consider using ${bestMatch.ratio} scale based on detected dimensions`)
    }
    
    // Add general suggestions
    if (scale.confidence < 0.7) {
      suggestions.push('Add explicit scale notation to drawing for better accuracy')
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      suggestions,
      alternativeScales: alternativeScales.slice(0, 3) // Top 3 alternatives
    }
  }
  
  /**
   * Validate individual dimension
   */
  validateDimension(
    dimension: { value: number; unit: string; description?: string },
    scale: DrawingScale
  ): MeasurementValidation {
    // Convert to real-world value
    const realWorldValue = dimension.value * (scale.unit === 'metric' ? 1 : 1)
    const realWorldUnit = dimension.unit
    
    // Check against typical dimensions
    for (const [key, typical] of Object.entries(this.TYPICAL_DIMENSIONS)) {
      if (dimension.description?.toLowerCase().includes(key.replace('_', ' '))) {
        const isRealistic = realWorldValue >= typical.min && realWorldValue <= typical.max
        
        return {
          dimension: dimension.value,
          unit: dimension.unit,
          realWorldValue,
          realWorldUnit,
          isRealistic,
          reason: isRealistic ? undefined : 
            `${key.replace('_', ' ')} typically ranges from ${typical.min} to ${typical.max} ${typical.unit}`
        }
      }
    }
    
    // General validation
    const isRealistic = realWorldValue > 0 && realWorldValue < 1000
    
    return {
      dimension: dimension.value,
      unit: dimension.unit,
      realWorldValue,
      realWorldUnit,
      isRealistic,
      reason: isRealistic ? undefined : 'Dimension seems unusually large or small'
    }
  }
  
  /**
   * Determine drawing type from materials
   */
  private determineDrawingType(
    materials: Array<{ type: string; name: string }>
  ): 'floor_plan' | 'site_plan' | 'detail' | 'elevation' {
    const materialTypes = new Set(materials.map(m => m.type.toLowerCase()))
    
    if (materialTypes.has('door') && materialTypes.has('wall')) {
      return 'floor_plan'
    }
    
    if (materials.some(m => m.name.toLowerCase().includes('property'))) {
      return 'site_plan'
    }
    
    if (materials.length < 5) {
      return 'detail'
    }
    
    return 'elevation'
  }
  
  /**
   * Get recommended scales for drawing type
   */
  private getRecommendedScales(drawingType: string): DrawingScale[] {
    const scales: DrawingScale[] = []
    
    switch (drawingType) {
      case 'floor_plan':
        scales.push(
          { type: 'architectural', ratio: '1/4" = 1\'-0"', scaleFactor: 48, unit: 'imperial', confidence: 1, source: 'default' },
          { type: 'architectural', ratio: '1/8" = 1\'-0"', scaleFactor: 96, unit: 'imperial', confidence: 1, source: 'default' },
          { type: 'metric', ratio: '1:50', scaleFactor: 50, unit: 'metric', confidence: 1, source: 'default' }
        )
        break
      
      case 'site_plan':
        scales.push(
          { type: 'engineering', ratio: '1" = 20\'', scaleFactor: 240, unit: 'imperial', confidence: 1, source: 'default' },
          { type: 'engineering', ratio: '1" = 30\'', scaleFactor: 360, unit: 'imperial', confidence: 1, source: 'default' },
          { type: 'metric', ratio: '1:200', scaleFactor: 200, unit: 'metric', confidence: 1, source: 'default' }
        )
        break
      
      case 'detail':
        scales.push(
          { type: 'architectural', ratio: '3" = 1\'-0"', scaleFactor: 4, unit: 'imperial', confidence: 1, source: 'default' },
          { type: 'architectural', ratio: '1 1/2" = 1\'-0"', scaleFactor: 8, unit: 'imperial', confidence: 1, source: 'default' },
          { type: 'metric', ratio: '1:10', scaleFactor: 10, unit: 'metric', confidence: 1, source: 'default' }
        )
        break
      
      default:
        scales.push(
          { type: 'architectural', ratio: '1/4" = 1\'-0"', scaleFactor: 48, unit: 'imperial', confidence: 1, source: 'default' },
          { type: 'metric', ratio: '1:100', scaleFactor: 100, unit: 'metric', confidence: 1, source: 'default' }
        )
    }
    
    return scales
  }
  
  /**
   * Score how well a scale fits the dimensions
   */
  private scoreScale(
    scale: DrawingScale,
    dimensions: Array<{ value: number; unit: string; description?: string }>
  ): number {
    let score = 0
    let validDimensions = 0
    
    for (const dim of dimensions) {
      const validation = this.validateDimension(dim, scale)
      if (validation.isRealistic) {
        score += 1
      }
      validDimensions++
    }
    
    return validDimensions > 0 ? score / validDimensions : 0
  }
  
  /**
   * Convert measurement between scales
   */
  convertMeasurement(
    value: number,
    fromScale: DrawingScale,
    toScale: DrawingScale
  ): number {
    // Convert to real-world units first
    const realWorldValue = value * fromScale.scaleFactor
    
    // Then convert to target scale
    return realWorldValue / toScale.scaleFactor
  }
  
  /**
   * Get all common scales for dropdown selection
   */
  getAllScales(): Array<{ category: string; scales: Array<{ value: string; label: string }> }> {
    return [
      {
        category: 'Architectural',
        scales: this.COMMON_SCALES.architectural.map(s => ({
          value: s.ratio,
          label: s.display
        }))
      },
      {
        category: 'Engineering',
        scales: this.COMMON_SCALES.engineering.map(s => ({
          value: s.ratio,
          label: s.display
        }))
      },
      {
        category: 'Metric',
        scales: this.COMMON_SCALES.metric.map(s => ({
          value: s.ratio,
          label: s.display
        }))
      }
    ]
  }
}

// Export singleton instance
export const scaleDetector = new ScaleDetector()