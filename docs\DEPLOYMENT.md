# AI Construction Management Platform - Deployment Guide

This guide covers the deployment process for the AI Construction Management platform across various environments and cloud providers.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Local Development](#local-development)
4. [Docker Deployment](#docker-deployment)
5. [Cloud Deployments](#cloud-deployments)
   - [AWS](#aws-deployment)
   - [Vercel](#vercel-deployment)
   - [Railway](#railway-deployment)
6. [Production Considerations](#production-considerations)
7. [Monitoring & Logging](#monitoring--logging)
8. [Security](#security)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

- Node.js 20+ and npm 9+
- Docker and Docker Compose (for containerized deployments)
- PostgreSQL 16+
- Redis 7+
- Valid API keys for:
  - Google Gemini AI
  - Mapbox
  - AWS S3 (for file uploads)
  - SMTP service (for emails)

## Environment Configuration

1. Copy the production environment template:
```bash
cp .env.production .env.production.local
```

2. Fill in all required environment variables:
```bash
# Core Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Database
DATABASE_URL=************************************/dbname

# Authentication
JWT_SECRET=your-secure-jwt-secret-min-32-chars

# AI Services
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-2.0-flash

# External Services
NEXT_PUBLIC_MAPBOX_TOKEN=your-mapbox-token
REDIS_URL=redis://:password@host:6379

# File Storage
S3_BUCKET=your-s3-bucket
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your-aws-access-key
S3_SECRET_ACCESS_KEY=your-aws-secret-key

# Email
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASSWORD=your-sendgrid-api-key
SMTP_FROM=<EMAIL>
```

## Local Development

```bash
# Install dependencies
npm install

# Run database migrations
npx prisma migrate deploy

# Start development server
npm run dev
```

## Docker Deployment

### Using Docker Compose (Recommended)

1. Build and start all services:
```bash
docker-compose up -d
```

2. Run database migrations:
```bash
docker-compose exec app npx prisma migrate deploy
```

3. Check service health:
```bash
docker-compose ps
curl http://localhost:3000/api/health
```

### Using Dockerfile Only

```bash
# Build the image
docker build -t ai-construction-app .

# Run the container
docker run -p 3000:3000 \
  -e DATABASE_URL="postgresql://..." \
  -e JWT_SECRET="..." \
  -e GEMINI_API_KEY="..." \
  ai-construction-app
```

## Cloud Deployments

### AWS Deployment

Prerequisites:
- AWS CLI configured
- ECS cluster created
- RDS PostgreSQL instance
- ElastiCache Redis instance
- S3 bucket for uploads
- Secrets stored in AWS Secrets Manager

Deploy using the provided script:
```bash
./scripts/deploy-aws.sh
```

Manual deployment steps:
1. Build and push Docker image to ECR
2. Update ECS task definition
3. Update ECS service
4. Run database migrations
5. Verify health endpoint

### Vercel Deployment

1. Install Vercel CLI:
```bash
npm i -g vercel
```

2. Deploy to Vercel:
```bash
# Production deployment
./scripts/deploy-vercel.sh production

# Preview deployment
./scripts/deploy-vercel.sh
```

3. Configure environment variables in Vercel dashboard

Note: For database and Redis, use external providers like:
- Neon/Supabase for PostgreSQL
- Upstash for Redis

### Railway Deployment

1. Install Railway CLI:
```bash
npm install -g @railway/cli
```

2. Deploy to Railway:
```bash
./scripts/deploy-railway.sh
```

3. The script will:
   - Link to your Railway project
   - Deploy the application
   - Optionally provision PostgreSQL and Redis
   - Run database migrations

## Production Considerations

### Database Optimization

1. Enable connection pooling:
```env
DATABASE_URL="************************************/db?pgbouncer=true&connection_limit=25"
```

2. Create indexes for performance:
```sql
-- Add indexes for frequently queried columns
CREATE INDEX idx_projects_company_id ON projects(company_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_tasks_project_id ON tasks(project_id);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
```

### Caching Strategy

1. **Static Assets**: Cached for 1 year with immutable headers
2. **API Responses**: No cache for dynamic content
3. **Images**: Cached for 30 days
4. **Health Checks**: Never cached

### Security Headers

All security headers are configured in:
- `next.config.js` for Next.js responses
- `nginx/nginx.conf` for Nginx deployments

Key headers include:
- Content Security Policy (CSP)
- Strict Transport Security (HSTS)
- X-Frame-Options
- X-Content-Type-Options
- Referrer Policy

### Rate Limiting

Nginx configuration includes rate limiting:
- General API: 10 requests/second
- API endpoints: 30 requests/second
- Auth endpoints: 5 requests/minute

## Monitoring & Logging

### Health Checks

- **Full health check**: `GET /api/health`
- **Simple health check**: `HEAD /api/health`

### Metrics Endpoint

Prometheus-compatible metrics available at:
```
GET /api/metrics
```

### Monitoring Stack

1. **Prometheus**: Metrics collection
2. **Grafana**: Visualization
3. **AlertManager**: Alert routing

Deploy monitoring stack:
```bash
docker-compose -f docker-compose.monitoring.yml up -d
```

### Application Logs

- Development: Console output
- Production: Structured JSON logs
- Error tracking: Sentry integration

## Security

### SSL/TLS Configuration

1. Use Let's Encrypt for free SSL certificates:
```bash
certbot --nginx -d your-domain.com -d www.your-domain.com
```

2. Configure strong ciphers in Nginx

### Environment Variables

1. Never commit `.env` files
2. Use secrets management services:
   - AWS Secrets Manager
   - Vercel Environment Variables
   - Railway Variables

### Database Security

1. Use SSL connections:
```env
DATABASE_URL="************************************/db?sslmode=require"
```

2. Implement row-level security where appropriate

### API Security

1. JWT tokens expire after 7 days
2. Refresh tokens expire after 30 days
3. Rate limiting on all endpoints
4. CORS configured for specific origins

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL format
   - Verify network connectivity
   - Ensure SSL mode matches database configuration

2. **Build Failures**
   - Clear `.next` directory
   - Check Node.js version (must be 20+)
   - Verify all dependencies are installed

3. **Memory Issues**
   - Increase Docker container memory limits
   - Optimize Next.js bundle size
   - Enable swap on small VMs

4. **WebSocket Connection Issues**
   - Ensure WebSocket upgrade headers are proxied
   - Check firewall rules for WebSocket ports
   - Verify Redis connection for Socket.io adapter

### Debug Mode

Enable debug logging:
```env
DEBUG=app:*,prisma:*
LOG_LEVEL=debug
```

### Performance Optimization

1. **Enable Next.js optimizations**:
   - Image optimization
   - Font optimization
   - Script optimization

2. **Database query optimization**:
   - Use Prisma query analysis
   - Implement pagination
   - Cache frequently accessed data

3. **CDN Configuration**:
   - Serve static assets from CDN
   - Configure proper cache headers
   - Use geographic distribution

## Backup and Recovery

### Database Backups

1. **Automated backups**:
```bash
# Add to crontab
0 2 * * * pg_dump $DATABASE_URL > /backup/db-$(date +\%Y\%m\%d).sql
```

2. **Point-in-time recovery**:
   - Enable WAL archiving
   - Configure continuous archiving

### Application State

1. **File uploads**: Stored in S3 with versioning
2. **Redis data**: Configure persistence (AOF or RDB)
3. **Configuration**: Version controlled in git

## Scaling Considerations

### Horizontal Scaling

1. **Application servers**:
   - Use load balancer (ALB/NLB)
   - Session affinity for WebSockets
   - Shared Redis for session storage

2. **Database scaling**:
   - Read replicas for queries
   - Connection pooling with PgBouncer
   - Partitioning for large tables

### Vertical Scaling

1. Monitor resource usage via metrics
2. Scale based on:
   - CPU utilization > 70%
   - Memory usage > 85%
   - Response time > 2s (95th percentile)

## Support

For deployment issues:
1. Check application logs
2. Review health check endpoint
3. Verify all environment variables
4. Consult error tracking (Sentry)

For additional help, refer to:
- [Next.js Deployment Docs](https://nextjs.org/docs/deployment)
- [Prisma Production Docs](https://www.prisma.io/docs/guides/deployment)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)