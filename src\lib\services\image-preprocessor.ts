/**
 * Image Preprocessing Service for Construction Drawings
 * Optimizes images for Gemini AI analysis with resolution and format handling
 */

export interface PreprocessingOptions {
  maxWidth?: number
  maxHeight?: number
  targetFormat?: 'png' | 'jpeg' | 'webp'
  quality?: number
  enhanceContrast?: boolean
  removeNoise?: boolean
  autoRotate?: boolean
  extractText?: boolean
}

export interface PreprocessingResult {
  data: string // base64 encoded image
  mimeType: string
  width: number
  height: number
  originalWidth: number
  originalHeight: number
  wasResized: boolean
  processingSteps: string[]
  estimatedTokens: number
}

export interface ImageMetadata {
  width: number
  height: number
  format: string
  fileSize: number
  hasAlpha: boolean
  colorSpace: string
}

export class ImagePreprocessor {
  private readonly MAX_DIMENSION = 3072 // Gemini's max supported dimension
  private readonly OPTIMAL_DIMENSION = 2048 // Optimal for quality vs token usage
  
  /**
   * Preprocess construction drawing for optimal AI analysis
   */
  async preprocessImage(
    imageData: string | Uint8Array,
    options: PreprocessingOptions = {}
  ): Promise<PreprocessingResult> {
    const {
      maxWidth = this.OPTIMAL_DIMENSION,
      maxHeight = this.OPTIMAL_DIMENSION,
      targetFormat = 'png',
      quality = 0.9,
      enhanceContrast = true,
      removeNoise = false,
      autoRotate = false,
      extractText = false
    } = options
    
    const processingSteps: string[] = []
    
    try {
      // Extract base64 data if it's a data URL
      let base64Data: string
      if (typeof imageData === 'string') {
        if (imageData.startsWith('data:')) {
          base64Data = imageData.split(',')[1] || imageData
        } else {
          base64Data = imageData
        }
      } else {
        base64Data = Buffer.from(imageData).toString('base64')
      }
      
      // Get image metadata
      const metadata = await this.getImageMetadata(base64Data)
      
      // Check if resizing is needed
      let processedData = base64Data
      let currentWidth = metadata.width
      let currentHeight = metadata.height
      let wasResized = false
      
      if (metadata.width > maxWidth || metadata.height > maxHeight) {
        processedData = await this.resizeImage(
          processedData,
          metadata,
          maxWidth,
          maxHeight
        )
        
        // Update dimensions after resize
        const aspectRatio = metadata.width / metadata.height
        if (metadata.width > metadata.height) {
          currentWidth = maxWidth
          currentHeight = Math.round(maxWidth / aspectRatio)
        } else {
          currentHeight = maxHeight
          currentWidth = Math.round(maxHeight * aspectRatio)
        }
        
        wasResized = true
        processingSteps.push(`Resized from ${metadata.width}x${metadata.height} to ${currentWidth}x${currentHeight}`)
      }
      
      // Apply enhancements for construction drawings
      if (enhanceContrast) {
        processedData = await this.enhanceConstructionDrawing(processedData)
        processingSteps.push('Enhanced contrast for line detection')
      }
      
      if (removeNoise) {
        processedData = await this.removeNoise(processedData)
        processingSteps.push('Removed noise')
      }
      
      if (autoRotate) {
        const rotationResult = await this.detectAndRotate(processedData)
        if (rotationResult.wasRotated) {
          processedData = rotationResult.data
          processingSteps.push(`Auto-rotated ${rotationResult.angle} degrees`)
        }
      }
      
      // Convert to target format if needed
      if (targetFormat !== metadata.format.toLowerCase()) {
        processedData = await this.convertFormat(
          processedData,
          targetFormat,
          quality
        )
        processingSteps.push(`Converted from ${metadata.format} to ${targetFormat}`)
      }
      
      // Estimate token usage
      const estimatedTokens = this.estimateTokenUsage(currentWidth, currentHeight)
      
      return {
        data: processedData,
        mimeType: `image/${targetFormat}`,
        width: currentWidth,
        height: currentHeight,
        originalWidth: metadata.width,
        originalHeight: metadata.height,
        wasResized,
        processingSteps,
        estimatedTokens
      }
    } catch (error) {
      console.error('Image preprocessing error:', error)
      throw new Error(`Failed to preprocess image: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  /**
   * Get image metadata without full decoding
   */
  private async getImageMetadata(base64Data: string): Promise<ImageMetadata> {
    // Simple metadata extraction based on image headers
    // In a real implementation, would use a proper image library
    
    // Check for PNG
    if (base64Data.startsWith('iVBORw0K')) {
      return this.getPNGMetadata(base64Data)
    }
    
    // Check for JPEG
    if (base64Data.startsWith('/9j/')) {
      return this.getJPEGMetadata(base64Data)
    }
    
    // Default fallback
    return {
      width: 1920,
      height: 1080,
      format: 'unknown',
      fileSize: base64Data.length * 0.75, // Approximate
      hasAlpha: false,
      colorSpace: 'RGB'
    }
  }
  
  /**
   * Extract PNG metadata
   */
  private getPNGMetadata(base64Data: string): ImageMetadata {
    try {
      const buffer = Buffer.from(base64Data, 'base64')
      
      // PNG header check
      if (buffer[0] === 0x89 && buffer[1] === 0x50) {
        // IHDR chunk is always at offset 16
        const width = buffer.readUInt32BE(16)
        const height = buffer.readUInt32BE(20)
        
        return {
          width,
          height,
          format: 'PNG',
          fileSize: buffer.length,
          hasAlpha: true,
          colorSpace: 'RGB'
        }
      }
    } catch (error) {
      console.error('Error parsing PNG metadata:', error)
    }
    
    return {
      width: 1920,
      height: 1080,
      format: 'PNG',
      fileSize: base64Data.length * 0.75,
      hasAlpha: true,
      colorSpace: 'RGB'
    }
  }
  
  /**
   * Extract JPEG metadata
   */
  private getJPEGMetadata(base64Data: string): ImageMetadata {
    try {
      const buffer = Buffer.from(base64Data, 'base64')
      
      // Simple JPEG dimension extraction
      // In practice, would parse EXIF data properly
      let width = 1920
      let height = 1080
      
      // Look for SOF0 marker (0xFFC0)
      for (let i = 0; i < buffer.length - 10; i++) {
        if (buffer[i] === 0xFF && buffer[i + 1] === 0xC0) {
          height = buffer.readUInt16BE(i + 5)
          width = buffer.readUInt16BE(i + 7)
          break
        }
      }
      
      return {
        width,
        height,
        format: 'JPEG',
        fileSize: buffer.length,
        hasAlpha: false,
        colorSpace: 'RGB'
      }
    } catch (error) {
      console.error('Error parsing JPEG metadata:', error)
    }
    
    return {
      width: 1920,
      height: 1080,
      format: 'JPEG',
      fileSize: base64Data.length * 0.75,
      hasAlpha: false,
      colorSpace: 'RGB'
    }
  }
  
  /**
   * Resize image to fit within max dimensions
   * Note: This is a placeholder - real implementation would use Canvas API or Sharp
   */
  private async resizeImage(
    base64Data: string,
    metadata: ImageMetadata,
    maxWidth: number,
    maxHeight: number
  ): Promise<string> {
    // In a real implementation, this would:
    // 1. Decode the image
    // 2. Calculate new dimensions maintaining aspect ratio
    // 3. Resize using high-quality algorithm
    // 4. Re-encode to base64
    
    // For now, return original data
    console.log(`Would resize from ${metadata.width}x${metadata.height} to fit ${maxWidth}x${maxHeight}`)
    return base64Data
  }
  
  /**
   * Enhance construction drawing contrast and clarity
   */
  private async enhanceConstructionDrawing(base64Data: string): Promise<string> {
    // In a real implementation, this would:
    // 1. Increase contrast for better line detection
    // 2. Sharpen edges for clearer text
    // 3. Normalize brightness for consistent analysis
    // 4. Enhance blue lines (common in blueprints)
    
    console.log('Enhancing construction drawing contrast')
    return base64Data
  }
  
  /**
   * Remove noise from scanned drawings
   */
  private async removeNoise(base64Data: string): Promise<string> {
    // In a real implementation, this would:
    // 1. Apply median filter to remove salt-and-pepper noise
    // 2. Use morphological operations to clean up lines
    // 3. Remove small isolated pixels
    
    console.log('Removing noise from drawing')
    return base64Data
  }
  
  /**
   * Detect and correct image rotation
   */
  private async detectAndRotate(base64Data: string): Promise<{
    data: string
    wasRotated: boolean
    angle: number
  }> {
    // In a real implementation, this would:
    // 1. Detect text orientation
    // 2. Find dominant line angles
    // 3. Rotate to correct orientation
    
    return {
      data: base64Data,
      wasRotated: false,
      angle: 0
    }
  }
  
  /**
   * Convert image format
   */
  private async convertFormat(
    base64Data: string,
    targetFormat: 'png' | 'jpeg' | 'webp',
    quality: number
  ): Promise<string> {
    // In a real implementation, this would use Canvas API or image library
    console.log(`Converting to ${targetFormat} with quality ${quality}`)
    return base64Data
  }
  
  /**
   * Estimate token usage based on image dimensions
   * Based on Gemini documentation:
   * - 258 tokens for images up to 384x384
   * - 1290 tokens for 1024x1024
   * - Linear scaling for other sizes
   */
  estimateTokenUsage(width: number, height: number): number {
    const pixels = width * height
    const basePixels = 384 * 384
    const largePixels = 1024 * 1024
    
    if (pixels <= basePixels) {
      return 258
    } else if (pixels >= largePixels) {
      return 1290
    } else {
      // Linear interpolation
      const ratio = (pixels - basePixels) / (largePixels - basePixels)
      return Math.round(258 + ratio * (1290 - 258))
    }
  }
  
  /**
   * Extract drawing information for better analysis
   */
  async extractDrawingInfo(base64Data: string): Promise<{
    title?: string
    scale?: string
    drawingNumber?: string
    date?: string
    revision?: string
  }> {
    // In a real implementation, this would:
    // 1. Use OCR on title block area
    // 2. Extract standard drawing information
    // 3. Parse drawing number formats
    
    return {
      title: 'Construction Drawing',
      scale: '1:50',
      drawingNumber: 'A-101',
      date: new Date().toISOString().split('T')[0],
      revision: 'A'
    }
  }
  
  /**
   * Split large drawings into tiles for processing
   */
  async splitIntoTiles(
    base64Data: string,
    tileSize: number = 1024
  ): Promise<Array<{
    x: number
    y: number
    width: number
    height: number
    data: string
  }>> {
    // In a real implementation, this would:
    // 1. Decode the image
    // 2. Split into overlapping tiles
    // 3. Return tile data with coordinates
    
    console.log(`Would split image into ${tileSize}x${tileSize} tiles`)
    return [{
      x: 0,
      y: 0,
      width: tileSize,
      height: tileSize,
      data: base64Data
    }]
  }
}

// Export singleton instance
export const imagePreprocessor = new ImagePreprocessor()