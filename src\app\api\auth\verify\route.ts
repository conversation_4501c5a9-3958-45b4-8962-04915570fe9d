import { NextRequest, NextResponse } from 'next/server'
import { verifySession } from '@/lib/db/auth'

export async function GET(request: NextRequest) {
  try {
    // Get session cookie from request
    const cookieFromRequest = request.cookies.get('session')
    
    // Verify session
    const sessionData = await verifySession()
    
    return NextResponse.json({
      authenticated: !!sessionData,
      cookiePresent: !!cookieFromRequest,
      cookieValue: cookieFromRequest?.value ? 'present' : 'missing',
      user: sessionData ? {
        id: sessionData.user.id,
        email: sessionData.user.email,
        name: sessionData.user.name,
        role: sessionData.user.role,
      } : null,
    })
  } catch (error) {
    return NextResponse.json({
      authenticated: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}