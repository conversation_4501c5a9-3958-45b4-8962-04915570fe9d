'use client'

import { useState, useCallback, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Upload, FileUp, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import BIMViewer from '@/features/bim-viewer/components/BIMViewer'
import ModelControls from '@/features/bim-viewer/components/ModelControls'
import ModelTree from '@/features/bim-viewer/components/ModelTree'
import ElementInspector from '@/features/bim-viewer/components/ElementInspector'
import { useViewerState } from '@/features/bim-viewer/hooks/useViewerState'
import { ModelLoadEvent, SelectionEvent } from '@/features/bim-viewer/types/bim.types'
import { useDropzone } from 'react-dropzone'
import { CollaborationProvider } from '@/lib/socket/components/CollaborationProvider'
import { CollaborationToolbar } from '@/lib/socket/components/CollaborationToolbar'
import { CursorVisualization } from '@/lib/socket/components/CursorVisualization'
import { SelectionHighlight } from '@/lib/socket/components/SelectionHighlight'
import { AnnotationMarker } from '@/lib/socket/components/AnnotationMarker'
import { useCollaboration } from '@/lib/socket/components/CollaborationProvider'

// Inner component that uses collaboration features
function BIMViewerContent() {
  const [modelUrl, setModelUrl] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showInspector, setShowInspector] = useState(true)
  const [showTree, setShowTree] = useState(true)

  const {
    model,
    setModel,
    selectedElements,
    hiddenElements,
    selectElement,
    clearSelection,
    showElement,
    hideElement,
    setElementsVisibility,
    tools,
    setTool,
    savedViews,
    saveView,
    loadView,
    setCameraPosition,
    setCameraTarget,
    updateElementProgress,
    addElementIssue,
  } = useViewerState()

  // Collaboration features
  const {
    updateCursor,
    selectElements: syncSelectElements,
    deselectElements: syncDeselectElements,
    createAnnotation,
    cursors,
    selections,
    annotations,
  } = useCollaboration()

  // Sync local selection with collaboration
  useEffect(() => {
    if (selectedElements.length > 0) {
      syncSelectElements(selectedElements)
    } else {
      syncDeselectElements([])
    }
  }, [selectedElements, syncSelectElements, syncDeselectElements])

  // File upload handling
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return
    
    const file = acceptedFiles[0]
    if (!file.name.endsWith('.ifc')) {
      alert('Please upload an IFC file')
      return
    }

    setUploading(true)
    try {
      // In a real app, upload to server and get URL
      // For demo, create local URL
      const url = URL.createObjectURL(file)
      setModelUrl(url)
    } catch (error) {
      console.error('Failed to upload file:', error)
      alert('Failed to upload file')
    } finally {
      setUploading(false)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/x-step': ['.ifc'],
    },
    maxFiles: 1,
  })

  // Viewer callbacks
  const handleModelLoad = (event: ModelLoadEvent) => {
    setModel(event.model)
    console.log('Model loaded:', event)
  }

  const handleElementSelect = (event: SelectionEvent) => {
    if (event.elements.length > 0) {
      selectElement(event.elements[0].id)
    }
    
    // Update cursor position in 3D space for collaboration
    if (event.point) {
      updateCursor(event.point, { x: 0, y: 0, z: 1 })
    }
  }

  const handleToolChange = (tool: keyof typeof tools) => {
    // Disable all other tools when enabling a new one
    Object.keys(tools).forEach(t => {
      setTool(t as keyof typeof tools, t === tool)
    })
  }

  const handleViewChange = (view: string | any) => {
    if (typeof view === 'string') {
      // Standard views
      switch (view) {
        case 'top':
          setCameraPosition({ x: 0, y: 50, z: 0 })
          setCameraTarget({ x: 0, y: 0, z: 0 })
          break
        case 'front':
          setCameraPosition({ x: 0, y: 0, z: 50 })
          setCameraTarget({ x: 0, y: 0, z: 0 })
          break
        case 'right':
          setCameraPosition({ x: 50, y: 0, z: 0 })
          setCameraTarget({ x: 0, y: 0, z: 0 })
          break
        case 'iso':
          setCameraPosition({ x: 30, y: 30, z: 30 })
          setCameraTarget({ x: 0, y: 0, z: 0 })
          break
      }
    } else {
      // Saved view
      loadView(view)
    }
  }

  const handleSaveView = () => {
    const name = prompt('Enter view name:')
    if (name) {
      saveView(name)
    }
  }

  const handleToggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  return (
    <div className="flex flex-col h-full">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <h1 className="text-2xl font-bold text-gray-900">BIM Viewer</h1>
        <p className="text-gray-600 mt-1">
          View and analyze 3D building models with real-time collaboration
        </p>
      </motion.div>

      <div className="flex-1 flex gap-4 min-h-0">
        {/* Left sidebar - Model tree */}
        {showTree && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="w-80 flex-shrink-0"
          >
            <ModelTree
              elements={model?.elements || []}
              selectedElements={selectedElements}
              hiddenElements={hiddenElements}
              onElementSelect={selectElement}
              onElementVisibility={(id, visible) => {
                if (visible) showElement(id)
                else hideElement(id)
              }}
              onBatchVisibility={setElementsVisibility}
            />
          </motion.div>
        )}

        {/* Main viewer */}
        <div className="flex-1 relative">
          <Card className="h-full overflow-hidden">
            {!modelUrl ? (
              // Upload area
              <div
                {...getRootProps()}
                className={`h-full flex items-center justify-center cursor-pointer transition-colors ${
                  isDragActive ? 'bg-primary/5' : 'bg-gray-50'
                }`}
              >
                <input {...getInputProps()} />
                <div className="text-center">
                  {uploading ? (
                    <>
                      <Loader2 className="h-12 w-12 text-gray-400 mx-auto mb-4 animate-spin" />
                      <p className="text-gray-600">Uploading model...</p>
                    </>
                  ) : (
                    <>
                      <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-2">
                        {isDragActive
                          ? 'Drop the IFC file here'
                          : 'Drag & drop an IFC file here'}
                      </p>
                      <Button variant="outline" size="sm">
                        <FileUp className="h-4 w-4 mr-2" />
                        Browse Files
                      </Button>
                      <p className="text-xs text-gray-500 mt-2">
                        Supports IFC 2x3 and IFC 4 formats
                      </p>
                    </>
                  )}
                </div>
              </div>
            ) : (
              // 3D viewer
              <div className="relative h-full">
                <BIMViewer
                  modelUrl={modelUrl}
                  onModelLoad={handleModelLoad}
                  onElementSelect={handleElementSelect}
                  className="absolute inset-0"
                />
                
                {/* Controls overlay */}
                <ModelControls
                  tools={tools}
                  onToolChange={handleToolChange}
                  onViewChange={handleViewChange}
                  onZoomIn={() => console.log('Zoom in')}
                  onZoomOut={() => console.log('Zoom out')}
                  onZoomFit={() => console.log('Zoom fit')}
                  onResetView={() => handleViewChange('iso')}
                  onToggleFullscreen={handleToggleFullscreen}
                  savedViews={savedViews}
                  onSaveView={handleSaveView}
                  isFullscreen={isFullscreen}
                />
                
                {/* Collaboration toolbar */}
                <CollaborationToolbar />
              </div>
            )}
          </Card>
        </div>

        {/* Right sidebar - Element inspector */}
        {showInspector && selectedElements.length > 0 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="w-96 flex-shrink-0"
          >
            <ElementInspector
              elements={model?.elements.filter(e => selectedElements.includes(e.id)) || []}
              onClose={clearSelection}
              onUpdateProgress={updateElementProgress}
              onAddIssue={addElementIssue}
            />
          </motion.div>
        )}
      </div>

      {/* Demo models */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="mt-4"
      >
        <p className="text-sm text-gray-600 mb-2">Or try a demo model:</p>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setModelUrl('/demo-models/office-building.ifc')}
          >
            Office Building
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setModelUrl('/demo-models/residential-complex.ifc')}
          >
            Residential Complex
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setModelUrl('/demo-models/warehouse.ifc')}
          >
            Warehouse
          </Button>
        </div>
      </motion.div>
    </div>
  )
}

// Main component that provides collaboration context
export default function BIMViewerPage() {
  // In a real app, these would come from user authentication
  const userId = 'user-' + Math.random().toString(36).substr(2, 9)
  const userName = 'User ' + Math.floor(Math.random() * 100)
  const userRole = 'PROJECT_MANAGER'
  const roomId = 'project-123' // This would be the project ID
  
  return (
    <CollaborationProvider
      roomId={roomId}
      userId={userId}
      userName={userName}
      userRole={userRole}
    >
      <BIMViewerContent />
    </CollaborationProvider>
  )
}