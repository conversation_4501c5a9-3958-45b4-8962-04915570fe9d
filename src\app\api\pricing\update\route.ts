import { NextRequest, NextResponse } from 'next/server'
import { pricingUpdater } from '@/lib/services/pricing-updater'
import { logger } from '@/lib/logger'

export async function POST(request: NextRequest) {
  try {
    logger.info('Manual pricing update requested')
    
    const results = await pricingUpdater.forceUpdate()
    
    const totalUpdated = results.reduce((sum, r) => sum + r.updatedItems, 0)
    const errors = results.flatMap(r => r.errors)
    
    return NextResponse.json({
      success: true,
      message: `Updated ${totalUpdated} pricing items`,
      results,
      errors: errors.length > 0 ? errors : undefined
    })
    
  } catch (error) {
    logger.error('Manual pricing update failed', { error })
    
    return NextResponse.json({
      success: false,
      message: 'Pricing update failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const status = pricingUpdater.getUpdateStatus()
    
    // Calculate estimated 2025 pricing adjustments
    const estimatedAdjustments = {
      electrical: {
        materials: '+10%',
        labor: '+7%',
        overall: '+8.5%'
      },
      general: {
        materials: '+8%',
        labor: '+6%',
        overall: '+7%'
      },
      lastManualUpdate: '2024-12-01',
      recommendedAction: status.enabled ? 'Automatic updates enabled' : 'Manual update recommended'
    }
    
    return NextResponse.json({
      status,
      estimatedAdjustments,
      currentYear: 2025,
      dataYear: 2024,
      needsUpdate: !status.enabled
    })
    
  } catch (error) {
    logger.error('Failed to get pricing update status', { error })
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
