// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "windows"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User authentication and management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  passwordHash  String?
  role          UserRole  @default(CONTRACTOR)
  companyType   CompanyType @default(GENERAL)
  companyName   String?
  emailVerified <PERSON>ole<PERSON>   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastLogin     DateTime?
  isActive      Boolean   @default(true)
  
  // Relations
  sessions      Session[]
  projects      Project[]
  notifications Notification[]
  aiChats       AIChat[]
  takeoffs      Takeoff[]
  activities    Activity[]
  
  @@index([email])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
}

// Projects and construction management
model Project {
  id          String        @id @default(cuid())
  name        String
  description String?
  startDate   DateTime
  endDate     DateTime
  budget      Float
  status      ProjectStatus @default(PLANNING)
  userId      String
  user        User          @relation(fields: [userId], references: [id])
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  
  // Construction-specific fields
  projectType     String
  location        String
  siteArea        Float?
  constructionType String?
  
  // Relations
  schedules   Schedule[]
  safety      SafetyReport[]
  progress    ProgressReport[]
  takeoffs    Takeoff[]
  documents   Document[]
  activities  Activity[]
  
  @@index([userId])
  @@index([status])
}

// AI Chat History
model AIChat {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  role      String   // 'user' or 'assistant'
  content   String   
  createdAt DateTime @default(now())
  
  // Context
  projectId String?
  context   String?
  
  @@index([userId])
  @@index([createdAt])
}

// Takeoff and Estimating
model Takeoff {
  id          String   @id @default(cuid())
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id])
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  drawingUrl  String?
  status      String   @default("processing")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Results
  totalCost   Float?
  confidence  Float?
  
  // Relations
  items       TakeoffItem[]
  
  @@index([projectId])
  @@index([userId])
}

model TakeoffItem {
  id          String   @id @default(cuid())
  takeoffId   String
  takeoff     Takeoff  @relation(fields: [takeoffId], references: [id], onDelete: Cascade)
  
  // Item details
  name        String
  description String?
  quantity    Float
  unit        String
  unitCost    Float
  totalCost   Float
  category    String
  materialType String?
  
  // Supplier info
  supplier    String?
  leadTime    Int?
  
  // AI analysis
  confidence  Float?
  source      String? // 'vision', 'ai', 'manual'
  
  createdAt   DateTime @default(now())
  
  @@index([takeoffId])
}

// Scheduling
model Schedule {
  id          String   @id @default(cuid())
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id])
  taskName    String
  description String?
  startDate   DateTime
  endDate     DateTime
  status      TaskStatus @default(NOT_STARTED)
  priority    Priority   @default(MEDIUM)
  dependencies String[]
  assignedTo  String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([projectId])
  @@index([status])
}

// Safety Management
model SafetyReport {
  id          String   @id @default(cuid())
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id])
  reportDate  DateTime @default(now())
  score       Int      // 0-100
  violations  String
  incidents   String
  ppeCompliance Float
  observations String   
  recommendations String[] 
  createdAt   DateTime @default(now())
  
  @@index([projectId])
  @@index([reportDate])
}

// Progress Tracking
model ProgressReport {
  id          String   @id @default(cuid())
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id])
  reportDate  DateTime @default(now())
  completion  Float    // 0-100
  milestones  String
  issues      String
  photoUrls   String[]
  notes       String?  
  weatherConditions String?
  workersOnSite Int?
  createdAt   DateTime @default(now())
  
  @@index([projectId])
  @@index([reportDate])
}

// Document Management
model Document {
  id          String   @id @default(cuid())
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id])
  name        String
  type        DocumentType
  url         String
  size        Int
  uploadedBy  String
  version     Int      @default(1)
  tags        String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([projectId])
  @@index([type])
}

// Activity Log
model Activity {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  projectId   String?
  project     Project? @relation(fields: [projectId], references: [id])
  action      String
  description String
  metadata    String?
  createdAt   DateTime @default(now())
  
  @@index([userId])
  @@index([projectId])
  @@index([createdAt])
}

// Notifications
model Notification {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  title       String
  message     String   
  type        NotificationType
  read        Boolean  @default(false)
  actionUrl   String?
  createdAt   DateTime @default(now())
  
  @@index([userId])
  @@index([read])
}

// Enums
enum UserRole {
  ADMIN
  PROJECT_MANAGER
  CONTRACTOR
  SUBCONTRACTOR
  VIEWER
}

enum CompanyType {
  GENERAL
  ELECTRICAL
  PLUMBING
  HVAC
  ROOFING
  CONCRETE
  STEEL_METAL
  MASONRY
  PAINTING
  FLOORING
  LANDSCAPING
  DEMOLITION
  EXCAVATION
  GLASS_GLAZING
  INSULATION
  DRYWALL
  FIRE_PROTECTION
  ELEVATOR
  SOLAR_RENEWABLE
  MARINE_UNDERWATER
}

enum ProjectStatus {
  PLANNING
  ACTIVE
  ON_HOLD
  COMPLETED
  CANCELLED
}

enum TaskStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  DELAYED
  CANCELLED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum DocumentType {
  BLUEPRINT
  CONTRACT
  PERMIT
  REPORT
  PHOTO
  INVOICE
  CHANGE_ORDER
  RFI
  SUBMITTAL
  OTHER
}

enum NotificationType {
  INFO
  WARNING
  ERROR
  SUCCESS
  SAFETY_ALERT
  SCHEDULE_UPDATE
  BUDGET_ALERT
  TASK_ASSIGNED
}