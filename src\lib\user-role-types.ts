/**
 * Standardized User Role Types and Utilities
 * 
 * This module provides a centralized definition for user roles across the system
 * with helper functions for role hierarchy, permissions, and validation.
 */

// Standardized UserRole type with uppercase values
export type UserRole = 'ADMIN' | 'PROJECT_MANAGER' | 'CONTRACTOR' | 'SUBCONTRACTOR' | 'VIEWER'

// Role hierarchy levels (higher number = more permissions)
const ROLE_HIERARCHY: Record<UserRole, number> = {
  ADMIN: 5,
  PROJECT_MANAGER: 4,
  CONTRACTOR: 3,
  SUBCONTRACTOR: 2,
  VIEWER: 1
}

// Display names for each role
const ROLE_DISPLAY_NAMES: Record<UserRole, string> = {
  ADMIN: 'Administrator',
  PROJECT_MANAGER: 'Project Manager',
  CONTRACTOR: 'Contractor',
  SUBCONTRACTOR: 'Subcontractor',
  VIEWER: 'Viewer'
}

/**
 * Get the hierarchy level of a role
 * Higher numbers indicate more permissions
 * 
 * @param role - The user role
 * @returns The hierarchy level (1-5)
 */
export function getRoleLevel(role: UserRole): number {
  return ROLE_HIERARCHY[role] || 0
}

/**
 * Check if a user role has permission to perform actions requiring a specific role
 * Uses role hierarchy - higher roles have all permissions of lower roles
 * 
 * @param userRole - The user's current role
 * @param requiredRole - The minimum role required for the action
 * @returns True if the user has permission, false otherwise
 */
export function hasPermission(userRole: UserRole, requiredRole: UserRole): boolean {
  const userLevel = getRoleLevel(userRole)
  const requiredLevel = getRoleLevel(requiredRole)
  return userLevel >= requiredLevel
}

/**
 * Get the display name for a role
 * 
 * @param role - The user role
 * @returns The human-readable display name
 */
export function getRoleDisplayName(role: UserRole): string {
  return ROLE_DISPLAY_NAMES[role] || role
}

/**
 * Validate if a string is a valid UserRole
 * 
 * @param role - The string to validate
 * @returns True if the string is a valid UserRole
 */
export function isValidUserRole(role: string): role is UserRole {
  return Object.keys(ROLE_HIERARCHY).includes(role as UserRole)
}

/**
 * Get all available roles sorted by hierarchy (highest to lowest)
 * 
 * @returns Array of UserRole values
 */
export function getAllRoles(): UserRole[] {
  return Object.keys(ROLE_HIERARCHY)
    .sort((a, b) => ROLE_HIERARCHY[b as UserRole] - ROLE_HIERARCHY[a as UserRole]) as UserRole[]
}

/**
 * Get roles that a user can assign to others based on their own role
 * Users can only assign roles lower than or equal to their own
 * 
 * @param userRole - The role of the user doing the assignment
 * @returns Array of assignable UserRole values
 */
export function getAssignableRoles(userRole: UserRole): UserRole[] {
  const userLevel = getRoleLevel(userRole)
  return getAllRoles().filter(role => getRoleLevel(role) <= userLevel)
}

/**
 * Convert legacy role values to standardized UserRole
 * Provides backwards compatibility for existing data
 * 
 * @param legacyRole - The legacy role value
 * @returns The standardized UserRole or default 'CONTRACTOR'
 */
export function convertLegacyRole(legacyRole: string): UserRole {
  const roleMap: Record<string, UserRole> = {
    // Current standardized values (already uppercase)
    'ADMIN': 'ADMIN',
    'PROJECT_MANAGER': 'PROJECT_MANAGER',
    'CONTRACTOR': 'CONTRACTOR',
    'SUBCONTRACTOR': 'SUBCONTRACTOR',
    'VIEWER': 'VIEWER',
    
    // Legacy lowercase values
    'admin': 'ADMIN',
    'project_manager': 'PROJECT_MANAGER',
    'contractor': 'CONTRACTOR',
    'subcontractor': 'SUBCONTRACTOR',
    'viewer': 'VIEWER',
    
    // Other legacy values from the old system
    'owner': 'ADMIN',
    'site_manager': 'PROJECT_MANAGER',
    'safety_officer': 'PROJECT_MANAGER',
    'estimator': 'CONTRACTOR',
    'field_worker': 'SUBCONTRACTOR'
  }
  
  return roleMap[legacyRole] || 'CONTRACTOR'
}

/**
 * Get role-specific permissions
 * Returns an object with boolean flags for various permissions
 * 
 * @param role - The user role
 * @returns Object with permission flags
 */
export function getRolePermissions(role: UserRole) {
  const level = getRoleLevel(role)
  
  return {
    // System permissions
    canManageUsers: level >= getRoleLevel('ADMIN'),
    canManageCompanySettings: level >= getRoleLevel('ADMIN'),
    canViewAllProjects: level >= getRoleLevel('PROJECT_MANAGER'),
    
    // Project permissions
    canCreateProjects: level >= getRoleLevel('PROJECT_MANAGER'),
    canEditProjects: level >= getRoleLevel('PROJECT_MANAGER'),
    canDeleteProjects: level >= getRoleLevel('ADMIN'),
    canAssignProjectMembers: level >= getRoleLevel('PROJECT_MANAGER'),
    
    // Schedule permissions
    canEditSchedules: level >= getRoleLevel('PROJECT_MANAGER'),
    canViewSchedules: level >= getRoleLevel('VIEWER'),
    
    // Safety permissions
    canCreateSafetyReports: level >= getRoleLevel('CONTRACTOR'),
    canApproveSafetyReports: level >= getRoleLevel('PROJECT_MANAGER'),
    
    // Estimating permissions
    canCreateEstimates: level >= getRoleLevel('CONTRACTOR'),
    canApproveEstimates: level >= getRoleLevel('PROJECT_MANAGER'),
    
    // Document permissions
    canUploadDocuments: level >= getRoleLevel('SUBCONTRACTOR'),
    canDeleteDocuments: level >= getRoleLevel('PROJECT_MANAGER'),
    
    // Analytics permissions
    canViewAnalytics: level >= getRoleLevel('CONTRACTOR'),
    canExportReports: level >= getRoleLevel('PROJECT_MANAGER'),
    
    // AI Assistant permissions
    canUseAIAssistant: level >= getRoleLevel('VIEWER'),
    canConfigureAI: level >= getRoleLevel('ADMIN')
  }
}