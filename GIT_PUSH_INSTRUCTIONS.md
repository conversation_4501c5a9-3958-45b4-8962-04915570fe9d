# Quick Git Push Instructions

## 🚀 To push your code to GitHub:

### Step 1: Update .env.local
Add your GitHub Personal Access Token to `.env.local`:
```
GITHUB_TOKEN=your-actual-token-here
GITHUB_EMAIL=<EMAIL>
```

### Step 2: Create a GitHub Token
1. Go to: https://github.com/settings/tokens
2. Click "Generate new token (classic)"
3. Name: "AI Construction Push"
4. Select scope: `repo` (for full access)
5. Generate and copy the token

### Step 3: Run the push script
```powershell
.\scripts\git-push-with-token.ps1
```

Or use Node.js:
```bash
node scripts/git-push-node.js
```

That's it! Your code will be pushed to GitHub with proper authentication.
