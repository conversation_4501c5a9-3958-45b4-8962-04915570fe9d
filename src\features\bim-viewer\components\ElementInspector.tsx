'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  Info, 
  Package, 
  Ruler, 
  Hash,
  FileText,
  Calendar,
  User,
  AlertCircle,
  CheckCircle,
  Clock,
  ChevronDown,
  ChevronRight
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { BIMElement } from '@/types'
import { cn } from '@/lib/utils'

interface ElementInspectorProps {
  elements: BIMElement[]
  onClose: () => void
  onUpdateProgress?: (elementId: string, progress: number) => void
  onAddIssue?: (elementId: string, issue: string) => void
}

interface PropertySectionProps {
  title: string
  icon: React.ReactNode
  children: React.ReactNode
  defaultExpanded?: boolean
}

const PropertySection: React.FC<PropertySectionProps> = ({ 
  title, 
  icon, 
  children, 
  defaultExpanded = true 
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded)

  return (
    <div className="border-b last:border-b-0">
      <button
        onClick={() => setExpanded(!expanded)}
        className="w-full flex items-center gap-2 p-3 hover:bg-gray-50 transition-colors"
      >
        <div className="text-gray-600">{icon}</div>
        <span className="flex-1 text-left text-sm font-medium">{title}</span>
        {expanded ? (
          <ChevronDown className="h-4 w-4 text-gray-400" />
        ) : (
          <ChevronRight className="h-4 w-4 text-gray-400" />
        )}
      </button>
      <AnimatePresence>
        {expanded && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="px-3 pb-3">{children}</div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default function ElementInspector({
  elements,
  onClose,
  onUpdateProgress,
  onAddIssue,
}: ElementInspectorProps) {
  const [activeTab, setActiveTab] = useState('properties')
  
  if (elements.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6 text-center">
        <Package className="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <p className="text-sm text-gray-600">No element selected</p>
        <p className="text-xs text-gray-500 mt-1">
          Click on an element in the 3D view to inspect its properties
        </p>
      </div>
    )
  }

  const element = elements[0] // For now, show first selected element
  const progress = element.progress || 0
  const issues = element.issues || []

  // Mock property groups
  const generalProperties = {
    'Element ID': element.guid,
    'Type': element.ifcType || element.type,
    'Name': element.name,
    'Category': 'Structural Framing',
    'Family': 'Steel Column',
  }

  const dimensionProperties = {
    'Length': '3000 mm',
    'Width': '300 mm',
    'Height': '300 mm',
    'Volume': '0.27 m³',
    'Area': '3.6 m²',
  }

  const materialProperties = {
    'Material': element.material || 'Steel - ASTM A992',
    'Finish': 'Galvanized',
    'Fire Rating': '2 Hour',
    'Structural Usage': 'Primary',
  }

  const scheduleProperties = {
    'Planned Start': '2025-01-15',
    'Planned End': '2025-01-20',
    'Actual Start': '2025-01-16',
    'Duration': '5 days',
    'Assigned To': 'Steel Crew A',
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      className="bg-white rounded-lg shadow-lg overflow-hidden"
    >
      {/* Header */}
      <div className="p-4 border-b bg-gray-50">
        <div className="flex items-start justify-between">
          <div>
            <h3 className="font-semibold text-sm">{element.name}</h3>
            <p className="text-xs text-gray-600 mt-1">{element.ifcType || element.type}</p>
          </div>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Progress bar */}
        {onUpdateProgress && (
          <div className="mt-3">
            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-600">Progress</span>
              <span className="font-medium">{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {/* Status badges */}
        <div className="flex gap-2 mt-3">
          {progress === 100 && (
            <Badge variant="outline" className="text-xs">
              <CheckCircle className="h-3 w-3 mr-1" />
              Complete
            </Badge>
          )}
          {progress > 0 && progress < 100 && (
            <Badge variant="outline" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              In Progress
            </Badge>
          )}
          {issues.length > 0 && (
            <Badge variant="destructive" className="text-xs">
              <AlertCircle className="h-3 w-3 mr-1" />
              {issues.length} Issues
            </Badge>
          )}
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
        <TabsList className="w-full justify-start rounded-none border-b h-10">
          <TabsTrigger value="properties" className="text-xs">Properties</TabsTrigger>
          <TabsTrigger value="progress" className="text-xs">Progress</TabsTrigger>
          <TabsTrigger value="issues" className="text-xs">Issues</TabsTrigger>
          <TabsTrigger value="documents" className="text-xs">Documents</TabsTrigger>
        </TabsList>

        <div className="max-h-[500px] overflow-y-auto">
          <TabsContent value="properties" className="m-0">
            <PropertySection title="General" icon={<Info className="h-4 w-4" />}>
              <dl className="space-y-2">
                {Object.entries(generalProperties).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-xs">
                    <dt className="text-gray-600">{key}:</dt>
                    <dd className="font-medium text-right">{value}</dd>
                  </div>
                ))}
              </dl>
            </PropertySection>

            <PropertySection title="Dimensions" icon={<Ruler className="h-4 w-4" />}>
              <dl className="space-y-2">
                {Object.entries(dimensionProperties).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-xs">
                    <dt className="text-gray-600">{key}:</dt>
                    <dd className="font-medium text-right">{value}</dd>
                  </div>
                ))}
              </dl>
            </PropertySection>

            <PropertySection title="Material" icon={<Package className="h-4 w-4" />}>
              <dl className="space-y-2">
                {Object.entries(materialProperties).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-xs">
                    <dt className="text-gray-600">{key}:</dt>
                    <dd className="font-medium text-right">{value}</dd>
                  </div>
                ))}
              </dl>
            </PropertySection>

            <PropertySection title="Schedule" icon={<Calendar className="h-4 w-4" />}>
              <dl className="space-y-2">
                {Object.entries(scheduleProperties).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-xs">
                    <dt className="text-gray-600">{key}:</dt>
                    <dd className="font-medium text-right">{value}</dd>
                  </div>
                ))}
              </dl>
            </PropertySection>
          </TabsContent>

          <TabsContent value="progress" className="p-4 m-0">
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Installation Progress</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-xs">Delivered to site</span>
                    <span className="text-xs text-gray-500 ml-auto">Jan 16, 2025</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-xs">Positioned</span>
                    <span className="text-xs text-gray-500 ml-auto">Jan 17, 2025</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span className="text-xs">Welding in progress</span>
                    <span className="text-xs text-gray-500 ml-auto">Started Jan 18</span>
                  </div>
                  <div className="flex items-center gap-2 opacity-50">
                    <div className="h-4 w-4 border-2 border-gray-300 rounded-full" />
                    <span className="text-xs">Inspection</span>
                  </div>
                </div>
              </div>

              {onUpdateProgress && (
                <div>
                  <label className="text-xs font-medium text-gray-600">Update Progress</label>
                  <div className="flex gap-2 mt-2">
                    {[0, 25, 50, 75, 100].map(value => (
                      <Button
                        key={value}
                        size="sm"
                        variant={progress === value ? 'default' : 'outline'}
                        className="flex-1 h-8 text-xs"
                        onClick={() => onUpdateProgress(element.id, value)}
                      >
                        {value}%
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="issues" className="p-4 m-0">
            <div className="space-y-3">
              {issues.length === 0 ? (
                <p className="text-xs text-gray-500 text-center py-4">
                  No issues reported for this element
                </p>
              ) : (
                issues.map((issue, index) => (
                  <div key={index} className="p-3 bg-red-50 rounded-lg">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-xs font-medium">Alignment Issue</p>
                        <p className="text-xs text-gray-600 mt-1">{issue}</p>
                        <p className="text-xs text-gray-500 mt-2">Reported by John Doe • 2 hours ago</p>
                      </div>
                    </div>
                  </div>
                ))
              )}
              
              {onAddIssue && (
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full h-8 text-xs"
                  onClick={() => onAddIssue(element.id, 'New issue')}
                >
                  <AlertCircle className="h-3.5 w-3.5 mr-1" />
                  Report Issue
                </Button>
              )}
            </div>
          </TabsContent>

          <TabsContent value="documents" className="p-4 m-0">
            <div className="space-y-2">
              <div className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
                <FileText className="h-4 w-4 text-gray-400" />
                <div className="flex-1">
                  <p className="text-xs font-medium">Installation Guide</p>
                  <p className="text-xs text-gray-500">PDF • 2.4 MB</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
                <FileText className="h-4 w-4 text-gray-400" />
                <div className="flex-1">
                  <p className="text-xs font-medium">Structural Calculations</p>
                  <p className="text-xs text-gray-500">PDF • 1.8 MB</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
                <FileText className="h-4 w-4 text-gray-400" />
                <div className="flex-1">
                  <p className="text-xs font-medium">Material Certificate</p>
                  <p className="text-xs text-gray-500">PDF • 0.5 MB</p>
                </div>
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </motion.div>
  )
}