// This is your Prisma schema file for SQLite
// Simplified for SQLite compatibility

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "windows"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User authentication and management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  passwordHash  String?
  role          String    @default("CONTRACTOR")
  companyType   String    @default("GENERAL")
  companyName   String?
  emailVerified <PERSON><PERSON><PERSON>   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastLogin     DateTime?
  isActive      Boolean   @default(true)

  // Relations
  sessions       Session[]
  projects       Project[]
  notifications  Notification[]
  aiChats        AIChat[]
  takeoffs       Takeoff[]
  activities     Activity[]
  processingJobs ProcessingJob[]

  @@index([email])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

// Projects and construction management
model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  startDate   DateTime
  endDate     DateTime
  budget      Float
  status      String   @default("PLANNING")
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Construction-specific fields
  projectType      String
  location         String
  siteArea         Float?
  constructionType String?

  // Relations
  schedules      Schedule[]
  safety         SafetyReport[]
  progress       ProgressReport[]
  takeoffs       Takeoff[]
  documents      Document[]
  activities     Activity[]
  processingJobs ProcessingJob[]

  @@index([userId])
  @@index([status])
}

// AI Chat History
model AIChat {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  role      String // 'user' or 'assistant'
  content   String
  createdAt DateTime @default(now())

  // Context
  projectId String?
  context   String?

  @@index([userId])
  @@index([createdAt])
}

// Processing Jobs for file uploads
model ProcessingJob {
  id             String    @id @default(cuid())
  userId         String
  user           User      @relation(fields: [userId], references: [id])
  projectId      String?
  project        Project?  @relation(fields: [projectId], references: [id])
  status         String    @default("pending") // pending, processing, completed, failed
  totalFiles     Int
  processedFiles Int       @default(0)
  failedFiles    Int       @default(0)
  startedAt      DateTime?
  completedAt    DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Job configuration
  companyType    String?
  projectContext String // JSON string of project context

  // Results
  totalItems Int   @default(0)
  totalCost  Float @default(0)

  // Relations
  files    ProcessingFile[]
  takeoffs Takeoff[]

  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model ProcessingFile {
  id       String        @id @default(cuid())
  jobId    String
  job      ProcessingJob @relation(fields: [jobId], references: [id], onDelete: Cascade)
  fileName String
  fileSize Int
  mimeType String
  status   String        @default("pending") // pending, uploading, processing, completed, failed
  progress Int           @default(0)

  // Storage
  storageUrl   String? // S3/Cloud storage URL
  thumbnailUrl String?

  // Processing results
  itemsDetected  Int     @default(0)
  processingTime Int? // milliseconds
  errorMessage   String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([jobId])
  @@index([status])
}

// Takeoff and Estimating
model Takeoff {
  id         String   @id @default(cuid())
  projectId  String
  project    Project  @relation(fields: [projectId], references: [id])
  userId     String
  user       User     @relation(fields: [userId], references: [id])
  drawingUrl String?
  status     String   @default("processing")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Results
  totalCost  Float?
  confidence Float?

  // Relations
  items           TakeoffItem[]
  processingJob   ProcessingJob? @relation(fields: [processingJobId], references: [id])
  processingJobId String?

  @@index([projectId])
  @@index([userId])
}

model TakeoffItem {
  id        String  @id @default(cuid())
  takeoffId String
  takeoff   Takeoff @relation(fields: [takeoffId], references: [id], onDelete: Cascade)

  // Item details
  name         String
  description  String?
  quantity     Float
  unit         String
  unitCost     Float
  totalCost    Float
  category     String
  materialType String?

  // Supplier info
  supplier String?
  leadTime Int?

  // AI analysis
  confidence Float?
  source     String? // 'vision', 'ai', 'manual'

  createdAt DateTime @default(now())

  @@index([takeoffId])
}

// Scheduling
model Schedule {
  id           String   @id @default(cuid())
  projectId    String
  project      Project  @relation(fields: [projectId], references: [id])
  taskName     String
  description  String?
  startDate    DateTime
  endDate      DateTime
  status       String   @default("NOT_STARTED")
  priority     String   @default("MEDIUM")
  dependencies String // Comma-separated list
  assignedTo   String // Comma-separated list
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([projectId])
  @@index([status])
}

// Safety Management
model SafetyReport {
  id              String   @id @default(cuid())
  projectId       String
  project         Project  @relation(fields: [projectId], references: [id])
  reportDate      DateTime @default(now())
  score           Int // 0-100
  violations      String // JSON string
  incidents       String // JSON string
  ppeCompliance   Float
  observations    String
  recommendations String // Comma-separated list
  createdAt       DateTime @default(now())

  @@index([projectId])
  @@index([reportDate])
}

// Progress Tracking
model ProgressReport {
  id                String   @id @default(cuid())
  projectId         String
  project           Project  @relation(fields: [projectId], references: [id])
  reportDate        DateTime @default(now())
  completion        Float // 0-100
  milestones        String // JSON string
  issues            String // JSON string
  photoUrls         String // Comma-separated list
  notes             String?
  weatherConditions String?
  workersOnSite     Int?
  createdAt         DateTime @default(now())

  @@index([projectId])
  @@index([reportDate])
}

// Document Management
model Document {
  id         String   @id @default(cuid())
  projectId  String
  project    Project  @relation(fields: [projectId], references: [id])
  name       String
  type       String
  url        String
  size       Int
  uploadedBy String
  version    Int      @default(1)
  tags       String // Comma-separated list
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([projectId])
  @@index([type])
}

// Activity Log
model Activity {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  projectId   String?
  project     Project? @relation(fields: [projectId], references: [id])
  action      String
  description String
  metadata    String? // JSON string
  createdAt   DateTime @default(now())

  @@index([userId])
  @@index([projectId])
  @@index([createdAt])
}

// Notifications
model Notification {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  title     String
  message   String
  type      String
  read      Boolean  @default(false)
  actionUrl String?
  createdAt DateTime @default(now())

  @@index([userId])
  @@index([read])
}
