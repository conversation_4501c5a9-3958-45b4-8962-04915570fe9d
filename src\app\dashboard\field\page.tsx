'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  <PERSON><PERSON>,
  Printer,
  QrCode,
  MapPin,
  Play,
  Pause,
  Battery,
  Wifi,
  CheckCircle2,
  AlertTriangle,
  Clock,
  Layers,
  Smartphone,
  Package,
  Wrench,
  HardHat,
  Navigation,
  Target,
  Activity,
  FileText,
  Camera,
  Mic,
  Download,
  Upload,
  Grid,
  Zap,
  Info,
  Settings,
  ChevronRight,
  Eye,
  Building2,
  Calendar,
  Plus,
  TrendingUp
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { FieldLayout, LayoutDeviation } from '@/types'

// Mock data
const mockRobots = [
  {
    id: 'robot-1',
    name: 'FieldPrinter Alpha',
    status: 'active',
    battery: 87,
    location: 'Zone A - Floor 3',
    task: 'Printing MEP layout',
    progress: 65,
    speed: 12500, // sq ft per day
    accuracy: 0.0625, // 1/16 inch
    connectivity: 'excellent'
  },
  {
    id: 'robot-2',
    name: 'FieldPrinter Beta',
    status: 'idle',
    battery: 100,
    location: 'Charging Station',
    task: 'Ready for deployment',
    progress: 0,
    speed: 12500,
    accuracy: 0.0625,
    connectivity: 'good'
  }
]

const mockLayouts: FieldLayout[] = [
  {
    id: '1',
    projectId: 'project-1',
    zone: 'Zone A',
    floor: 3,
    type: 'mep',
    status: 'pending',
    printDate: new Date('2024-06-21'),
    deviations: [],
    createdAt: new Date()
  },
  {
    id: '2',
    projectId: 'project-1',
    zone: 'Zone B',
    floor: 2,
    type: 'walls',
    status: 'printed',
    printDate: new Date('2024-06-20'),
    verifiedBy: 'John Smith',
    deviations: [
      {
        type: 'incorrect_position',
        description: 'Wall offset by 2 inches',
        location: { x: 120, y: 200 },
        resolution: 'Adjusted in BIM model'
      }
    ],
    createdAt: new Date()
  }
]

const workPackages = [
  {
    id: '1',
    name: 'MEP Installation - Zone A',
    assignedTo: 'Team Alpha',
    status: 'in_progress',
    completionRate: 45,
    qrCode: 'WP-001-A',
    documents: 12,
    photos: 34
  },
  {
    id: '2',
    name: 'Concrete Pour - Zone B',
    assignedTo: 'Team Beta',
    status: 'ready',
    completionRate: 0,
    qrCode: 'WP-002-B',
    documents: 8,
    photos: 0
  }
]

const equipment = [
  { id: '1', name: 'Tower Crane #1', status: 'active', utilization: 87, location: 'Zone A' },
  { id: '2', name: 'Concrete Pump', status: 'idle', utilization: 0, location: 'Zone B' },
  { id: '3', name: 'Excavator #3', status: 'maintenance', utilization: 0, location: 'Yard' }
]

const arInstructions = [
  { id: '1', task: 'Install HVAC Duct', views: 45, completions: 12, rating: 4.8 },
  { id: '2', task: 'Frame Interior Wall', views: 67, completions: 23, rating: 4.9 },
  { id: '3', task: 'Install Electrical Box', views: 89, completions: 34, rating: 4.7 }
]

export default function FieldOperationsPage() {
  const [selectedTab, setSelectedTab] = useState<'layout' | 'packages' | 'equipment' | 'ar'>('layout')
  const [selectedRobot, setSelectedRobot] = useState(mockRobots[0])
  const [showLayoutPreview, setShowLayoutPreview] = useState(false)
  const [selectedZone, setSelectedZone] = useState('Zone A')
  const [selectedFloor, setSelectedFloor] = useState(3)

  const getRobotStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-500'
      case 'idle': return 'text-yellow-500'
      case 'error': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getBatteryColor = (level: number) => {
    if (level > 60) return 'text-green-500'
    if (level > 30) return 'text-yellow-500'
    return 'text-red-500'
  }

  const getConnectivityIcon = (level: string) => {
    switch (level) {
      case 'excellent': return '████'
      case 'good': return '███░'
      case 'fair': return '██░░'
      case 'poor': return '█░░░'
      default: return '░░░░'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Field Operations
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            Robotic layout, digital work packages, and AR instructions
          </p>
        </div>
        <div className="flex space-x-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-4 py-2 bg-construction-blue text-white rounded-lg shadow-sm hover:bg-construction-blue/90 transition-colors"
          >
            <Bot className="w-5 h-5 mr-2" />
            Deploy Robot
          </motion.button>
          <button className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <QrCode className="w-5 h-5 mr-2" />
            Scan QR
          </button>
        </div>
      </div>

      {/* Robot Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {mockRobots.map((robot, index) => (
          <motion.div
            key={robot.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            onClick={() => setSelectedRobot(robot)}
            className={cn(
              "bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 cursor-pointer transition-all",
              selectedRobot.id === robot.id && "ring-2 ring-construction-blue"
            )}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className={cn(
                  "p-3 rounded-lg mr-3",
                  robot.status === 'active' ? "bg-green-100 dark:bg-green-900/20" : "bg-gray-100 dark:bg-gray-700"
                )}>
                  <Bot className={cn("w-6 h-6", getRobotStatusColor(robot.status))} />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {robot.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {robot.location}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center justify-end mb-1">
                  <Battery className={cn("w-4 h-4 mr-1", getBatteryColor(robot.battery))} />
                  <span className={cn("text-sm font-medium", getBatteryColor(robot.battery))}>
                    {robot.battery}%
                  </span>
                </div>
                <div className="flex items-center justify-end text-xs text-gray-500 dark:text-gray-400">
                  <Wifi className="w-3 h-3 mr-1" />
                  <span className="font-mono">{getConnectivityIcon(robot.connectivity)}</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  Current Task
                </p>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {robot.task}
                </p>
              </div>

              {robot.status === 'active' && (
                <div>
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span className="text-gray-600 dark:text-gray-400">Progress</span>
                    <span className="font-medium text-gray-900 dark:text-white">{robot.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${robot.progress}%` }}
                      transition={{ duration: 1 }}
                      className="bg-construction-blue h-2 rounded-full"
                    />
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  <span className="font-medium">{robot.speed.toLocaleString()}</span> sq ft/day
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  <span className="font-medium">1/16&quot;</span> accuracy
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Primary Content */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
          >
            {/* Tabs */}
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="flex">
                {[
                  { id: 'layout', label: 'Robotic Layout', icon: Printer },
                  { id: 'packages', label: 'Work Packages', icon: Package },
                  { id: 'equipment', label: 'Equipment', icon: Wrench },
                  { id: 'ar', label: 'AR Instructions', icon: Smartphone }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setSelectedTab(tab.id as any)}
                    className={cn(
                      "flex-1 py-3 px-4 text-sm font-medium transition-colors flex items-center justify-center",
                      selectedTab === tab.id
                        ? "text-construction-blue border-b-2 border-construction-blue bg-gray-50 dark:bg-gray-700"
                        : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                    )}
                  >
                    <tab.icon className="w-4 h-4 mr-2" />
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {selectedTab === 'layout' && (
                <div className="space-y-6">
                  {/* Layout Controls */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <select
                        value={selectedZone}
                        onChange={(e) => setSelectedZone(e.target.value)}
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option>Zone A</option>
                        <option>Zone B</option>
                        <option>Zone C</option>
                        <option>Zone D</option>
                      </select>
                      <select
                        value={selectedFloor}
                        onChange={(e) => setSelectedFloor(Number(e.target.value))}
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value={0}>Ground Floor</option>
                        <option value={1}>Floor 1</option>
                        <option value={2}>Floor 2</option>
                        <option value={3}>Floor 3</option>
                        <option value={4}>Floor 4</option>
                      </select>
                    </div>
                    <button
                      onClick={() => setShowLayoutPreview(!showLayoutPreview)}
                      className="inline-flex items-center px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Preview
                    </button>
                  </div>

                  {/* Layout Preview */}
                  {showLayoutPreview ? (
                    <div className="h-96 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg relative overflow-hidden">
                      {/* Simulated layout lines */}
                      <svg className="absolute inset-0 w-full h-full">
                        <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                          <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="0.5" className="text-gray-300 dark:text-gray-600" />
                        </pattern>
                        <rect width="100%" height="100%" fill="url(#grid)" />
                        
                        {/* Simulated wall layouts */}
                        <line x1="100" y1="100" x2="300" y2="100" stroke="currentColor" strokeWidth="2" className="text-construction-blue" />
                        <line x1="100" y1="100" x2="100" y2="250" stroke="currentColor" strokeWidth="2" className="text-construction-blue" />
                        <line x1="300" y1="100" x2="300" y2="250" stroke="currentColor" strokeWidth="2" className="text-construction-blue" />
                        
                        {/* MEP elements */}
                        <circle cx="150" cy="150" r="5" fill="currentColor" className="text-red-500" />
                        <circle cx="200" cy="150" r="5" fill="currentColor" className="text-red-500" />
                        <circle cx="250" cy="150" r="5" fill="currentColor" className="text-red-500" />
                      </svg>
                      
                      <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded text-sm">
                        {selectedZone} - Floor {selectedFloor}
                      </div>
                      
                      <div className="absolute bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-lg">
                        <div className="text-xs space-y-1">
                          <div className="flex items-center">
                            <div className="w-4 h-0.5 bg-construction-blue mr-2" />
                            <span className="text-gray-600 dark:text-gray-400">Walls</span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-2 h-2 bg-red-500 rounded-full mr-2" />
                            <span className="text-gray-600 dark:text-gray-400">MEP Points</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {mockLayouts.map((layout) => (
                        <div
                          key={layout.id}
                          className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-gray-300 dark:hover:border-gray-600 transition-colors"
                        >
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900 dark:text-white">
                                {layout.type.toUpperCase()} Layout - {layout.zone} Floor {layout.floor}
                              </h4>
                              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                                <span className="flex items-center">
                                  <MapPin className="w-4 h-4 mr-1" />
                                  {layout.zone} - Floor {layout.floor}
                                </span>
                                <span className="flex items-center">
                                  <Calendar className="w-4 h-4 mr-1" />
                                  {layout.printDate?.toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                            <span className={cn(
                              "text-xs font-medium px-2 py-1 rounded",
                              layout.status === 'printed' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                              layout.status === 'pending' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                              layout.status === 'verified' && "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                            )}>
                              {layout.status}
                            </span>
                          </div>
                          
                          {layout.deviations.length > 0 && (
                            <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded flex items-start">
                              <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                              <div className="text-sm">
                                <p className="font-medium text-gray-900 dark:text-white">
                                  {layout.deviations.length} deviation{layout.deviations.length > 1 ? 's' : ''} found
                                </p>
                                <p className="text-gray-600 dark:text-gray-400">
                                  {layout.deviations[0].description}
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {selectedTab === 'packages' && (
                <div className="space-y-4">
                  {workPackages.map((pkg) => (
                    <motion.div
                      key={pkg.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white flex items-center">
                            <QrCode className="w-5 h-5 mr-2 text-gray-400" />
                            {pkg.name}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            Assigned to: {pkg.assignedTo}
                          </p>
                        </div>
                        <span className={cn(
                          "text-xs font-medium px-2 py-1 rounded",
                          pkg.status === 'in_progress' && "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
                          pkg.status === 'ready' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                          pkg.status === 'completed' && "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                        )}>
                          {pkg.status.replace('_', ' ')}
                        </span>
                      </div>
                      
                      <div className="mb-3">
                        <div className="flex items-center justify-between text-sm mb-1">
                          <span className="text-gray-600 dark:text-gray-400">Completion</span>
                          <span className="font-medium text-gray-900 dark:text-white">{pkg.completionRate}%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-construction-blue h-2 rounded-full"
                            style={{ width: `${pkg.completionRate}%` }}
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-4 text-gray-600 dark:text-gray-400">
                          <span className="flex items-center">
                            <FileText className="w-4 h-4 mr-1" />
                            {pkg.documents} docs
                          </span>
                          <span className="flex items-center">
                            <Camera className="w-4 h-4 mr-1" />
                            {pkg.photos} photos
                          </span>
                        </div>
                        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                          <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                            {pkg.qrCode}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                  
                  <button className="w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500 transition-colors">
                    <Plus className="w-5 h-5 mx-auto mb-2" />
                    Create New Work Package
                  </button>
                </div>
              )}

              {selectedTab === 'equipment' && (
                <div className="space-y-6">
                  {/* Equipment List */}
                  <div className="space-y-3">
                    {equipment.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center">
                          <div className={cn(
                            "w-3 h-3 rounded-full mr-3",
                            item.status === 'active' && "bg-green-500",
                            item.status === 'idle' && "bg-yellow-500",
                            item.status === 'maintenance' && "bg-red-500"
                          )} />
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">
                              {item.name}
                            </h4>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Location: {item.location}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {item.utilization}%
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Utilization
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Equipment Map Placeholder */}
                  <div className="h-64 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <MapPin className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-2" />
                      <p className="text-gray-600 dark:text-gray-400">Equipment Location Map</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedTab === 'ar' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {arInstructions.map((instruction, index) => (
                      <motion.div
                        key={instruction.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-lg transition-shadow"
                      >
                        {/* AR Preview Placeholder */}
                        <div className="h-48 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 relative">
                          <Smartphone className="w-12 h-12 text-gray-400 dark:text-gray-500 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                          <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                            AR Preview
                          </div>
                        </div>
                        
                        <div className="p-4">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                            {instruction.task}
                          </h4>
                          <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center space-x-3 text-gray-600 dark:text-gray-400">
                              <span className="flex items-center">
                                <Eye className="w-4 h-4 mr-1" />
                                {instruction.views}
                              </span>
                              <span className="flex items-center">
                                <CheckCircle2 className="w-4 h-4 mr-1" />
                                {instruction.completions}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <span className="text-yellow-500">★</span>
                              <span className="text-sm font-medium text-gray-900 dark:text-white ml-1">
                                {instruction.rating}
                              </span>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div className="flex items-start">
                      <Info className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                          AR Instructions Available
                        </h4>
                        <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                          Workers can access step-by-step AR guidance using mobile devices or AR glasses.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>

        {/* Side Panel */}
        <div className="space-y-6">
          {/* Robot Control */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Robot Control
            </h3>
            
            {selectedRobot.status === 'active' ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <button className="flex-1 mr-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center">
                    <Pause className="w-5 h-5 mr-2" />
                    Pause
                  </button>
                  <button className="flex-1 ml-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center">
                    <Navigation className="w-5 h-5 mr-2" />
                    Return
                  </button>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Print Speed</span>
                    <span className="font-medium text-gray-900 dark:text-white">Normal</span>
                  </div>
                  <input
                    type="range"
                    min="0.5"
                    max="2"
                    step="0.5"
                    defaultValue="1"
                    className="w-full"
                  />
                </div>
                
                <div className="pt-3 border-t border-gray-200 dark:border-gray-700 space-y-2">
                  <p className="text-xs text-gray-500 dark:text-gray-400">Estimated Completion</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    2:45 PM (1h 23m remaining)
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Robot is currently idle and ready for deployment.
                </p>
                <button className="w-full px-4 py-2 bg-construction-blue text-white rounded-lg hover:bg-construction-blue/90 transition-colors flex items-center justify-center">
                  <Play className="w-5 h-5 mr-2" />
                  Start Layout
                </button>
              </div>
            )}
          </motion.div>

          {/* Field Data Collection */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Quick Capture
            </h3>
            <div className="space-y-3">
              <button className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <span className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                  <Camera className="w-4 h-4 mr-2" />
                  Take Photo
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </button>
              <button className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <span className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                  <Mic className="w-4 h-4 mr-2" />
                  Voice Note
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </button>
              <button className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <span className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                  <FileText className="w-4 h-4 mr-2" />
                  Field Report
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </button>
            </div>
          </motion.div>

          {/* Today's Activities */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Today&apos;s Field Activities
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Layouts Printed</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">4</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Work Packages</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">12</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">QR Scans</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">87</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">AR Sessions</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">23</span>
              </div>
              
              <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Productivity</span>
                  <div className="flex items-center">
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-sm font-medium text-green-500">+12%</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Digital Twin Integration */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Digital Twin Integration
          </h3>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Last sync: 5 min ago
            </span>
            <button className="text-sm text-construction-blue hover:text-construction-blue/80">
              Sync Now
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full mb-3">
              <Building2 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">98.5%</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">BIM Accuracy</p>
          </div>
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full mb-3">
              <Target className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">1/16&quot;</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Layout Precision</p>
          </div>
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full mb-3">
              <Zap className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">45x</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Faster than Manual</p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
