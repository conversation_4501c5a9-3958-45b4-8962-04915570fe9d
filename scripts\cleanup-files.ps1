#!/usr/bin/env pwsh
# Clean up test files and temporary files

Write-Host "🧹 Cleaning up test and temporary files..." -ForegroundColor Yellow

# Remove test files
if (Test-Path "__tests__") {
    Write-Host "Removing test directory..." -ForegroundColor Cyan
    Remove-Item -Path "__tests__" -Recurse -Force
    Write-Host "✅ Test directory removed" -ForegroundColor Green
}

# Remove any .delete files
Get-ChildItem -Path . -Recurse -Filter "*.delete" | ForEach-Object {
    Write-Host "Removing $($_.FullName)..." -ForegroundColor Cyan
    Remove-Item $_.FullName -Force
}

# Remove any .backup files
Get-ChildItem -Path . -Recurse -Filter "*.backup" | ForEach-Object {
    Write-Host "Removing $($_.FullName)..." -ForegroundColor Cyan
    Remove-Item $_.FullName -Force
}

# Remove any temporary files
$tempPatterns = @("*.tmp", "*.temp", "*.log", "*.cache")
foreach ($pattern in $tempPatterns) {
    Get-ChildItem -Path . -Recurse -Filter $pattern -ErrorAction SilentlyContinue | ForEach-Object {
        if ($_.FullName -notmatch "node_modules" -and $_.FullName -notmatch ".next") {
            Write-Host "Removing $($_.FullName)..." -ForegroundColor Cyan
            Remove-Item $_.FullName -Force
        }
    }
}

Write-Host "`n✅ Cleanup complete!" -ForegroundColor Green
Write-Host "Code base is clean and production-ready." -ForegroundColor White
