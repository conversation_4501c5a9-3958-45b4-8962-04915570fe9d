# AI Construction Management Platform Environment Configuration
# Based on AceWatt credentials for real functionality

# ============================================
# ENVIRONMENT & CORE SETTINGS
# ============================================
NODE_ENV=development
LOG_LEVEL=debug

# ============================================
# DATABASE & CACHE (Ready for future integration)
# ============================================
DATABASE_URL="file:./dev.db"
REDIS_URL=redis://localhost:6379

# ============================================
# AUTHENTICATION & SECURITY
# ============================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this
SESSION_SECRET=your-session-secret-key-change-this
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# ============================================
# AI SERVICES - GOOGLE GEMINI AI
# ============================================
# Real Google Gemini AI credentials from AceWatt
GEMINI_API_KEY=AIzaSyDL95vL0rTGAfjSyacakG1Dpqb5exfRu-E
GEMINI_MODEL=gemini-2.5-flash
GEMINI_MAX_TOKENS=1000000
GEMINI_TEMPERATURE=0.3
GEMINI_OUTPUT_TOKENS=8192

# Public AI Configuration
NEXT_PUBLIC_ENABLE_AI_ASSISTANT=true
NEXT_PUBLIC_AI_MODEL=gemini-2.5-flash

# ============================================
# GOOGLE SERVICES
# ============================================
# Google Custom Search (for construction material price searching)
GOOGLE_CUSTOM_SEARCH_API_KEY=AIzaSyD-mG6jNousnxwEUp9RWb7aBsNr0A-Ozhk
GOOGLE_CUSTOM_SEARCH_CX=5275621b8f304407c
GOOGLE_SEARCH_PREFERRED_DOMAINS=homedepot.com,lowes.com,ferguson.com,grainger.com

# Google Maps API (for construction site mapping and location services)
GOOGLE_MAPS_API_KEY=AIzaSyBjul622_fwkUK8MmlAyOLRFx6S1aHobVc
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyBjul622_fwkUK8MmlAyOLRFx6S1aHobVc

# ============================================
# EXTERNAL APIS FOR CONSTRUCTION DATA
# ============================================
# Firecrawl API (for construction industry data scraping)
FIRECRAWL_API_KEY=fc-5cbd6d31c6b44b3eabe3cbc28823ee2d

# Weather API (for construction weather monitoring)
WEATHER_API_KEY=your-weather-api-key
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# ============================================
# CONSTRUCTION INDUSTRY APIS
# ============================================
# Building material suppliers
SUPPLYHOUSE_API_KEY=
SUPPLYHOUSE_API_URL=https://api.supplyhouse.com

# Construction equipment APIs
EQUIPMENT_RENTAL_API_KEY=
MACHINERY_DATA_API_KEY=

# ============================================
# INTERNAL SERVICE URLS
# ============================================
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:3000
FRONTEND_URL=http://localhost:3000

# ============================================
# FILE UPLOAD & STORAGE
# ============================================
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=*********  # 100MB in bytes

# AWS S3 Configuration (for construction documents and images)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=ai-construction-uploads

# ============================================
# COMMUNICATION SERVICES
# ============================================
# Email Service (for project notifications)
SENDGRID_API_KEY=
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=AI Construction Management

# SMS Service (for emergency alerts)
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=

# ============================================
# RATE LIMITING & SECURITY
# ============================================
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000  # 15 minutes in ms
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# ============================================
# FEATURE FLAGS
# ============================================
NEXT_PUBLIC_ENABLE_AI_QUOTES=true
NEXT_PUBLIC_ENABLE_PRICE_MONITORING=true
NEXT_PUBLIC_ENABLE_SAFETY_MONITORING=true
NEXT_PUBLIC_ENABLE_PROGRESS_TRACKING=true
NEXT_PUBLIC_ENABLE_SCHEDULE_OPTIMIZATION=true
NEXT_PUBLIC_ENABLE_REAL_TIME_UPDATES=true

# ============================================
# ANALYTICS & MONITORING
# ============================================
# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=

# Error Tracking
SENTRY_DSN=
NEXT_PUBLIC_SENTRY_DSN=

# ============================================
# DEVELOPMENT TOOLS
# ============================================
ENABLE_SWAGGER=true
WS_HEARTBEAT_INTERVAL=30000

# ============================================
# GIT & GITHUB CONFIGURATION
# ============================================
# GitHub Personal Access Token for automated git operations
# Create at: https://github.com/settings/tokens
# Required scopes: repo (for private repos) or public_repo (for public repos)
GITHUB_TOKEN=****************************************
GITHUB_USERNAME=mikeaper323
GITHUB_EMAIL=<EMAIL>

# ============================================
# FILE TYPE CONFIGURATION
# ============================================
NEXT_PUBLIC_ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp
NEXT_PUBLIC_ALLOWED_VIDEO_TYPES=video/mp4,video/mpeg,video/quicktime,video/x-msvideo
NEXT_PUBLIC_ALLOWED_DOCUMENT_TYPES=application/pdf,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document
NEXT_PUBLIC_MAX_FILE_SIZE=100MB
