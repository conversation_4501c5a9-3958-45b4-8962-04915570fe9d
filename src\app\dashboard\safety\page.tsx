'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  AlertTriangle,
  Brain,
  Camera,
  TrendingDown,
  TrendingUp,
  CheckCircle2,
  XCircle,
  Info,
  Eye,
  Download,
  Upload,
  Filter,
  Calendar,
  Users,
  HardHat,
  Activity,
  Bell,
  FileText,
  BarChart3,
  Target,
  Zap,
  Clock,
  MapPin,
  ChevronRight,
  Settings
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { SafetyIncident, SafetyObservation, SafetyMetrics } from '@/types'

// Mock safety data
const mockMetrics: SafetyMetrics = {
  projectId: 'project-1',
  totalIncidents: 3,
  lostTimeIncidents: 1,
  nearMisses: 12,
  safetyScore: 94,
  ppeCompliance: 96,
  hazardsIdentified: 45,
  hazardsResolved: 42,
  trainingCompletion: 88,
  lastUpdated: new Date()
}

const mockIncidents: SafetyIncident[] = [
  {
    id: '1',
    projectId: 'project-1',
    type: 'near_miss',
    severity: 'minor',
    description: 'Worker nearly tripped over unsecured cable',
    location: 'Zone B - Floor 2',
    dateTime: new Date('2024-06-19T14:30:00'),
    reportedBy: '<PERSON>',
    involvedPersonnel: [],
    rootCause: 'Poor housekeeping',
    correctiveActions: ['Cable secured', 'Area cleaned', 'Toolbox talk conducted'],
    status: 'resolved',
    aiDetected: true,
    createdAt: new Date('2024-06-19T14:35:00'),
    updatedAt: new Date('2024-06-19T16:00:00')
  },
  {
    id: '2',
    projectId: 'project-1',
    type: 'violation',
    severity: 'moderate',
    description: 'Worker without hard hat in active construction area',
    location: 'Zone A - Floor 3',
    dateTime: new Date('2024-06-18T10:15:00'),
    reportedBy: 'AI System',
    involvedPersonnel: ['Worker ID: 12345'],
    correctiveActions: ['Worker counseled', 'PPE training scheduled'],
    status: 'open',
    aiDetected: true,
    createdAt: new Date('2024-06-18T10:15:00'),
    updatedAt: new Date('2024-06-18T10:15:00')
  }
]

const mockObservations: SafetyObservation[] = [
  {
    id: '1',
    projectId: 'project-1',
    type: 'positive',
    category: 'ppe',
    description: 'All workers wearing proper PPE in high-risk area',
    location: 'Zone C - Ground Floor',
    imageUrl: '/api/images/observation-1',
    aiDetected: true,
    confidence: 0.98,
    createdBy: 'AI System',
    createdAt: new Date('2024-06-20T09:00:00')
  },
  {
    id: '2',
    projectId: 'project-1',
    type: 'negative',
    category: 'fall_hazard',
    description: 'Unsecured opening on floor 4',
    location: 'Zone A - Floor 4',
    imageUrl: '/api/images/observation-2',
    aiDetected: true,
    confidence: 0.92,
    createdBy: 'AI System',
    createdAt: new Date('2024-06-20T08:30:00')
  }
]

const riskPredictions = [
  {
    type: 'fall_hazard',
    zone: 'Zone A',
    probability: 78,
    impact: 'High',
    recommendation: 'Increase safety barriers and conduct fall protection training',
    timeframe: 'Next 7 days'
  },
  {
    type: 'struck_by',
    zone: 'Zone C',
    probability: 45,
    impact: 'Medium',
    recommendation: 'Review crane operations and establish exclusion zones',
    timeframe: 'Next 14 days'
  },
  {
    type: 'ergonomic',
    zone: 'All Zones',
    probability: 62,
    impact: 'Low',
    recommendation: 'Implement stretching program and review lifting procedures',
    timeframe: 'Next 30 days'
  }
]

const behaviorTrends = [
  { behavior: 'PPE Compliance', trend: 'up', current: 96, previous: 92 },
  { behavior: 'Housekeeping', trend: 'down', current: 78, previous: 85 },
  { behavior: 'Fall Protection', trend: 'up', current: 94, previous: 91 },
  { behavior: 'Tool Safety', trend: 'stable', current: 88, previous: 87 }
]

export default function SafetyMonitoringPage() {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'incidents' | 'observations' | 'predictions'>('overview')
  const [selectedIncident, setSelectedIncident] = useState<SafetyIncident | null>(null)
  const [dateRange, setDateRange] = useState('week')
  const [showAIInsights, setShowAIInsights] = useState(true)

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/20'
      case 'severe': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20'
      case 'moderate': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20'
      case 'minor': return 'text-green-600 bg-green-100 dark:bg-green-900/20'
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'injury': return AlertTriangle
      case 'near_miss': return Info
      case 'violation': return XCircle
      case 'property_damage': return Shield
      default: return AlertTriangle
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            AI Safety Monitoring
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            Predictive analytics and real-time hazard detection
          </p>
        </div>
        <div className="flex space-x-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg shadow-sm hover:bg-red-700 transition-colors"
          >
            <AlertTriangle className="w-5 h-5 mr-2" />
            Report Incident
          </motion.button>
          <button className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <FileText className="w-5 h-5 mr-2" />
            Safety Report
          </button>
        </div>
      </div>

      {/* Safety Score Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <Shield className="w-8 h-8 text-green-500" />
            <span className="text-sm text-green-500 font-medium">+2 pts</span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {mockMetrics.safetyScore}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Safety Score</p>
          <div className="mt-4 flex space-x-1">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className={cn(
                  "h-6 w-1 rounded-full",
                  i < Math.floor(mockMetrics.safetyScore / 20) ? "bg-green-500" : "bg-gray-300 dark:bg-gray-700"
                )}
              />
            ))}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <HardHat className="w-8 h-8 text-blue-500" />
            <span className="text-sm text-green-500 font-medium">+4%</span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {mockMetrics.ppeCompliance}%
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">PPE Compliance</p>
          <div className="mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${mockMetrics.ppeCompliance}%` }} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <AlertTriangle className="w-8 h-8 text-yellow-500" />
            <span className="text-sm text-red-500 font-medium">+2</span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {mockMetrics.nearMisses}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Near Misses</p>
          <button className="mt-4 text-sm text-yellow-500 hover:text-yellow-600 font-medium">
            View Details →
          </button>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <Brain className="w-8 h-8 text-purple-500" />
            <span className="text-sm text-purple-500 font-medium">AI Active</span>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {mockMetrics.hazardsIdentified}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Hazards Detected</p>
          <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
            {mockMetrics.hazardsResolved} resolved ({Math.round((mockMetrics.hazardsResolved / mockMetrics.hazardsIdentified) * 100)}%)
          </div>
        </motion.div>
      </div>

      {/* AI Predictive Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl p-6 text-white"
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Brain className="w-8 h-8 mr-3" />
            <h2 className="text-2xl font-bold">AI Safety Predictions</h2>
          </div>
          <button
            onClick={() => setShowAIInsights(!showAIInsights)}
            className="text-white/80 hover:text-white"
          >
            {showAIInsights ? 'Hide' : 'Show'} Insights
          </button>
        </div>
        
        {showAIInsights && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {riskPredictions.map((prediction, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-4"
              >
                <div className="flex items-start justify-between mb-2">
                  <AlertTriangle className="w-6 h-6" />
                  <span className={cn(
                    "text-xs font-semibold px-2 py-1 rounded",
                    prediction.impact === 'High' ? 'bg-red-500/20' : 
                    prediction.impact === 'Medium' ? 'bg-yellow-500/20' : 'bg-green-500/20'
                  )}>
                    {prediction.impact} Impact
                  </span>
                </div>
                <h3 className="font-semibold mb-1 capitalize">
                  {prediction.type.replace('_', ' ')} Risk
                </h3>
                <p className="text-sm text-white/80 mb-2">
                  {prediction.zone} - {prediction.timeframe}
                </p>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm">Probability</span>
                  <div className="flex items-center">
                    <div className="w-20 bg-white/20 rounded-full h-2 mr-2">
                      <div 
                        className={cn(
                          "h-2 rounded-full",
                          prediction.probability > 70 ? 'bg-red-400' :
                          prediction.probability > 40 ? 'bg-yellow-400' : 'bg-green-400'
                        )}
                        style={{ width: `${prediction.probability}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium">{prediction.probability}%</span>
                  </div>
                </div>
                <p className="text-xs text-white/70">
                  {prediction.recommendation}
                </p>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Incidents & Observations */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
          >
            {/* Tabs */}
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="flex">
                {['overview', 'incidents', 'observations', 'predictions'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setSelectedTab(tab as any)}
                    className={cn(
                      "flex-1 py-3 px-4 text-sm font-medium capitalize transition-colors",
                      selectedTab === tab
                        ? "text-construction-blue border-b-2 border-construction-blue bg-gray-50 dark:bg-gray-700"
                        : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                    )}
                  >
                    {tab}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {selectedTab === 'overview' && (
                <div className="space-y-6">
                  {/* Safety Trend Chart */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Safety Trends
                    </h3>
                    <div className="h-64 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg flex items-center justify-center">
                      <Activity className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                    </div>
                  </div>

                  {/* Behavior Trends */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Behavior Trends
                    </h3>
                    <div className="space-y-3">
                      {behaviorTrends.map((behavior) => (
                        <div key={behavior.behavior} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div className="flex items-center">
                            <div className={cn(
                              "w-8 h-8 rounded-full flex items-center justify-center mr-3",
                              behavior.trend === 'up' ? 'bg-green-100 dark:bg-green-900' :
                              behavior.trend === 'down' ? 'bg-red-100 dark:bg-red-900' :
                              'bg-gray-100 dark:bg-gray-600'
                            )}>
                              {behavior.trend === 'up' ? (
                                <TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400" />
                              ) : behavior.trend === 'down' ? (
                                <TrendingDown className="w-4 h-4 text-red-600 dark:text-red-400" />
                              ) : (
                                <Activity className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                              )}
                            </div>
                            <span className="font-medium text-gray-900 dark:text-white">
                              {behavior.behavior}
                            </span>
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {behavior.previous}%
                            </span>
                            <ChevronRight className="w-4 h-4 text-gray-400" />
                            <span className={cn(
                              "text-sm font-medium",
                              behavior.trend === 'up' ? 'text-green-600 dark:text-green-400' :
                              behavior.trend === 'down' ? 'text-red-600 dark:text-red-400' :
                              'text-gray-600 dark:text-gray-400'
                            )}>
                              {behavior.current}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {selectedTab === 'incidents' && (
                <div className="space-y-4">
                  {mockIncidents.map((incident) => {
                    const Icon = getTypeIcon(incident.type)
                    return (
                      <motion.div
                        key={incident.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-gray-300 dark:hover:border-gray-600 cursor-pointer transition-colors"
                        onClick={() => setSelectedIncident(incident)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start">
                            <div className={cn("p-2 rounded-lg mr-3", getSeverityColor(incident.severity))}>
                              <Icon className="w-5 h-5" />
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900 dark:text-white">
                                {incident.description}
                              </h4>
                              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                                <span className="flex items-center">
                                  <MapPin className="w-4 h-4 mr-1" />
                                  {incident.location}
                                </span>
                                <span className="flex items-center">
                                  <Clock className="w-4 h-4 mr-1" />
                                  {incident.dateTime.toLocaleString()}
                                </span>
                                {incident.aiDetected && (
                                  <span className="flex items-center text-purple-500">
                                    <Brain className="w-4 h-4 mr-1" />
                                    AI Detected
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <span className={cn(
                            "text-xs font-medium px-2 py-1 rounded",
                            incident.status === 'open' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                            incident.status === 'investigating' && "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
                            incident.status === 'resolved' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                            incident.status === 'closed' && "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                          )}>
                            {incident.status}
                          </span>
                        </div>
                      </motion.div>
                    )
                  })}
                </div>
              )}

              {selectedTab === 'observations' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {mockObservations.map((observation, index) => (
                    <motion.div
                      key={observation.id}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-lg transition-shadow"
                    >
                      {/* Image Placeholder */}
                      <div className="h-48 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 relative">
                        <Camera className="w-12 h-12 text-gray-400 dark:text-gray-500 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                        {observation.aiDetected && (
                          <div className="absolute top-2 right-2 bg-purple-500 text-white text-xs px-2 py-1 rounded flex items-center">
                            <Brain className="w-3 h-3 mr-1" />
                            AI {Math.round((observation.confidence || 0) * 100)}%
                          </div>
                        )}
                        <div className={cn(
                          "absolute top-2 left-2 text-xs px-2 py-1 rounded",
                          observation.type === 'positive' 
                            ? "bg-green-500 text-white" 
                            : "bg-red-500 text-white"
                        )}>
                          {observation.type}
                        </div>
                      </div>
                      <div className="p-4">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                          {observation.category.replace('_', ' ').charAt(0).toUpperCase() + observation.category.slice(1).replace('_', ' ')}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                          {observation.description}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                          <span>{observation.location}</span>
                          <span>{new Date(observation.createdAt).toLocaleTimeString()}</span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}

              {selectedTab === 'predictions' && (
                <div className="space-y-6">
                  {/* Risk Matrix */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Risk Matrix
                    </h3>
                    <div className="grid grid-cols-3 gap-2">
                      {[
                        { risk: 'Low', zones: ['Zone D'], color: 'bg-green-100 dark:bg-green-900/20' },
                        { risk: 'Medium', zones: ['Zone B', 'Zone C'], color: 'bg-yellow-100 dark:bg-yellow-900/20' },
                        { risk: 'High', zones: ['Zone A'], color: 'bg-red-100 dark:bg-red-900/20' }
                      ].map((level) => (
                        <div key={level.risk} className={cn("p-4 rounded-lg", level.color)}>
                          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                            {level.risk} Risk
                          </h4>
                          <div className="space-y-1">
                            {level.zones.map(zone => (
                              <p key={zone} className="text-sm text-gray-600 dark:text-gray-300">
                                {zone}
                              </p>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Predictive Timeline */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Risk Timeline
                    </h3>
                    <div className="space-y-3">
                      {riskPredictions.map((prediction, index) => (
                        <div key={index} className="flex items-center">
                          <div className="w-32 text-sm text-gray-600 dark:text-gray-400">
                            {prediction.timeframe}
                          </div>
                          <div className="flex-1 flex items-center">
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 relative">
                              <div 
                                className={cn(
                                  "h-4 rounded-full",
                                  prediction.probability > 70 ? 'bg-red-500' :
                                  prediction.probability > 40 ? 'bg-yellow-500' : 'bg-green-500'
                                )}
                                style={{ width: `${prediction.probability}%` }}
                              />
                              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-white font-medium">
                                {prediction.type.replace('_', ' ')}
                              </span>
                            </div>
                          </div>
                          <div className="w-16 text-right text-sm font-medium text-gray-900 dark:text-white">
                            {prediction.probability}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>

        {/* Side Panel */}
        <div className="space-y-6">
          {/* Training & Compliance */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Training & Compliance
            </h3>
            
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Overall Completion</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {mockMetrics.trainingCompletion}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-construction-blue h-2 rounded-full" style={{ width: `${mockMetrics.trainingCompletion}%` }} />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Fall Protection</span>
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Hazard Communication</span>
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Equipment Safety</span>
                  <Clock className="w-4 h-4 text-yellow-500" />
                </div>
              </div>

              <button className="w-full text-sm text-construction-blue hover:text-construction-blue/80 font-medium">
                View All Training →
              </button>
            </div>
          </motion.div>

          {/* AI Camera Status */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                AI Monitoring
              </h3>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2" />
                <span className="text-sm text-green-500">Active</span>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Active Cameras</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">24/24</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Coverage</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">98%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Detections Today</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">157</span>
              </div>
              
              <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">Recent Detections</div>
                <div className="space-y-2">
                  <div className="flex items-center text-xs">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2" />
                    <span className="text-gray-700 dark:text-gray-300">Missing hard hat - Zone A</span>
                  </div>
                  <div className="flex items-center text-xs">
                    <div className="w-2 h-2 bg-red-500 rounded-full mr-2" />
                    <span className="text-gray-700 dark:text-gray-300">Fall hazard - Zone C</span>
                  </div>
                  <div className="flex items-center text-xs">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    <span className="text-gray-700 dark:text-gray-300">Good housekeeping - Zone B</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Quick Actions
            </h3>
            <div className="space-y-2">
              <button className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <span className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                  <FileText className="w-4 h-4 mr-2" />
                  Generate Safety Report
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </button>
              <button className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <span className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                  <Users className="w-4 h-4 mr-2" />
                  Schedule Training
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </button>
              <button className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <span className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                  <Bell className="w-4 h-4 mr-2" />
                  Configure Alerts
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Safety Performance Dashboard */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Safety Performance Dashboard
          </h3>
          <div className="flex items-center space-x-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="day">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
            </select>
            <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
              <Download className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Performance metrics visualization */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-green-100 dark:bg-green-900/20 rounded-full mb-3">
              <span className="text-3xl font-bold text-green-600 dark:text-green-400">A+</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Safety Grade</p>
          </div>
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-blue-100 dark:bg-blue-900/20 rounded-full mb-3">
              <span className="text-3xl font-bold text-blue-600 dark:text-blue-400">342</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Days Without Incident</p>
          </div>
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-purple-100 dark:bg-purple-900/20 rounded-full mb-3">
              <span className="text-3xl font-bold text-purple-600 dark:text-purple-400">89%</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">AI Accuracy Rate</p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
