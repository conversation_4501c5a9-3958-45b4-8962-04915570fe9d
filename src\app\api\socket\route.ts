// Socket.io API route for Next.js App Router

import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  // Socket.io WebSocket connections are handled by the Next.js server
  // This endpoint provides information about the socket server status
  return NextResponse.json({ 
    message: 'Socket.io server is running. Connect via WebSocket to the main server URL.' 
  })
}

export async function POST(request: NextRequest) {
  return NextResponse.json({ 
    error: 'Socket.io connections should use WebSocket transport' 
  }, { status: 400 })
}