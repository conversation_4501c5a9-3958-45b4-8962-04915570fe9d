'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  Users,
  Building2,
  Calendar,
  Target,
  Activity,
  Download,
  Filter,
  Eye,
  Info,
  ChevronUp,
  ChevronDown,
  AlertTriangle,
  CheckCircle2,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Brain,
  PieChart,
  LineChart
} from 'lucide-react'
import { cn } from '@/lib/utils'
import Link from 'next/link'
import type { ProjectAnalytics, KPI, Prediction } from '@/types'

// Mock analytics data
const kpiData: KPI[] = [
  {
    name: 'Overall Progress',
    value: 67,
    target: 65,
    unit: '%',
    trend: 'up',
    status: 'on_track'
  },
  {
    name: 'Budget Utilization',
    value: 72,
    target: 75,
    unit: '%',
    trend: 'stable',
    status: 'on_track'
  },
  {
    name: 'Schedule Performance',
    value: 0.94,
    target: 1.0,
    unit: 'SPI',
    trend: 'down',
    status: 'at_risk'
  },
  {
    name: 'Safety Score',
    value: 94,
    target: 90,
    unit: 'pts',
    trend: 'up',
    status: 'on_track'
  },
  {
    name: 'Quality Index',
    value: 96,
    target: 95,
    unit: '%',
    trend: 'up',
    status: 'on_track'
  },
  {
    name: 'Productivity Rate',
    value: 112,
    target: 100,
    unit: '%',
    trend: 'up',
    status: 'on_track'
  }
]

const predictions: Prediction[] = [
  {
    type: 'completion_date',
    probability: 85,
    impact: '2 weeks early',
    recommendedAction: 'Maintain current pace, monitor Zone C progress closely'
  },
  {
    type: 'cost_overrun',
    probability: 22,
    impact: '$1.2M over budget',
    recommendedAction: 'Review material costs and negotiate bulk discounts'
  },
  {
    type: 'weather_delay',
    probability: 68,
    impact: '5-7 days',
    recommendedAction: 'Accelerate exterior work before rainy season'
  }
]

const projectComparison = [
  { project: 'Downtown Tower', progress: 67, budget: 78, safety: 94, quality: 96 },
  { project: 'Riverside Complex', progress: 43, budget: 41, safety: 88, quality: 92 },
  { project: 'Tech Campus', progress: 89, budget: 89, safety: 96, quality: 98 },
  { project: 'Medical Center', progress: 25, budget: 28, safety: 91, quality: 94 }
]

const costBreakdown = [
  { category: 'Labor', actual: 42000000, budget: 40000000, variance: 5 },
  { category: 'Materials', actual: 35000000, budget: 38000000, variance: -7.9 },
  { category: 'Equipment', actual: 18000000, budget: 17000000, variance: 5.9 },
  { category: 'Subcontractors', actual: 25000000, budget: 25000000, variance: 0 },
  { category: 'Overhead', actual: 8000000, budget: 10000000, variance: -20 }
]

const aiInsights = [
  {
    title: 'Resource Optimization',
    description: 'Reallocating 2 crane operators to Zone C could improve productivity by 15%',
    impact: '+$450K savings',
    confidence: 92,
    action: 'Review staffing plan'
  },
  {
    title: 'Schedule Compression',
    description: 'Parallel MEP installation in Zones A & B can save 8 days',
    impact: '-8 days duration',
    confidence: 88,
    action: 'Update schedule'
  },
  {
    title: 'Cost Reduction',
    description: 'Bulk material purchase for Q3 could reduce costs by 12%',
    impact: '-$1.8M cost',
    confidence: 95,
    action: 'Negotiate pricing'
  }
]

export default function AnalyticsPage() {
  const [selectedTimeRange, setSelectedTimeRange] = useState('month')
  const [selectedProject, setSelectedProject] = useState('all')
  const [showDetails, setShowDetails] = useState(false)
  const [selectedMetric, setSelectedMetric] = useState<'cost' | 'schedule' | 'quality' | 'safety'>('cost')

  const getKPIColor = (status: string) => {
    switch (status) {
      case 'on_track': return 'text-green-500'
      case 'at_risk': return 'text-yellow-500'
      case 'off_track': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />
      case 'down': return <TrendingDown className="w-4 h-4 text-red-500" />
      default: return <Activity className="w-4 h-4 text-gray-500" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Analytics Dashboard
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            Real-time insights and predictive analytics across all projects
          </p>
        </div>
        <div className="flex space-x-3">
          <select
            value={selectedProject}
            onChange={(e) => setSelectedProject(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All Projects</option>
            <option value="downtown">Downtown Tower</option>
            <option value="riverside">Riverside Complex</option>
            <option value="tech">Tech Campus</option>
            <option value="medical">Medical Center</option>
          </select>
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
          <button className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <Download className="w-5 h-5 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* KPI Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {kpiData.map((kpi, index) => (
          <motion.div
            key={kpi.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 hover:shadow-lg transition-all"
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs text-gray-600 dark:text-gray-400">
                {kpi.name}
              </span>
              {getTrendIcon(kpi.trend)}
            </div>
            <div className="flex items-baseline justify-between">
              <span className={cn("text-2xl font-bold", getKPIColor(kpi.status))}>
                {kpi.value}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {kpi.unit}
              </span>
            </div>
            <div className="mt-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500">Target: {kpi.target}</span>
                <span className={cn(
                  "font-medium",
                  kpi.value >= kpi.target ? "text-green-500" : "text-red-500"
                )}>
                  {kpi.value >= kpi.target ? '+' : ''}{((kpi.value - kpi.target) / kpi.target * 100).toFixed(1)}%
                </span>
              </div>
              <div className="mt-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                <div 
                  className={cn(
                    "h-1 rounded-full transition-all",
                    getKPIColor(kpi.status).replace('text-', 'bg-')
                  )}
                  style={{ width: `${Math.min((kpi.value / kpi.target) * 100, 100)}%` }}
                />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* AI Predictions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl p-6 text-white"
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Brain className="w-8 h-8 mr-3" />
            <h2 className="text-2xl font-bold">AI Predictions & Insights</h2>
          </div>
          <button className="text-white/80 hover:text-white">
            View All →
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {predictions.map((prediction, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white/10 backdrop-blur-sm rounded-lg p-4"
            >
              <div className="flex items-start justify-between mb-2">
                <span className="text-sm font-medium capitalize">
                  {prediction.type.replace('_', ' ')}
                </span>
                <span className={cn(
                  "text-xs font-semibold px-2 py-1 rounded",
                  prediction.probability > 70 ? 'bg-red-500/20' : 
                  prediction.probability > 40 ? 'bg-yellow-500/20' : 'bg-green-500/20'
                )}>
                  {prediction.probability}% probability
                </span>
              </div>
              <p className="text-lg font-semibold mb-1">{prediction.impact}</p>
              <p className="text-sm text-white/80 mb-3">{prediction.recommendedAction}</p>
              <button className="text-sm font-medium hover:underline">
                Take Action →
              </button>
            </motion.div>
          ))}
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Chart Area */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Performance Trends
              </h3>
              <div className="flex items-center space-x-2">
                {['cost', 'schedule', 'quality', 'safety'].map((metric) => (
                  <button
                    key={metric}
                    onClick={() => setSelectedMetric(metric as any)}
                    className={cn(
                      "px-3 py-1.5 text-sm rounded-lg transition-colors capitalize",
                      selectedMetric === metric
                        ? "bg-construction-blue text-white"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                    )}
                  >
                    {metric}
                  </button>
                ))}
              </div>
            </div>

            {/* Chart Placeholder */}
            <div className="h-80 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg flex items-center justify-center">
              <LineChart className="w-16 h-16 text-gray-400 dark:text-gray-500" />
            </div>

            {/* Chart Legend */}
            <div className="mt-4 flex items-center justify-center space-x-6 text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-construction-blue rounded-full mr-2" />
                <span className="text-gray-600 dark:text-gray-400">Actual</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-gray-400 rounded-full mr-2" />
                <span className="text-gray-600 dark:text-gray-400">Target</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-purple-500 rounded-full mr-2" />
                <span className="text-gray-600 dark:text-gray-400">AI Forecast</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* AI Insights Panel */}
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Zap className="w-5 h-5 mr-2 text-yellow-500" />
              AI Recommendations
            </h3>
            
            <div className="space-y-3">
              {aiInsights.map((insight, index) => (
                <div 
                  key={index}
                  className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors"
                >
                  <div className="flex items-start justify-between mb-1">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {insight.title}
                    </h4>
                    <span className="text-xs text-purple-600 dark:text-purple-400">
                      {insight.confidence}% conf
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">
                    {insight.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-green-600 dark:text-green-400">
                      {insight.impact}
                    </span>
                    <button className="text-xs text-construction-blue hover:text-construction-blue/80">
                      {insight.action} →
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Portfolio Summary
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Active Projects</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">12</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Total Value</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">$495M</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Avg Performance</span>
                <span className="text-sm font-medium text-green-600 dark:text-green-400">94.2%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">At Risk</span>
                <span className="text-sm font-medium text-yellow-600 dark:text-yellow-400">2 projects</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Project Comparison */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
      >
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Project Comparison
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Project
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Budget
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Safety
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Quality
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Overall
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {projectComparison.map((project) => {
                const overall = Math.round((project.progress + project.budget + project.safety + project.quality) / 4)
                return (
                  <tr key={project.project} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link
                        href={`/dashboard/projects/${project.project.toLowerCase().replace(' ', '-')}`}
                        className="text-sm font-medium text-gray-900 dark:text-white hover:text-construction-blue"
                      >
                        {project.project}
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ width: `${project.progress}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-900 dark:text-white">{project.progress}%</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${project.budget}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-900 dark:text-white">{project.budget}%</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={cn(
                        "text-sm font-medium",
                        project.safety >= 90 ? "text-green-600" : "text-yellow-600"
                      )}>
                        {project.safety}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={cn(
                        "text-sm font-medium",
                        project.quality >= 95 ? "text-green-600" : "text-yellow-600"
                      )}>
                        {project.quality}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className={cn(
                          "text-sm font-bold",
                          overall >= 80 ? "text-green-600" : 
                          overall >= 60 ? "text-yellow-600" : "text-red-600"
                        )}>
                          {overall}%
                        </span>
                        {overall >= 80 ? (
                          <CheckCircle2 className="w-4 h-4 text-green-500 ml-2" />
                        ) : (
                          <AlertTriangle className="w-4 h-4 text-yellow-500 ml-2" />
                        )}
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Cost Analysis */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Cost Breakdown Analysis
          </h3>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-sm text-construction-blue hover:text-construction-blue/80"
          >
            {showDetails ? 'Hide' : 'Show'} Details
          </button>
        </div>

        <div className="space-y-4">
          {costBreakdown.map((category) => (
            <div key={category.category}>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {category.category}
                </span>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    ${(category.actual / 1000000).toFixed(1)}M / ${(category.budget / 1000000).toFixed(1)}M
                  </span>
                  <span className={cn(
                    "text-sm font-medium flex items-center",
                    category.variance > 0 ? "text-red-600" : "text-green-600"
                  )}>
                    {category.variance > 0 ? (
                      <ArrowUpRight className="w-4 h-4 mr-1" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4 mr-1" />
                    )}
                    {Math.abs(category.variance)}%
                  </span>
                </div>
              </div>
              <div className="relative w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="absolute top-0 left-0 h-2 bg-gray-400 dark:bg-gray-500 rounded-full"
                  style={{ width: `${(category.budget / Math.max(category.actual, category.budget)) * 100}%` }}
                />
                <div 
                  className={cn(
                    "absolute top-0 left-0 h-2 rounded-full",
                    category.variance > 0 ? "bg-red-500" : "bg-green-500"
                  )}
                  style={{ width: `${(category.actual / Math.max(category.actual, category.budget)) * 100}%` }}
                />
              </div>
              {showDetails && (
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  Variance: ${Math.abs(category.actual - category.budget).toLocaleString()}
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Project Cost</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                $128M / $130M
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600 dark:text-gray-400">Overall Variance</p>
              <p className={cn(
                "text-2xl font-bold",
                "text-green-600"
              )}>
                -1.5%
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
