#!/usr/bin/env node

/**
 * Fallback to SQLite for development if PostgreSQL is not available
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Setting up SQLite fallback for development...\n');

// Update .env.local to use SQLite
const envPath = path.join(process.cwd(), '.env.local');
const prismaSchemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');

// Read current .env.local
let envContent = fs.readFileSync(envPath, 'utf8');

// Update DATABASE_URL to use SQLite
const sqliteUrl = 'file:./dev.db';
envContent = envContent.replace(
  /DATABASE_URL=.*/,
  `DATABASE_URL="${sqliteUrl}"`
);

// Write updated .env.local
fs.writeFileSync(envPath, envContent);
console.log('✅ Updated .env.local to use SQLite');

// Update Prisma schema to use SQLite provider
let schemaContent = fs.readFileSync(prismaSchemaPath, 'utf8');
schemaContent = schemaContent.replace(
  /provider\s*=\s*"postgresql"/,
  'provider = "sqlite"'
);

// Also update any PostgreSQL-specific field types
// For SQLite compatibility
schemaContent = schemaContent
  .replace(/@db\.Text/g, '')
  .replace(/Json\[\]/g, 'String')
  .replace(/Json/g, 'String')
  .replace(/String\[\]/g, 'String') // SQLite doesn't support arrays
  .replace(/enum\s+(\w+)\s*{[^}]+}/g, (match, enumName) => {
    // Convert enums to String type
    console.log(`  - Converting enum ${enumName} to String type`);
    return '';
  })
  .replace(/(\w+)\s+(\w+)\s+/g, (match, field, type) => {
    // Replace enum types with String
    if (type.match(/^(UserRole|CompanyType|ProjectStatus|TaskStatus|Priority|DocumentType|NotificationType)$/)) {
      return `${field} String `;
    }
    return match;
  });

// Create a backup of the original schema
fs.writeFileSync(prismaSchemaPath + '.postgresql.backup', fs.readFileSync(prismaSchemaPath));
fs.writeFileSync(prismaSchemaPath, schemaContent);

console.log('✅ Updated Prisma schema for SQLite (backup saved as schema.prisma.postgresql.backup)');
console.log('\n📝 Note: Some features may be limited with SQLite compared to PostgreSQL');
console.log('\n🚀 Next steps:');
console.log('1. Run: npm run db:push');
console.log('2. Run: npm run db:seed (optional)');
console.log('3. Run: npm run dev');
console.log('\n💡 To switch back to PostgreSQL later:');
console.log('1. Restore the backup: prisma/schema.prisma.postgresql.backup');
console.log('2. Update DATABASE_URL in .env.local');