'use client'

import { useState, useRef, ChangeEvent, useEffect } from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import {
  User,
  Bell,
  Shield,
  Key,
  Monitor,
  Globe,
  Mail,
  Smartphone,
  Building2,
  Database,
  Zap,
  Eye,
  EyeOff,
  Save,
  ChevronRight,
  ToggleLeft,
  Info,
  AlertCircle,
  CheckCircle2,
  Moon,
  Sun,
  Palette,
  Volume2,
  Copy,
  RefreshCw,
  Trash2,
  Lock,
  Plus,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useTheme } from 'next-themes'
import { toast } from '@/hooks/use-toast'
import { useCompany } from '@/contexts/CompanyContext'
import { CompanyType } from '@/lib/company-types'
import { UserRole, getRoleDisplayName, getAllRoles } from '@/lib/user-role-types'

// Settings sections
const settingsSections = [
  { id: 'profile', name: 'Profile', icon: User },
  { id: 'notifications', name: 'Notifications', icon: Bell },
  { id: 'security', name: 'Security', icon: Shield },
  { id: 'api', name: 'API Keys', icon: Key },
  { id: 'display', name: 'Display', icon: Monitor },
  { id: 'organization', name: 'Organization', icon: Building2 },
  { id: 'integrations', name: 'Integrations', icon: Database },
  { id: 'ai', name: 'AI Settings', icon: Zap }
]

// Modal component for various actions
function Modal({ isOpen, onClose, title, children }: { 
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode 
}) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75" onClick={onClose}></div>
        </div>
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h3>
              <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
                <X className="w-5 h-5" />
              </button>
            </div>
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState('profile')
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle')
  const [profileImage, setProfileImage] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const { companyType, setCompanyType } = useCompany()
  
  // Modal states
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [showApiKeyModal, setShowApiKeyModal] = useState(false)
  const [showInviteModal, setShowInviteModal] = useState(false)
  const [showApiKeys, setShowApiKeys] = useState<{ [key: string]: boolean }>({})
  
  // Form states
  const [profile, setProfile] = useState({
    name: 'Mike Best',
    email: '<EMAIL>',
    role: 'ADMIN' as UserRole,
    phone: '************',
    company: 'AceWatt'
  })

  const [notifications, setNotifications] = useState({
    emailAlerts: true,
    smsAlerts: false,
    pushNotifications: true,
    dailyDigest: true,
    weeklyReport: true,
    safetyAlerts: true,
    scheduleUpdates: true,
    budgetAlerts: true,
    aiInsights: true
  })

  const [security, setSecurity] = useState({
    twoFactor: true,
    sessionTimeout: '30',
    ipWhitelist: false,
    apiAccess: true
  })

  const [aiSettings, setAiSettings] = useState({
    autoSuggestions: true,
    predictiveAnalytics: true,
    realTimeMonitoring: true,
    voiceCommands: false,
    aiAssistant: true,
    dataSharing: false,
    modelAccuracy: 70
  })

  const [organization, setOrganization] = useState({
    name: 'AceWatt',
    industry: 'Commercial Construction',
    size: '201-500 employees',
    companyType: companyType || 'General Contractor' as CompanyType
  })

  const [integrations, setIntegrations] = useState({
    'Autodesk BIM 360': { connected: true, lastSync: '2 hours ago' },
    'Microsoft Project': { connected: true, lastSync: '1 day ago' },
    'Procore': { connected: false, lastSync: null },
    'PlanGrid': { connected: false, lastSync: null },
    'Slack': { connected: true, lastSync: 'Real-time' },
    'Google Drive': { connected: false, lastSync: null }
  })

  // Password form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  // API Key state
  const [apiKeys] = useState({
    production: 'sk_live_1234567890abcdef',
    development: 'sk_test_abcdef1234567890'
  })

  // Load saved settings from localStorage
  useEffect(() => {
    setMounted(true)
    const savedSettings = localStorage.getItem('aiConstructionSettings')
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings)
        if (settings.profile) setProfile(settings.profile)
        if (settings.notifications) setNotifications(settings.notifications)
        if (settings.security) setSecurity(settings.security)
        if (settings.aiSettings) setAiSettings(settings.aiSettings)
        if (settings.organization) setOrganization(settings.organization)
        if (settings.profileImage) setProfileImage(settings.profileImage)
        // Set company type if it exists in saved settings
        if (settings.organization?.companyType) {
          setCompanyType(settings.organization.companyType)
        }
      } catch (error) {
        console.error('Error loading settings:', error)
      }
    }
  }, [setCompanyType])

  const handleSave = () => {
    setSaveStatus('saving')
    
    // Save to localStorage
    const settings = {
      profile,
      notifications,
      security,
      aiSettings,
      organization,
      profileImage
    }
    
    localStorage.setItem('aiConstructionSettings', JSON.stringify(settings))
    
    // Simulate API call
    setTimeout(() => {
      setSaveStatus('saved')
      toast({
        title: "Settings saved",
        description: "Your preferences have been updated successfully.",
      })
      setTimeout(() => setSaveStatus('idle'), 2000)
    }, 1000)
  }

  const handleImageChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      if (!validTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: "Please select a valid image file (JPG, PNG, GIF, or WebP)",
          variant: "destructive"
        })
        return
      }

      // Validate file size (2MB limit)
      const maxSize = 2 * 1024 * 1024 // 2MB in bytes
      if (file.size > maxSize) {
        toast({
          title: "File too large",
          description: "File size must be less than 2MB",
          variant: "destructive"
        })
        return
      }

      // Create preview URL
      const reader = new FileReader()
      reader.onloadend = () => {
        setProfileImage(reader.result as string)
        toast({
          title: "Photo uploaded",
          description: "Your profile photo has been updated.",
        })
      }
      reader.onerror = () => {
        toast({
          title: "Upload failed",
          description: "Failed to upload photo. Please try again.",
          variant: "destructive"
        })
      }
      reader.readAsDataURL(file)
    }
  }

  const handleChangePhotoClick = () => {
    fileInputRef.current?.click()
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const copyToClipboard = (text: string, keyType: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: `${keyType} API key copied to clipboard.`,
    })
  }

  const regenerateApiKey = (keyType: string) => {
    toast({
      title: "API key regenerated",
      description: `Your ${keyType} API key has been regenerated.`,
    })
  }

  const revokeApiKey = (keyType: string) => {
    toast({
      title: "API key revoked",
      description: `Your ${keyType} API key has been revoked.`,
      variant: "destructive"
    })
  }

  const handlePasswordChange = () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "Please make sure your passwords match.",
        variant: "destructive"
      })
      return
    }
    
    if (passwordForm.newPassword.length < 8) {
      toast({
        title: "Password too short",
        description: "Password must be at least 8 characters long.",
        variant: "destructive"
      })
      return
    }
    
    // Simulate password change
    toast({
      title: "Password updated",
      description: "Your password has been changed successfully.",
    })
    setShowPasswordModal(false)
    setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' })
  }

  const handleIntegrationToggle = (integration: string) => {
    setIntegrations(prev => ({
      ...prev,
      [integration]: {
        ...prev[integration as keyof typeof prev],
        connected: !prev[integration as keyof typeof prev].connected,
        lastSync: !prev[integration as keyof typeof prev].connected ? 'Just now' : null
      }
    }))
    
    const action = integrations[integration as keyof typeof integrations].connected ? 'disconnected' : 'connected'
    toast({
      title: `${integration} ${action}`,
      description: `${integration} has been ${action} successfully.`,
    })
  }

  const handleInviteTeamMember = (email: string, role: string) => {
    toast({
      title: "Invitation sent",
      description: `Invitation sent to ${email} as ${role}.`,
    })
    setShowInviteModal(false)
  }

  const renderSection = () => {
    switch (activeSection) {
      case 'profile':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Profile Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    value={profile.name}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={profile.email}
                    onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Role
                  </label>
                  <select
                    value={profile.role}
                    onChange={(e) => setProfile({ ...profile, role: e.target.value as UserRole })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors"
                  >
                    {getAllRoles().map(role => (
                      <option key={role} value={role}>{getRoleDisplayName(role)}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={profile.phone}
                    onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Company
                  </label>
                  <input
                    type="text"
                    value={profile.company}
                    onChange={(e) => setProfile({ ...profile, company: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white transition-colors"
                  />
                </div>
              </div>
            </div>

            <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Profile Photo
              </h3>
              <div className="flex items-center space-x-6">
                <div className="relative group">
                  {profileImage ? (
                    <Image
                      src={profileImage}
                      alt="Profile"
                      width={96}
                      height={96}
                      className="rounded-full object-cover border-2 border-gray-200 dark:border-gray-700"
                    />
                  ) : (
                    <div className="w-24 h-24 rounded-full bg-gradient-to-r from-construction-blue to-construction-orange flex items-center justify-center text-white text-2xl font-bold">
                      {getInitials(profile.name)}
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer" onClick={handleChangePhotoClick}>
                    <span className="text-white text-sm">Change</span>
                  </div>
                </div>
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                  <button 
                    onClick={handleChangePhotoClick}
                    className="px-4 py-2 bg-construction-blue text-white rounded-lg hover:bg-construction-blue/90 transition-colors"
                  >
                    Change Photo
                  </button>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    JPG, PNG, GIF or WebP. Max size of 2MB.
                  </p>
                  {profileImage && (
                    <button
                      onClick={() => {
                        setProfileImage(null)
                        toast({
                          title: "Photo removed",
                          description: "Your profile photo has been removed.",
                        })
                      }}
                      className="mt-2 text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                    >
                      Remove Photo
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )

      case 'notifications':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Notification Preferences
              </h3>
              <div className="space-y-4">
                {Object.entries(notifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-3">
                      {key === 'emailAlerts' && <Mail className="w-5 h-5 text-gray-400" />}
                      {key === 'smsAlerts' && <Smartphone className="w-5 h-5 text-gray-400" />}
                      {key === 'pushNotifications' && <Bell className="w-5 h-5 text-gray-400" />}
                      {key === 'safetyAlerts' && <AlertCircle className="w-5 h-5 text-gray-400" />}
                      {key === 'aiInsights' && <Zap className="w-5 h-5 text-gray-400" />}
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {getNotificationDescription(key)}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => setNotifications({ ...notifications, [key]: !value })}
                      className={cn(
                        "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                        value ? "bg-construction-blue" : "bg-gray-200 dark:bg-gray-700"
                      )}
                    >
                      <span
                        className={cn(
                          "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                          value ? "translate-x-6" : "translate-x-1"
                        )}
                      />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 'security':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Security Settings
              </h3>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      Two-Factor Authentication
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Add an extra layer of security to your account
                    </p>
                  </div>
                  <button
                    onClick={() => setSecurity({ ...security, twoFactor: !security.twoFactor })}
                    className={cn(
                      "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                      security.twoFactor ? "bg-construction-blue" : "bg-gray-200 dark:bg-gray-700"
                    )}
                  >
                    <span
                      className={cn(
                        "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                        security.twoFactor ? "translate-x-6" : "translate-x-1"
                      )}
                    />
                  </button>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Session Timeout (minutes)
                  </label>
                  <select
                    value={security.sessionTimeout}
                    onChange={(e) => setSecurity({ ...security, sessionTimeout: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="15">15 minutes</option>
                    <option value="30">30 minutes</option>
                    <option value="60">1 hour</option>
                    <option value="120">2 hours</option>
                  </select>
                </div>

                <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-4">
                    Password
                  </h4>
                  <button 
                    onClick={() => setShowPasswordModal(true)}
                    className="px-4 py-2 bg-gray-900 dark:bg-gray-700 text-white rounded-lg hover:bg-gray-800 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2"
                  >
                    <Lock className="w-4 h-4" />
                    <span>Change Password</span>
                  </button>
                </div>

                <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-4">
                    Active Sessions
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Monitor className="w-5 h-5 text-gray-400" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            Windows PC - Chrome
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Current session • Burbank, CA
                          </p>
                        </div>
                      </div>
                      <span className="text-xs text-green-600 dark:text-green-400">Active</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Smartphone className="w-5 h-5 text-gray-400" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            iPhone - Safari
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Last active 2 hours ago • Los Angeles, CA
                          </p>
                        </div>
                      </div>
                      <button className="text-xs text-red-600 hover:text-red-700">End Session</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      case 'api':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                API Keys
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Connect other tools and services to your AI Construction Management account.
              </p>
              
              {/* What are API Keys section */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <Info className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                      What are API Keys?
                    </p>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                      API keys are like special passwords that let other software talk to your AI Construction account. 
                      Think of them as digital keys that unlock your data for approved apps.
                    </p>
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-blue-800 dark:text-blue-200">Common uses:</p>
                      <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                        <li>• Connect to Excel or Google Sheets for automatic reports</li>
                        <li>• Sync data with your accounting software</li>
                        <li>• Build custom mobile apps for field workers</li>
                        <li>• Create automated safety alerts</li>
                        <li>• Export data to PowerBI or Tableau dashboards</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                      Keep Your Keys Safe
                    </p>
                    <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                      <li>• Treat API keys like passwords - don&apos;t share them</li>
                      <li>• Only give keys to trusted developers or services</li>
                      <li>• If you think someone has your key, regenerate it immediately</li>
                      <li>• Each key can be turned on/off without affecting others</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        Live Key
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                        For your real, everyday work
                      </p>
                    </div>
                    <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded flex items-center space-x-1">
                      <span className="w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full"></span>
                      <span>Active</span>
                    </span>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-3 text-xs space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Created:</span>
                      <span className="text-gray-900 dark:text-gray-200 font-medium">June 15, 2025</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Last activity:</span>
                      <span className="text-gray-900 dark:text-gray-200 font-medium">2 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Usage today:</span>
                      <span className="text-gray-900 dark:text-gray-200 font-medium">1,247 actions</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Permissions:</span>
                      <span className="text-gray-900 dark:text-gray-200 font-medium">Everything</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 mb-3">
                    <code className="flex-1 p-2 bg-gray-100 dark:bg-gray-700 rounded text-sm font-mono text-gray-800 dark:text-gray-200">
                      {showApiKeys.production ? apiKeys.production : 'sk_live_••••••••••••••••'}
                    </code>
                    <button 
                      onClick={() => setShowApiKeys({ ...showApiKeys, production: !showApiKeys.production })}
                      className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 tooltip"
                      title={showApiKeys.production ? 'Hide key' : 'Show key'}
                    >
                      {showApiKeys.production ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                    <button 
                      onClick={() => copyToClipboard(apiKeys.production, 'Production')}
                      className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 tooltip"
                      title="Copy to clipboard"
                    >
                      <Copy className="w-5 h-5" />
                    </button>
                  </div>
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => regenerateApiKey('production')}
                      className="text-sm text-construction-blue hover:underline flex items-center space-x-1"
                    >
                      <RefreshCw className="w-3 h-3" />
                      <span>Regenerate</span>
                    </button>
                    <span className="text-gray-300 dark:text-gray-600">•</span>
                    <button 
                      onClick={() => revokeApiKey('production')}
                      className="text-sm text-red-600 hover:underline flex items-center space-x-1"
                    >
                      <Trash2 className="w-3 h-3" />
                      <span>Revoke</span>
                    </button>
                  </div>
                </div>

                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        Test Key
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                        For trying things out safely
                      </p>
                    </div>
                    <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-2 py-1 rounded flex items-center space-x-1">
                      <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                      <span>Off</span>
                    </span>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-3 text-xs space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Created:</span>
                      <span className="text-gray-900 dark:text-gray-200 font-medium">June 10, 2025</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Last activity:</span>
                      <span className="text-gray-900 dark:text-gray-200 font-medium">Never used</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Usage today:</span>
                      <span className="text-gray-900 dark:text-gray-200 font-medium">0 actions</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Permissions:</span>
                      <span className="text-gray-900 dark:text-gray-200 font-medium">Limited</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 mb-3">
                    <code className="flex-1 p-2 bg-gray-100 dark:bg-gray-700 rounded text-sm font-mono text-gray-800 dark:text-gray-200">
                      {showApiKeys.development ? apiKeys.development : 'sk_test_••••••••••••••••'}
                    </code>
                    <button 
                      onClick={() => setShowApiKeys({ ...showApiKeys, development: !showApiKeys.development })}
                      className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 tooltip"
                      title={showApiKeys.development ? 'Hide key' : 'Show key'}
                    >
                      {showApiKeys.development ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                    <button 
                      onClick={() => copyToClipboard(apiKeys.development, 'Development')}
                      className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 tooltip"
                      title="Copy to clipboard"
                    >
                      <Copy className="w-5 h-5" />
                    </button>
                  </div>
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => regenerateApiKey('development')}
                      className="text-sm text-construction-blue hover:underline flex items-center space-x-1"
                    >
                      <RefreshCw className="w-3 h-3" />
                      <span>Regenerate</span>
                    </button>
                    <span className="text-gray-300 dark:text-gray-600">•</span>
                    <button 
                      onClick={() => revokeApiKey('development')}
                      className="text-sm text-red-600 hover:underline flex items-center space-x-1"
                    >
                      <Trash2 className="w-3 h-3" />
                      <span>Revoke</span>
                    </button>
                  </div>
                </div>
              </div>

              <button 
                onClick={() => setShowApiKeyModal(true)}
                className="mt-4 px-4 py-2 bg-construction-blue text-white rounded-lg hover:bg-construction-blue/90 transition-colors flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Add New Connection</span>
              </button>
              
              {/* Help Section for Developers */}
              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <details className="group">
                  <summary className="cursor-pointer flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Globe className="w-5 h-5 text-gray-400" />
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        Need help connecting?
                      </h4>
                    </div>
                    <ChevronRight className="w-4 h-4 text-gray-400 group-open:rotate-90 transition-transform" />
                  </summary>
                  
                  <div className="mt-4 space-y-4 pl-7">
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Share this with your IT team:
                      </h5>
                      <div className="space-y-3 text-sm">
                        <div>
                          <p className="text-gray-600 dark:text-gray-400 mb-1">Connection address:</p>
                          <code className="block p-2 bg-white dark:bg-gray-800 rounded text-xs">
                            https://api.aiconstructionmgmt.com/v1
                          </code>
                        </div>
                        <div>
                          <p className="text-gray-600 dark:text-gray-400 mb-1">How to connect:</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Use the API key above in your application&apos;s settings or configuration file
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        What can connected apps do?
                      </p>
                      <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">✓</span>
                          <span>View and update project information</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">✓</span>
                          <span>Generate safety and progress reports</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">✓</span>
                          <span>Access AI insights and predictions</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">✓</span>
                          <span>Export data to other systems</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div className="flex items-center space-x-3 text-sm">
                      <a 
                        href="#" 
                        className="text-construction-blue hover:underline flex items-center space-x-1"
                      >
                        <span>Download setup guide</span>
                      </a>
                      <span className="text-gray-300 dark:text-gray-600">•</span>
                      <a 
                        href="#" 
                        className="text-construction-blue hover:underline flex items-center space-x-1"
                      >
                        <span>Contact support</span>
                      </a>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        )

      case 'display':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Display Settings
              </h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Theme
                  </label>
                  <div className="grid grid-cols-3 gap-4">
                    <button
                      onClick={() => setTheme('light')}
                      className={cn(
                        "p-4 rounded-lg border-2 transition-all",
                        theme === 'light'
                          ? "border-construction-blue bg-construction-blue/10"
                          : "border-gray-300 dark:border-gray-600"
                      )}
                    >
                      <Sun className="w-6 h-6 mx-auto mb-2 text-yellow-500" />
                      <p className="text-sm font-medium text-gray-900 dark:text-white">Light</p>
                    </button>
                    <button
                      onClick={() => setTheme('dark')}
                      className={cn(
                        "p-4 rounded-lg border-2 transition-all",
                        theme === 'dark'
                          ? "border-construction-blue bg-construction-blue/10"
                          : "border-gray-300 dark:border-gray-600"
                      )}
                    >
                      <Moon className="w-6 h-6 mx-auto mb-2 text-indigo-500" />
                      <p className="text-sm font-medium text-gray-900 dark:text-white">Dark</p>
                    </button>
                    <button 
                      onClick={() => setTheme('system')}
                      className={cn(
                        "p-4 rounded-lg border-2 transition-all",
                        theme === 'system'
                          ? "border-construction-blue bg-construction-blue/10"
                          : "border-gray-300 dark:border-gray-600"
                      )}
                    >
                      <Monitor className="w-6 h-6 mx-auto mb-2 text-gray-500" />
                      <p className="text-sm font-medium text-gray-900 dark:text-white">System</p>
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Language
                  </label>
                  <select className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option>English (US)</option>
                    <option>Spanish</option>
                    <option>French</option>
                    <option>German</option>
                    <option>Chinese</option>
                    <option>Japanese</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Time Zone
                  </label>
                  <select className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option>Pacific Time (US & Canada)</option>
                    <option>Mountain Time (US & Canada)</option>
                    <option>Central Time (US & Canada)</option>
                    <option>Eastern Time (US & Canada)</option>
                    <option>UTC</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Date Format
                  </label>
                  <select className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option>MM/DD/YYYY</option>
                    <option>DD/MM/YYYY</option>
                    <option>YYYY-MM-DD</option>
                    <option>DD.MM.YYYY</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )

      case 'organization':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Organization Settings
              </h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Organization Name
                  </label>
                  <input
                    type="text"
                    value={organization.name}
                    onChange={(e) => setOrganization({ ...organization, name: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Company Type
                  </label>
                  <select 
                    value={organization.companyType}
                    onChange={(e) => {
                      const newType = e.target.value as CompanyType
                      setOrganization({ ...organization, companyType: newType })
                      setCompanyType(newType)
                      toast({
                        title: "Company type updated",
                        description: `AI responses will now be tailored for ${newType} operations.`,
                      })
                    }}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="General Contractor">General Contractor</option>
                    <option value="Electrical Contractor">Electrical Contractor</option>
                    <option value="Plumbing Contractor">Plumbing Contractor</option>
                    <option value="HVAC Contractor">HVAC Contractor</option>
                    <option value="Roofing Contractor">Roofing Contractor</option>
                    <option value="Concrete Contractor">Concrete Contractor</option>
                    <option value="Steel/Metal Contractor">Steel/Metal Contractor</option>
                    <option value="Masonry Contractor">Masonry Contractor</option>
                    <option value="Painting Contractor">Painting Contractor</option>
                    <option value="Flooring Contractor">Flooring Contractor</option>
                    <option value="Landscaping Contractor">Landscaping Contractor</option>
                    <option value="Demolition Contractor">Demolition Contractor</option>
                    <option value="Excavation/Earthwork Contractor">Excavation/Earthwork Contractor</option>
                    <option value="Glass & Glazing Contractor">Glass & Glazing Contractor</option>
                    <option value="Insulation Contractor">Insulation Contractor</option>
                    <option value="Drywall Contractor">Drywall Contractor</option>
                    <option value="Fire Protection Contractor">Fire Protection Contractor</option>
                    <option value="Elevator Contractor">Elevator Contractor</option>
                    <option value="Solar/Renewable Energy Contractor">Solar/Renewable Energy Contractor</option>
                    <option value="Marine/Underwater Contractor">Marine/Underwater Contractor</option>
                  </select>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    This setting customizes AI responses and features specifically for your trade
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Industry
                  </label>
                  <select 
                    value={organization.industry}
                    onChange={(e) => setOrganization({ ...organization, industry: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option>Commercial Construction</option>
                    <option>Residential Construction</option>
                    <option>Infrastructure</option>
                    <option>Industrial Construction</option>
                    <option>Mixed Use Development</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Company Size
                  </label>
                  <select 
                    value={organization.size}
                    onChange={(e) => setOrganization({ ...organization, size: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option>1-50 employees</option>
                    <option>51-200 employees</option>
                    <option>201-500 employees</option>
                    <option>501-1000 employees</option>
                    <option>1000+ employees</option>
                  </select>
                </div>

                <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-4">
                    Team Members
                  </h4>
                  <div className="space-y-3">
                    {[
                      { name: 'Sarah Johnson', role: 'ADMIN' as UserRole, email: '<EMAIL>' },
                      { name: 'Mike Chen', role: 'PROJECT_MANAGER' as UserRole, email: '<EMAIL>' },
                      { name: 'Emily Rodriguez', role: 'PROJECT_MANAGER' as UserRole, email: '<EMAIL>' }
                    ].map((member) => (
                      <div key={member.email} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-construction-blue to-construction-orange flex items-center justify-center text-white font-bold">
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{member.name}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {getRoleDisplayName(member.role)} • {member.email}
                            </p>
                          </div>
                        </div>
                        <button className="text-sm text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                          Manage
                        </button>
                      </div>
                    ))}
                  </div>
                  <button 
                    onClick={() => setShowInviteModal(true)}
                    className="mt-4 px-4 py-2 bg-construction-blue text-white rounded-lg hover:bg-construction-blue/90 transition-colors flex items-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Invite Team Member</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )

      case 'integrations':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Integrations
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(integrations).map(([name, details]) => (
                  <div key={name} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">
                          {name === 'Autodesk BIM 360' && '🏗️'}
                          {name === 'Microsoft Project' && '📊'}
                          {name === 'Procore' && '🔧'}
                          {name === 'PlanGrid' && '📐'}
                          {name === 'Slack' && '💬'}
                          {name === 'Google Drive' && '☁️'}
                        </span>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {name}
                        </h4>
                      </div>
                      {details.connected ? (
                        <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded">
                          Connected
                        </span>
                      ) : (
                        <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-2 py-1 rounded">
                          Available
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                      {details.connected
                        ? `Integration is active • Last sync: ${details.lastSync}`
                        : 'Connect to sync project data'}
                    </p>
                    <button 
                      onClick={() => handleIntegrationToggle(name)}
                      className={cn(
                        "w-full py-2 rounded-lg transition-colors text-sm font-medium",
                        details.connected
                          ? "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                          : "bg-construction-blue text-white hover:bg-construction-blue/90"
                      )}
                    >
                      {details.connected ? 'Disconnect' : 'Connect'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 'ai':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                AI Settings
              </h3>
              <div className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-3">
                  <Zap className="w-6 h-6" />
                  <div>
                    <p className="font-medium">AI-Powered Features</p>
                    <p className="text-sm text-white/80">
                      Configure how AI assists you in managing construction projects
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                {Object.entries(aiSettings).filter(([key]) => key !== 'modelAccuracy').map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700">
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getAISettingDescription(key)}
                      </p>
                    </div>
                    <button
                      onClick={() => setAiSettings({ ...aiSettings, [key]: !value })}
                      className={cn(
                        "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                        value ? "bg-construction-blue" : "bg-gray-200 dark:bg-gray-700"
                      )}
                    >
                      <span
                        className={cn(
                          "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                          value ? "translate-x-6" : "translate-x-1"
                        )}
                      />
                    </button>
                  </div>
                ))}
              </div>

              <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 className="font-medium text-gray-900 dark:text-white mb-4">
                  AI Model Preferences
                </h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Response Speed vs Accuracy ({aiSettings.modelAccuracy}%)
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={aiSettings.modelAccuracy}
                      onChange={(e) => setAiSettings({ ...aiSettings, modelAccuracy: parseInt(e.target.value) })}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <span>Faster</span>
                      <span>More Accurate</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  if (!mounted) {
    return null
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Settings</h1>
        <p className="mt-2 text-gray-600 dark:text-gray-300">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Sidebar */}
        <div className="lg:w-64">
          <nav className="space-y-1">
            {settingsSections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={cn(
                  "w-full flex items-center justify-between px-4 py-3 rounded-lg transition-all",
                  activeSection === section.id
                    ? "bg-construction-blue text-white"
                    : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                )}
              >
                <div className="flex items-center space-x-3">
                  <section.icon className="w-5 h-5" />
                  <span className="font-medium">{section.name}</span>
                </div>
                <ChevronRight className="w-4 h-4" />
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1">
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            {renderSection()}

            {/* Save Button */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Changes are saved automatically
              </p>
              <button
                onClick={handleSave}
                className={cn(
                  "px-6 py-2 rounded-lg font-medium transition-all flex items-center space-x-2",
                  saveStatus === 'idle' && "bg-construction-blue text-white hover:bg-construction-blue/90",
                  saveStatus === 'saving' && "bg-gray-400 text-white cursor-not-allowed",
                  saveStatus === 'saved' && "bg-green-500 text-white"
                )}
                disabled={saveStatus !== 'idle'}
              >
                {saveStatus === 'idle' && (
                  <>
                    <Save className="w-4 h-4" />
                    <span>Save Changes</span>
                  </>
                )}
                {saveStatus === 'saving' && (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Saving...</span>
                  </>
                )}
                {saveStatus === 'saved' && (
                  <>
                    <CheckCircle2 className="w-4 h-4" />
                    <span>Saved</span>
                  </>
                )}
              </button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Password Change Modal */}
      <Modal
        isOpen={showPasswordModal}
        onClose={() => {
          setShowPasswordModal(false)
          setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' })
        }}
        title="Change Password"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Current Password
            </label>
            <input
              type="password"
              value={passwordForm.currentPassword}
              onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              New Password
            </label>
            <input
              type="password"
              value={passwordForm.newPassword}
              onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Confirm New Password
            </label>
            <input
              type="password"
              value={passwordForm.confirmPassword}
              onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => {
                setShowPasswordModal(false)
                setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' })
              }}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handlePasswordChange}
              className="px-4 py-2 bg-construction-blue text-white rounded-lg hover:bg-construction-blue/90 transition-colors"
            >
              Change Password
            </button>
          </div>
        </div>
      </Modal>

      {/* API Key Generation Modal */}
      <Modal
        isOpen={showApiKeyModal}
        onClose={() => setShowApiKeyModal(false)}
        title="Create New Connection Key"
      >
        <div className="space-y-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              This will create a new key that lets other software connect to your account.
            </p>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              What&apos;s this key for?
            </label>
            <input
              type="text"
              placeholder="e.g., Excel Reports, Mobile App, Dashboard"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Give it a name so you remember what it&apos;s used for
            </p>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              How will you use it?
            </label>
            <div className="space-y-3">
              <label className="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50">
                <input type="radio" name="keyType" className="mt-0.5 mr-3" defaultChecked />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">For real work</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Use this for your everyday tools and integrations</p>
                </div>
              </label>
              <label className="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50">
                <input type="radio" name="keyType" className="mt-0.5 mr-3" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">For testing</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Safer option while you&apos;re setting things up</p>
                </div>
              </label>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              What can this key do?
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" defaultChecked />
                <span className="text-sm text-gray-700 dark:text-gray-300">View your data</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" defaultChecked />
                <span className="text-sm text-gray-700 dark:text-gray-300">Update information</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-gray-700 dark:text-gray-300">Delete things (be careful!)</span>
              </label>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => setShowApiKeyModal(false)}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                toast({
                  title: "Connection key created!",
                  description: "Your new key is ready to use. Remember to keep it safe!",
                })
                setShowApiKeyModal(false)
              }}
              className="px-4 py-2 bg-construction-blue text-white rounded-lg hover:bg-construction-blue/90 transition-colors"
            >
              Create Key
            </button>
          </div>
        </div>
      </Modal>

      {/* Invite Team Member Modal */}
      <Modal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        title="Invite Team Member"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email Address
            </label>
            <input
              type="email"
              placeholder="<EMAIL>"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Role
            </label>
            <select className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-construction-blue focus:border-transparent dark:bg-gray-700 dark:text-white">
              {getAllRoles().map(role => (
                <option key={role} value={role}>{getRoleDisplayName(role)}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Projects Access
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" defaultChecked />
                <span className="text-sm text-gray-700 dark:text-gray-300">All projects</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-gray-700 dark:text-gray-300">Downtown Tower</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-gray-700 dark:text-gray-300">Tech Campus</span>
              </label>
            </div>
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => setShowInviteModal(false)}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => handleInviteTeamMember('<EMAIL>', 'Project Manager')}
              className="px-4 py-2 bg-construction-blue text-white rounded-lg hover:bg-construction-blue/90 transition-colors"
            >
              Send Invitation
            </button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

// Helper functions
function getNotificationDescription(key: string): string {
  const descriptions: Record<string, string> = {
    emailAlerts: 'Receive important updates via email',
    smsAlerts: 'Get text messages for urgent notifications',
    pushNotifications: 'Browser notifications for real-time updates',
    dailyDigest: 'Summary of daily activities and progress',
    weeklyReport: 'Weekly project performance report',
    safetyAlerts: 'Immediate notifications for safety incidents',
    scheduleUpdates: 'Changes to project schedules and milestones',
    budgetAlerts: 'Notifications for budget variances',
    aiInsights: 'AI-generated recommendations and insights'
  }
  return descriptions[key] || ''
}

function getAISettingDescription(key: string): string {
  const descriptions: Record<string, string> = {
    autoSuggestions: 'Get AI-powered suggestions while working',
    predictiveAnalytics: 'Enable predictive analysis for project outcomes',
    realTimeMonitoring: 'AI monitors projects 24/7 for anomalies',
    voiceCommands: 'Control the platform using voice commands',
    aiAssistant: 'Access to AI assistant for questions and help',
    dataSharing: 'Share anonymized data to improve AI models'
  }
  return descriptions[key] || ''
}
