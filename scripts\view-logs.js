#!/usr/bin/env node

/**
 * Simple log viewer for development
 * Usage: node scripts/view-logs.js [service] [level]
 */

const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

const LOG_DIR = path.join(__dirname, '..', 'logs');
const [, , service = 'all', level = 'all'] = process.argv;

console.log(chalk.bold.cyan('📋 AI Construction Management - Log Viewer'));
console.log(chalk.gray('─'.repeat(60)));
console.log(chalk.yellow(`Service: ${service}, Level: ${level}`));
console.log(chalk.gray('─'.repeat(60)));
console.log('');

// Get log files
function getLogFiles(service) {
  const files = [];
  
  if (service === 'all') {
    const services = ['vision', 'takeoff', 'gemini', 'api', 'general'];
    services.forEach(svc => {
      const svcDir = path.join(LOG_DIR, svc);
      if (fs.existsSync(svcDir)) {
        const svcFiles = fs.readdirSync(svcDir)
          .filter(f => f.includes('combined') && f.endsWith('.log'))
          .map(f => path.join(svcDir, f));
        files.push(...svcFiles);
      }
    });
  } else {
    const svcDir = path.join(LOG_DIR, service);
    if (fs.existsSync(svcDir)) {
      const svcFiles = fs.readdirSync(svcDir)
        .filter(f => f.includes('combined') && f.endsWith('.log'))
        .map(f => path.join(svcDir, f));
      files.push(...svcFiles);
    }
  }
  
  return files;
}

// Parse and display logs
function displayLogs(files) {
  const allLogs = [];
  
  files.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf-8');
      const lines = content.split('\n').filter(line => line.trim());
      
      lines.forEach(line => {
        try {
          const entry = JSON.parse(line);
          
          // Filter by level
          if (level !== 'all' && entry.level !== level) return;
          
          allLogs.push(entry);
        } catch (e) {
          // Skip malformed lines
        }
      });
    } catch (e) {
      console.error(chalk.red(`Error reading ${file}:`, e.message));
    }
  });
  
  // Sort by timestamp
  allLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  
  // Display logs
  allLogs.slice(0, 100).forEach(entry => {
    const time = new Date(entry.timestamp).toLocaleTimeString();
    const levelColor = {
      error: chalk.red,
      warn: chalk.yellow,
      info: chalk.green,
      debug: chalk.magenta,
      http: chalk.cyan
    }[entry.level] || chalk.white;
    
    console.log(
      chalk.gray(time),
      levelColor(entry.level.toUpperCase().padEnd(5)),
      chalk.cyan(`[${entry.service}]`.padEnd(12)),
      entry.message
    );
    
    // Show relevant metadata
    const { timestamp, level: _, service: __, message: ___, stack, ...meta } = entry;
    if (Object.keys(meta).length > 0) {
      console.log(chalk.gray('  └─'), chalk.gray(JSON.stringify(meta)));
    }
  });
  
  console.log('');
  console.log(chalk.gray('─'.repeat(60)));
  console.log(chalk.green(`✅ Displayed ${Math.min(100, allLogs.length)} of ${allLogs.length} log entries`));
}

// Watch for changes
function watchLogs() {
  console.log(chalk.yellow('\n👀 Watching for new logs... (Ctrl+C to exit)\n'));
  
  const files = getLogFiles(service);
  const watchers = [];
  
  files.forEach(file => {
    const watcher = fs.watch(file, (eventType) => {
      if (eventType === 'change') {
        // Clear console and redisplay
        console.clear();
        console.log(chalk.bold.cyan('📋 AI Construction Management - Log Viewer (Live)'));
        console.log(chalk.gray('─'.repeat(60)));
        displayLogs(files);
      }
    });
    watchers.push(watcher);
  });
  
  // Handle exit
  process.on('SIGINT', () => {
    watchers.forEach(w => w.close());
    console.log(chalk.yellow('\n\n👋 Log viewer stopped'));
    process.exit(0);
  });
}

// Main
const files = getLogFiles(service);
if (files.length === 0) {
  console.log(chalk.red('❌ No log files found'));
  console.log(chalk.yellow('\nMake sure the application has been run at least once.'));
  process.exit(1);
}

displayLogs(files);

// Ask if user wants to watch
if (process.stdout.isTTY) {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('\nWatch for new logs? (y/n) ', (answer) => {
    if (answer.toLowerCase() === 'y') {
      watchLogs();
    } else {
      rl.close();
    }
  });
}