# 🚀 Quick Start Guide

## Prerequisites
- Node.js 18+ installed
- npm or yarn package manager

## Installation & Running

### Option 1: Using the start script (Recommended)
```bash
node start.js
```
This will automatically install dependencies and start the development server.

### Option 2: Manual installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

## Access the Application
Once started, open your browser and navigate to:
- 🌐 **Main App**: http://localhost:3000
- 📊 **Dashboard**: http://localhost:3000/dashboard

## Default Credentials
Since there's no authentication implemented yet, you can access all pages directly.

## Available Pages

### Landing Page
- **URL**: `/`
- **Description**: Marketing landing page with feature showcase

### Dashboard Pages
- **Dashboard Home**: `/dashboard` - Overview with metrics and AI insights
- **Projects**: `/dashboard/projects` - Project management with grid/list views
- **Scheduling**: `/dashboard/scheduling` - AI-powered schedule optimization
- **Progress Tracking**: `/dashboard/progress` - 360° reality capture viewer
- **Safety Monitoring**: `/dashboard/safety` - Predictive safety analytics
- **Estimating**: `/dashboard/estimating` - AI takeoff and cost estimation
- **Field Operations**: `/dashboard/field` - Robotic control and work packages
- **Analytics**: `/dashboard/analytics` - KPIs and performance metrics
- **AI Assistant**: `/dashboard/ai-assistant` - Natural language chat interface

## Troubleshooting

### Port Already in Use
If you see an error about port 3000 being in use:
```bash
# Kill the process using port 3000
npx kill-port 3000

# Or use a different port
PORT=3001 npm run dev
```

### Module Not Found Errors
If you encounter module not found errors:
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

### TypeScript Errors
Run type checking separately:
```bash
npm run type-check
```

### Environment Variables
The app will work without environment variables, but for full functionality:
1. Copy `.env.example` to `.env.local`
2. Fill in the required API keys

⚠️ **IMPORTANT**: `.env.local` contains sensitive API keys - NEVER commit it to git!

## Debug Mode
To check the project setup:
```bash
node debug.js
```

## Features Working Out of the Box
✅ All UI components and pages  
✅ Responsive design with dark mode  
✅ Animations with Framer Motion  
✅ Mock data for all features  
✅ Type-safe development with TypeScript  

## Features Requiring Backend
❌ User authentication  
❌ Real data persistence  
❌ File uploads  
❌ AI/ML integration  
❌ Real-time updates  

## Development Tips
- Use `npm run lint` to check for code issues
- Use `npm run type-check` for TypeScript validation
- The app uses mock data - all changes are temporary
- Dark mode can be toggled using system preferences

## Support
If you encounter any issues:
1. Check the console for error messages
2. Run `node debug.js` to verify setup
3. Ensure all dependencies are installed
4. Check that you're using Node.js 18+

Happy coding! 🏗️
