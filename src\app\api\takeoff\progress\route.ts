import { NextRequest, NextResponse } from 'next/server'

// In-memory store for progress updates (in production, use Redis or similar)
const progressStore = new Map<string, {
  step: string
  progress: number
  timestamp: number
  data?: any
}>()

// Server-Sent Events for real-time progress updates
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const sessionId = searchParams.get('sessionId')
  
  if (!sessionId) {
    return NextResponse.json({ error: 'Session ID required' }, { status: 400 })
  }

  // Create a readable stream for SSE
  const encoder = new TextEncoder()
  
  const stream = new ReadableStream({
    start(controller) {
      // Send initial connection message
      const data = `data: ${JSON.stringify({ 
        type: 'connected', 
        sessionId,
        timestamp: Date.now() 
      })}\n\n`
      controller.enqueue(encoder.encode(data))
      
      // Set up interval to check for progress updates
      const interval = setInterval(() => {
        const progress = progressStore.get(sessionId)
        if (progress) {
          const data = `data: ${JSON.stringify({
            type: 'progress',
            ...progress
          })}\n\n`
          controller.enqueue(encoder.encode(data))
          
          // Clean up completed sessions
          if (progress.progress >= 100) {
            progressStore.delete(sessionId)
            clearInterval(interval)
            controller.close()
          }
        }
      }, 500) // Check every 500ms
      
      // Clean up on client disconnect
      request.signal.addEventListener('abort', () => {
        clearInterval(interval)
        progressStore.delete(sessionId)
        controller.close()
      })
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  })
}

// Update progress for a session
export async function POST(request: NextRequest) {
  try {
    const { sessionId, step, progress, data } = await request.json()
    
    if (!sessionId || typeof progress !== 'number') {
      return NextResponse.json({ error: 'Invalid request data' }, { status: 400 })
    }
    
    // Store progress update
    progressStore.set(sessionId, {
      step: step || 'Processing',
      progress: Math.min(100, Math.max(0, progress)),
      timestamp: Date.now(),
      data
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update progress' }, { status: 500 })
  }
}

// Get current progress for a session
export async function PUT(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const sessionId = searchParams.get('sessionId')
  
  if (!sessionId) {
    return NextResponse.json({ error: 'Session ID required' }, { status: 400 })
  }
  
  const progress = progressStore.get(sessionId)
  
  if (!progress) {
    return NextResponse.json({ error: 'Session not found' }, { status: 404 })
  }
  
  return NextResponse.json(progress)
}
