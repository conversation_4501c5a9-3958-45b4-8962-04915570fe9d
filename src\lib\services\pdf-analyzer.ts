/**
 * PDF Analyzer Service
 * Handles PDF processing, text extraction, and conversion to images for vision analysis
 */

import { geminiService } from '@/lib/gemini'
import { takeoffLogger, logProcessingStep, logError } from './logger-wrapper'
import { pdfToImageConverter } from './pdf-to-image'
import type { CompanyType } from '@/lib/company-types'
import type { DetectedMaterial } from '@/types'

export interface PDFAnalysisResult {
  pageCount: number
  textContent: string
  metadata: {
    title?: string
    author?: string
    subject?: string
    keywords?: string
    creator?: string
    producer?: string
    creationDate?: Date
    modificationDate?: Date
  }
  drawingInfo: {
    type: DrawingType
    scale?: string
    sheetNumber?: string
    revision?: string
    discipline?: string
    projectName?: string
    projectNumber?: string
  }
  images: Array<{
    pageNumber: number
    base64: string
    width: number
    height: number
  }>
  extractedMaterials?: DetectedMaterial[]
 vectorData?: any[]
}



export type DrawingType = 
  | 'architectural' 
  | 'structural' 
  | 'electrical' 
  | 'plumbing' 
  | 'hvac' 
  | 'site' 
  | 'detail' 
  | 'schedule' 
  | 'specification'
  | 'unknown'

export class PDFAnalyzer {
  private readonly MAX_IMAGE_SIZE = 3072 // Gemini's max dimension
  private readonly OPTIMAL_IMAGE_SIZE = 1536 // Balance between quality and token usage
  
  /**
   * Analyze a PDF file and extract relevant information
   */
  async analyzePDF(
    fileBuffer: Buffer,
    fileName: string,
    companyType?: CompanyType | null
  ): Promise<PDFAnalysisResult> {
    const startTime = Date.now()
    const analysisId = `pdf-analysis-${Date.now()}`
    
    takeoffLogger.info('Starting PDF analysis', {
      analysisId,
      fileName,
      fileSize: fileBuffer.length,
      companyType
    })
    
    try {
      // Dynamically import pdf-parse to avoid module initialization issues
      const pdfParse = (await import('pdf-parse')).default
      
      // Parse PDF to extract text and metadata
      const pdfData = await pdfParse(fileBuffer)
      
      logProcessingStep(takeoffLogger, 'PDF parsed successfully', {
        analysisId,
        pageCount: pdfData.numpages,
        textLength: pdfData.text.length
      }, startTime)
      
      // Extract drawing information from text
      const drawingInfo = await this.extractDrawingInfo(pdfData.text, fileName, companyType)
      
      // Extract materials using AI
      const extractedMaterials = await this.extractMaterialsWithAI(
        pdfData.text,
        fileName,
        drawingInfo,
        companyType
      )
      
      // Extract vector data
      const vectorData = await this.extractVectorData(fileBuffer)
      
      // Convert PDF pages to images (if possible)
      const conversionMethod = pdfToImageConverter.getConversionMethod()
      let images: PDFAnalysisResult['images'] = []
      if (pdfToImageConverter.isConversionAvailable()) {
        try {
          images = await pdfToImageConverter.convertPDFToImages(
            fileBuffer,
            pdfData.numpages,
            fileName,
            companyType
          )
        } catch (error) {
          logError(takeoffLogger, error, 'PDF to image conversion failed', {
            analysisId,
            conversionMethod,
          })
        }
      }
      
      logProcessingStep(takeoffLogger, 'PDF analysis completed', {
        analysisId,
        imageCount: images.length,
        materialsExtracted: extractedMaterials.length,
        conversionMethod
      }, startTime)
      
      const result: PDFAnalysisResult = {
        pageCount: pdfData.numpages,
        textContent: pdfData.text,
        metadata: {
          title: pdfData.info?.Title,
          author: pdfData.info?.Author,
          subject: pdfData.info?.Subject,
          keywords: pdfData.info?.Keywords,
          creator: pdfData.info?.Creator,
          producer: pdfData.info?.Producer,
          creationDate: pdfData.info?.CreationDate ? new Date(pdfData.info.CreationDate) : undefined,
          modificationDate: pdfData.info?.ModDate ? new Date(pdfData.info.ModDate) : undefined
        },
        drawingInfo,
        images,
        extractedMaterials,
        vectorData
      }
      
      takeoffLogger.info('PDF analysis completed successfully', {
        analysisId,
        processingTime: Date.now() - startTime,
        drawingType: drawingInfo.type,
        pageCount: result.pageCount,
        materialsFound: extractedMaterials.length
      })
      
      return result
    } catch (error) {
      logError(takeoffLogger, error, 'PDF analysis failed', {
        analysisId,
        fileName
      })
      throw new Error(`Failed to analyze PDF: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  /**
   * Extract materials using AI from PDF text
   */
  private async extractMaterialsWithAI(
    textContent: string,
    fileName: string,
    drawingInfo: PDFAnalysisResult['drawingInfo'],
    companyType?: CompanyType | null
  ): Promise<DetectedMaterial[]> {
    try {
      // Split text into manageable chunks
      const maxChunkSize = 8000
      const chunks = this.splitTextIntoChunks(textContent, maxChunkSize)
      
      let allMaterials: DetectedMaterial[] = []
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i]
        
        const prompt = `You are analyzing a construction blueprint PDF. Extract ALL materials, quantities, and specifications from this drawing text.

Drawing Information:
- File: ${fileName}
- Type: ${drawingInfo.type} drawing
- Scale: ${drawingInfo.scale || 'Unknown'}
- Sheet: ${drawingInfo.sheetNumber || 'Unknown'}
${companyType ? `- Analyzing for: ${companyType}` : ''}

Drawing Text (Part ${i + 1} of ${chunks.length}):
${chunk}

Extract ALL materials mentioned in this drawing, including:
1. Material names and descriptions
2. Quantities with units (look for numbers followed by units like LF, SF, EA, etc.)
3. Specifications (sizes, ratings, models, types)
4. Equipment and fixtures
5. Any material schedules or lists
6. Location or room associations

${companyType ? `Focus especially on materials relevant to ${companyType}.` : ''}
${companyType === 'Electrical Contractor' ? `
For electrical drawings, look specifically for:
- Lighting fixtures (troffers, luminaires, LED panels)
- Lighting controls (occupancy sensors, dimmers, daylight sensors, control panels)
- Switches and devices (switches, receptacles, outlets)
- Emergency/Exit lighting
- Conduits and raceways
- Wire and cable specifications
- Panels and distribution equipment
- Low voltage systems
DO NOT include structural materials like concrete, steel, or masonry.` : ''}

Return ONLY a JSON array of materials found, with this structure:
[
  {
    "name": "material description",
    "type": "category (Electrical/Plumbing/HVAC/Structural/etc)",
    "category": "specific category",
    "quantity": number,
    "unit": "unit of measure",
    "specifications": "any specs, sizes, or ratings",
    "location": "where in building if mentioned",
    "confidence": 0.95
  }
]

IMPORTANT: 
- Extract ACTUAL materials from the drawing, not generic suggestions
- Include ALL quantities mentioned
- Look for material schedules, legends, and notes
- Pay attention to callouts and labels
- If you see model numbers or part numbers, include them`
        
        try {
          const response = await geminiService.sendMessage(prompt, 'pdf-material-extraction', {
            companyType
          })
          
          // Extract JSON from response
          const jsonMatch = response.match(/\[[\s\S]*\]/)
          if (jsonMatch) {
            const materials = JSON.parse(jsonMatch[0]) as DetectedMaterial[]
            allMaterials.push(...materials.map((m: DetectedMaterial) => ({
              ...m,
              pageNumber: i + 1,
              confidence: m.confidence || 0.9
            })))
          }
        } catch (error) {
          takeoffLogger.warn('Failed to extract materials from chunk', { 
            chunkIndex: i,
            error 
          })
        }
      }
      
      // Deduplicate materials
      // Intelligent classification will happen in the takeoff service
      return this.deduplicateMaterials(allMaterials)
    } catch (error) {
      takeoffLogger.error('Failed to extract materials with AI', { error })
      return []
    }
  }
  
  /**
   * Split text into manageable chunks
   */
  private splitTextIntoChunks(text: string, maxSize: number): string[] {
    const chunks: string[] = []
    const lines = text.split('\n')
    let currentChunk = ''
    
    for (const line of lines) {
      if (currentChunk.length + line.length > maxSize && currentChunk.length > 0) {
        chunks.push(currentChunk)
        currentChunk = line
      } else {
        currentChunk += (currentChunk ? '\n' : '') + line
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk)
    }
    
    return chunks
  }
  
  /**
   * Deduplicate extracted materials
   */
  private deduplicateMaterials(materials: DetectedMaterial[]): DetectedMaterial[] {
    const uniqueMaterials = new Map<string, DetectedMaterial>()
    
    for (const material of materials) {
      const key = `${material.name}-${material.specifications || ''}`
      const existing = uniqueMaterials.get(key)
      
      if (existing) {
        // Combine quantities if same material found multiple times
        if (existing.unit === material.unit) {
          existing.quantity += material.quantity
        }
        // Keep the one with higher confidence
        if (material.confidence > existing.confidence) {
          uniqueMaterials.set(key, {
            ...material,
            quantity: existing.quantity + material.quantity
          })
        }
      } else {
        uniqueMaterials.set(key, material)
      }
    }
    
    return Array.from(uniqueMaterials.values())
  }
  
  /**
   * Extract drawing information from PDF text content
   */
  private async extractDrawingInfo(
    textContent: string,
    fileName: string,
    companyType?: CompanyType | null
  ): Promise<PDFAnalysisResult['drawingInfo']> {
    // Common patterns in construction drawings
    const patterns = {
      scale: /SCALE[:\s]+([0-9\/"'\s=:]+)/i,
      sheetNumber: /SHEET[:\s]+([A-Z0-9\-\.]+)/i,
      revision: /REV(?:ISION)?[:\s]+([A-Z0-9\-\.]+)/i,
      projectName: /PROJECT[:\s]+([^\\n]+)/i,
      projectNumber: /PROJECT\s*(?:NO|NUMBER)[:\s]+([A-Z0-9\-]+)/i
    }
    
    const drawingInfo: PDFAnalysisResult['drawingInfo'] = {
      type: 'unknown'
    }
    
    // Extract basic information using patterns
    for (const [key, pattern] of Object.entries(patterns)) {
      const match = textContent.match(pattern)
      if (match) {
        (drawingInfo as any)[key] = match[1].trim()
      }
    }
    
    // Determine drawing type from filename and content
    drawingInfo.type = this.determineDrawingType(fileName, textContent)
    drawingInfo.discipline = this.determineDiscipline(fileName, textContent, drawingInfo.type)
    
    // Use AI to extract additional context if needed
    if (companyType && textContent.length > 100) {
      const aiPrompt = `Analyze this construction drawing text and identify:
1. Drawing type and discipline
2. Scale information
3. Any ${companyType}-specific elements

Text excerpt: ${textContent.substring(0, 1000)}

Respond with JSON containing: type, scale, discipline, relevantElements[]`
      
      try {
        const aiResponse = await geminiService.sendMessage(aiPrompt, 'pdf-analyzer', { companyType })
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          const aiData = JSON.parse(jsonMatch[0])
          
          // Merge AI insights with pattern matching
          drawingInfo.type = aiData.type || drawingInfo.type
          drawingInfo.scale = aiData.scale || drawingInfo.scale
          drawingInfo.discipline = aiData.discipline || drawingInfo.discipline
        }
      } catch (error) {
        takeoffLogger.debug('AI drawing info extraction failed, using pattern matching only', { error })
      }
    }
    
    return drawingInfo
  }
  
  /**
   * Determine drawing type from filename and content
   */
  private determineDrawingType(fileName: string, textContent: string): DrawingType {
    const lowerName = fileName.toLowerCase()
    const lowerText = textContent.toLowerCase()
    
    // Check filename patterns
    if (lowerName.includes('arch') || lowerName.match(/^a\d/)) return 'architectural'
    if (lowerName.includes('struct') || lowerName.match(/^s\d/)) return 'structural'
    if (lowerName.includes('elec') || lowerName.match(/^e\d/)) return 'electrical'
    if (lowerName.includes('plumb') || lowerName.match(/^p\d/)) return 'plumbing'
    if (lowerName.includes('mech') || lowerName.includes('hvac') || lowerName.match(/^m\d/)) return 'hvac'
    if (lowerName.includes('site') || lowerName.includes('civil') || lowerName.match(/^c\d/)) return 'site'
    if (lowerName.includes('detail') || lowerName.match(/^d\d/)) return 'detail'
    if (lowerName.includes('schedule')) return 'schedule'
    if (lowerName.includes('spec')) return 'specification'
    
    // Check content patterns
    if (lowerText.includes('floor plan') || lowerText.includes('elevation')) return 'architectural'
    if (lowerText.includes('foundation') || lowerText.includes('framing')) return 'structural'
    if (lowerText.includes('power') || lowerText.includes('lighting')) return 'electrical'
    if (lowerText.includes('water') || lowerText.includes('drain')) return 'plumbing'
    if (lowerText.includes('duct') || lowerText.includes('diffuser')) return 'hvac'
    
    return 'unknown'
  }
  
  /**
   * Determine drawing discipline
   */
  private determineDiscipline(fileName: string, textContent: string, drawingType: DrawingType): string {
    const disciplineMap: Record<DrawingType, string> = {
      architectural: 'Architectural',
      structural: 'Structural',
      electrical: 'Electrical',
      plumbing: 'Plumbing',
      hvac: 'Mechanical',
      site: 'Civil',
      detail: 'Detail',
      schedule: 'Schedule',
      specification: 'Specification',
      unknown: 'General'
    }
    
    return disciplineMap[drawingType]
  }
  
  
  
  /**
   * Extract scale from drawing text
   */
  extractScale(text: string): number {
    // Common scale patterns in construction drawings
    const scalePatterns = [
      /1[:\s]*=[\s]*(\d+)/i,           // 1 = 50
      /1"[\s]*=[\s]*(\d+)'/i,          // 1" = 50'
      /1\/(\d+)/i,                      // 1/50
      /(\d+)[\s]*:[\s]*1/i,            // 50:1
      /SCALE[\s:]+1[:\s]*(\d+)/i       // SCALE: 1:50
    ]
    
    for (const pattern of scalePatterns) {
      const match = text.match(pattern)
      if (match) {
        const scaleValue = parseInt(match[1])
        if (scaleValue > 0 && scaleValue < 1000) {
          return 1 / scaleValue // Return as decimal (e.g., 1:50 = 0.02)
        }
      }
    }
    
    // Default scales based on drawing type
    if (text.toLowerCase().includes('detail')) return 1 / 10  // 1:10 for details
    if (text.toLowerCase().includes('site')) return 1 / 100   // 1:100 for site plans
    return 1 / 50 // Default to 1:50
  }
  
  /**
   * Identify relevant sheets for a specific trade
   */
  identifyTradeSheets(
    pdfAnalysis: PDFAnalysisResult,
    companyType: CompanyType
  ): number[] {
    const relevantPages: number[] = []
    
    // Map company types to relevant drawing types and keywords
    const tradeKeywords: Record<string, { types: DrawingType[], keywords: string[] }> = {
      'Electrical Contractor': {
        types: ['electrical'],
        keywords: ['power', 'lighting', 'panel', 'circuit', 'conduit', 'wire', 'electrical']
      },
      'Plumbing Contractor': {
        types: ['plumbing'],
        keywords: ['plumbing', 'pipe', 'water', 'drain', 'fixture', 'valve', 'sewer']
      },
      'HVAC Contractor': {
        types: ['hvac'],
        keywords: ['hvac', 'mechanical', 'duct', 'diffuser', 'vav', 'equipment', 'refrigerant']
      },
      'General Contractor': {
        types: ['architectural', 'structural', 'site'],
        keywords: ['floor plan', 'elevation', 'section', 'foundation', 'framing']
      }
    }
    
    const tradeInfo = tradeKeywords[companyType] || tradeKeywords['General Contractor']
    
    // Check if the drawing type matches
    if (tradeInfo.types.includes(pdfAnalysis.drawingInfo.type)) {
      // All pages are relevant if the drawing type matches
      return Array.from({ length: pdfAnalysis.pageCount }, (_, i) => i + 1)
    }
    
    // Otherwise, search for relevant keywords in text
    const lowerText = pdfAnalysis.textContent.toLowerCase()
    const hasRelevantContent = tradeInfo.keywords.some(keyword => lowerText.includes(keyword))
    
    if (hasRelevantContent) {
      // Return all pages if relevant content found
      return Array.from({ length: pdfAnalysis.pageCount }, (_, i) => i + 1)
    }
    
    return relevantPages
  }
   /**
   * Extract vector data from PDF
   */
  private async extractVectorData(fileBuffer: Buffer): Promise<any[]> {
    try {
      // This is a placeholder for a more robust vector extraction implementation
      // For now, we'll just return an empty array
      return []
    } catch (error) {
      takeoffLogger.error('Failed to extract vector data', { error })
      return []
    }
  }
}

// Export singleton instance
export const pdfAnalyzer = new PDFAnalyzer()
