import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { redis } from '@/lib/redis';

interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: ServiceStatus;
    redis: ServiceStatus;
    gemini: ServiceStatus;
  };
  system: {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
  };
}

interface ServiceStatus {
  status: 'connected' | 'disconnected' | 'error';
  responseTime?: number;
  error?: string;
}

async function checkDatabase(): Promise<ServiceStatus> {
  const start = Date.now();
  try {
    await db.$queryRaw`SELECT 1`;
    return {
      status: 'connected',
      responseTime: Date.now() - start,
    };
  } catch (error) {
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function checkRedis(): Promise<ServiceStatus> {
  const start = Date.now();
  try {
    if (!redis) {
      return { status: 'disconnected' };
    }
    await redis.ping();
    return {
      status: 'connected',
      responseTime: Date.now() - start,
    };
  } catch (error) {
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function checkGemini(): Promise<ServiceStatus> {
  try {
    // Check if Gemini API key is configured
    if (!process.env.GEMINI_API_KEY) {
      return {
        status: 'disconnected',
        error: 'API key not configured',
      };
    }
    
    // We don't make an actual API call to avoid rate limits
    // Just verify configuration
    return {
      status: 'connected',
    };
  } catch (error) {
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

function getSystemInfo() {
  const memoryUsage = process.memoryUsage();
  const totalMemory = memoryUsage.heapTotal;
  const usedMemory = memoryUsage.heapUsed;
  
  return {
    memory: {
      used: Math.round(usedMemory / 1024 / 1024), // MB
      total: Math.round(totalMemory / 1024 / 1024), // MB
      percentage: Math.round((usedMemory / totalMemory) * 100),
    },
    cpu: {
      usage: Math.round(process.cpuUsage().user / 1000), // ms
    },
  };
}

export async function GET() {
  try {
    // Parallel health checks
    const [databaseStatus, redisStatus, geminiStatus] = await Promise.all([
      checkDatabase(),
      checkRedis(),
      checkGemini(),
    ]);

    const allServicesHealthy = 
      databaseStatus.status === 'connected' &&
      redisStatus.status === 'connected' &&
      geminiStatus.status === 'connected';

    const health: HealthStatus = {
      status: allServicesHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: databaseStatus,
        redis: redisStatus,
        gemini: geminiStatus,
      },
      system: getSystemInfo(),
    };

    return NextResponse.json(health, {
      status: allServicesHealthy ? 200 : 503,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      {
        status: 503,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        },
      }
    );
  }
}

// Lightweight health check for load balancers
export async function HEAD() {
  try {
    // Quick database connectivity check
    await db.$queryRaw`SELECT 1`;
    return new NextResponse(null, { status: 200 });
  } catch {
    return new NextResponse(null, { status: 503 });
  }
}