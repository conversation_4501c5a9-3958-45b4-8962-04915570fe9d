# Clean up temporary test directories and files
# Run this script to remove test directories created during development

$testDirs = @(
    "src\app\api\takeoff-test",
    "src\app\api\test",
    "src\app\api\test-takeoff", 
    "src\app\api\takeoff-simple",
    "src\app\api\simple",
    "src\app\api\minimal",
    "src\app\api\hello",
    "src\app\test",
    "src\app\test-api"
)

Write-Host "🧹 Cleaning up temporary test directories..." -ForegroundColor Yellow

foreach ($dir in $testDirs) {
    if (Test-Path $dir) {
        try {
            Remove-Item $dir -Recurse -Force
            Write-Host "✅ Removed: $dir" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to remove: $dir - $_" -ForegroundColor Red
        }
    } else {
        Write-Host "⏭️  Skipped (not found): $dir" -ForegroundColor Gray
    }
}

# Check for duplicate log entries in logs directory
Write-Host "`n📋 Checking log files..." -ForegroundColor Yellow
$logDirs = @("logs\api", "logs\gemini", "logs\takeoff", "logs\vision")

foreach ($logDir in $logDirs) {
    if (Test-Path $logDir) {
        $logFiles = Get-ChildItem $logDir -Filter "*.log" | Where-Object { $_.Length -gt 50MB }
        if ($logFiles.Count -gt 0) {
            Write-Host "⚠️  Large log files found in $logDir:" -ForegroundColor Yellow
            foreach ($file in $logFiles) {
                Write-Host "   - $($file.Name) ($('{0:N2}' -f ($file.Length / 1MB)) MB)" -ForegroundColor Gray
            }
        }
    }
}

Write-Host "`n✨ Cleanup completed!" -ForegroundColor Green
Write-Host "💡 Run 'npm run dev' to ensure everything still works properly" -ForegroundColor Cyan
