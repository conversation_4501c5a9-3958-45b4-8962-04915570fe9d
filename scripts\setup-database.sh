#!/bin/bash

echo "🚀 Setting up PostgreSQL database for AI Construction Management"
echo ""

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if .env.local exists
if [ ! -f .env.local ]; then
    echo -e "${RED}❌ .env.local file not found!${NC}"
    echo "Creating .env.local from .env.example..."
    cp .env.example .env.local
    echo -e "${YELLOW}⚠️  Please update the DATABASE_URL in .env.local with your PostgreSQL connection string${NC}"
    exit 1
fi

# Check if DATABASE_URL is set
DATABASE_URL=$(grep DATABASE_URL .env.local | cut -d '=' -f2- | tr -d '"')
if [ -z "$DATABASE_URL" ] || [ "$DATABASE_URL" = "postgresql://user:password@localhost:5432/ai_construction_db" ]; then
    echo -e "${RED}❌ DATABASE_URL is not configured in .env.local${NC}"
    echo ""
    echo "Please update DATABASE_URL in .env.local with your PostgreSQL connection string:"
    echo "Example: DATABASE_URL=\"postgresql://username:password@localhost:5432/ai_construction_db\""
    echo ""
    echo "If you don't have PostgreSQL installed:"
    echo "  - macOS: brew install postgresql && brew services start postgresql"
    echo "  - Ubuntu: sudo apt-get install postgresql postgresql-contrib"
    echo "  - Windows: Download from https://www.postgresql.org/download/windows/"
    exit 1
fi

echo -e "${GREEN}✓ Found DATABASE_URL configuration${NC}"
echo ""

# Generate Prisma Client
echo "📦 Generating Prisma Client..."
npm run db:generate
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to generate Prisma Client${NC}"
    exit 1
fi

echo ""
echo "🔄 Pushing database schema..."
npm run db:push
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to push database schema${NC}"
    echo "Make sure your PostgreSQL server is running and the DATABASE_URL is correct"
    exit 1
fi

echo ""
echo "🌱 Seeding database with initial data..."
npm run db:seed
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠️  Failed to seed database (this is optional)${NC}"
else
    echo -e "${GREEN}✓ Database seeded successfully${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Database setup completed successfully!${NC}"
echo ""
echo "Demo credentials:"
echo "  Admin: <EMAIL> / demo123456"
echo "  Contractor: <EMAIL> / demo123456"
echo ""
echo "You can now run 'npm run dev' to start the application"
echo "Or run 'npm run db:studio' to explore your database"