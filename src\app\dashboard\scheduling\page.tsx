'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  <PERSON>,
  Zap,
  <PERSON>,
  TrendingUp,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Clock,
  Users,
  Wrench,
  DollarSign,
  Play,
  Pause,
  RotateCw,
  Settings,
  Filter,
  Download,
  Upload,
  GitBranch,
  BarChart3,
  Target,
  Loader2,
  CheckCircle2,
  XCircle,
  Info,
  ChevronRight,
  Maximize2,
  Sun,
  Cloud,
  CloudRain
} from 'lucide-react'
import { cn } from '@/lib/utils'
import Link from 'next/link'
import type { Schedule, Task, TaskStatus } from '@/types'

// Mock schedule scenarios generated by AI
const mockScenarios = [
  {
    id: '1',
    name: 'Baseline Schedule',
    duration: 365,
    cost: 125000000,
    resources: 450,
    riskScore: 35,
    feasibility: 85,
    criticalPathLength: 280,
    weatherImpact: 'low',
    optimizationScore: 72
  },
  {
    id: '2',
    name: 'Accelerated Schedule',
    duration: 320,
    cost: 138000000,
    resources: 580,
    riskScore: 55,
    feasibility: 70,
    criticalPathLength: 250,
    weatherImpact: 'medium',
    optimizationScore: 65
  },
  {
    id: '3',
    name: 'Cost Optimized',
    duration: 385,
    cost: 118000000,
    resources: 380,
    riskScore: 25,
    feasibility: 92,
    criticalPathLength: 290,
    weatherImpact: 'low',
    optimizationScore: 88
  },
  {
    id: '4',
    name: 'AI Optimized',
    duration: 342,
    cost: 122000000,
    resources: 420,
    riskScore: 28,
    feasibility: 90,
    criticalPathLength: 265,
    weatherImpact: 'low',
    optimizationScore: 94
  }
]

// Mock tasks for Gantt chart
const mockTasks: Task[] = [
  {
    id: '1',
    scheduleId: 'schedule-1',
    name: 'Site Preparation',
    type: 'work_package',
    status: 'completed',
    plannedStartDate: new Date('2024-01-15'),
    plannedEndDate: new Date('2024-02-28'),
    actualStartDate: new Date('2024-01-15'),
    actualEndDate: new Date('2024-02-25'),
    duration: 45,
    progress: 100,
    dependencies: [],
    resources: [],
    constraints: [],
    riskFactors: []
  },
  {
    id: '2',
    scheduleId: 'schedule-1',
    name: 'Foundation',
    type: 'work_package',
    status: 'completed',
    plannedStartDate: new Date('2024-03-01'),
    plannedEndDate: new Date('2024-04-30'),
    actualStartDate: new Date('2024-03-01'),
    actualEndDate: new Date('2024-04-28'),
    duration: 60,
    progress: 100,
    dependencies: ['1'],
    resources: [],
    constraints: [],
    riskFactors: []
  },
  {
    id: '3',
    scheduleId: 'schedule-1',
    name: 'Structural Steel',
    type: 'work_package',
    status: 'in_progress',
    plannedStartDate: new Date('2024-05-01'),
    plannedEndDate: new Date('2024-07-31'),
    actualStartDate: new Date('2024-05-01'),
    duration: 90,
    progress: 65,
    dependencies: ['2'],
    resources: [],
    constraints: [],
    riskFactors: []
  },
  {
    id: '4',
    scheduleId: 'schedule-1',
    name: 'MEP Rough-In',
    type: 'work_package',
    status: 'not_started',
    plannedStartDate: new Date('2024-07-15'),
    plannedEndDate: new Date('2024-09-30'),
    duration: 75,
    progress: 0,
    dependencies: ['3'],
    resources: [],
    constraints: [],
    riskFactors: []
  },
  {
    id: '5',
    scheduleId: 'schedule-1',
    name: 'Interior Finishes',
    type: 'work_package',
    status: 'not_started',
    plannedStartDate: new Date('2024-10-01'),
    plannedEndDate: new Date('2024-12-31'),
    duration: 90,
    progress: 0,
    dependencies: ['4'],
    resources: [],
    constraints: [],
    riskFactors: []
  }
]

const weatherForecast = [
  { date: 'Mon', condition: 'clear', impact: 'none', icon: Sun },
  { date: 'Tue', condition: 'clear', impact: 'none', icon: Sun },
  { date: 'Wed', condition: 'cloudy', impact: 'low', icon: Cloud },
  { date: 'Thu', condition: 'rain', impact: 'high', icon: CloudRain },
  { date: 'Fri', condition: 'rain', impact: 'high', icon: CloudRain },
  { date: 'Sat', condition: 'cloudy', impact: 'low', icon: Cloud },
  { date: 'Sun', condition: 'clear', impact: 'none', icon: Sun }
]

export default function SchedulingPage() {
  const [selectedScenario, setSelectedScenario] = useState(mockScenarios[0])
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [showWhatIf, setShowWhatIf] = useState(false)
  const [viewMode, setViewMode] = useState<'gantt' | '4d' | 'network'>('gantt')
  const [simulationSpeed, setSimulationSpeed] = useState(1)
  const [isPlaying, setIsPlaying] = useState(false)

  // What-if parameters
  const [whatIfParams, setWhatIfParams] = useState({
    overtime: false,
    additionalCrews: 0,
    accelerateCritical: false,
    weatherBuffer: 5
  })

  const handleOptimize = () => {
    setIsOptimizing(true)
    // Simulate optimization process
    setTimeout(() => {
      setIsOptimizing(false)
      setSelectedScenario(mockScenarios[3]) // Select AI optimized scenario
    }, 3000)
  }

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case 'completed': return 'bg-green-500'
      case 'in_progress': return 'bg-blue-500'
      case 'delayed': return 'bg-red-500'
      case 'blocked': return 'bg-orange-500'
      default: return 'bg-gray-300'
    }
  }

  const getWeatherImpactColor = (impact: string) => {
    switch (impact) {
      case 'none': return 'text-green-500'
      case 'low': return 'text-yellow-500'
      case 'high': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            AI Scheduling Optimization
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            Generate and optimize millions of schedule scenarios with AI
          </p>
        </div>
        <div className="flex space-x-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleOptimize}
            disabled={isOptimizing}
            className="inline-flex items-center px-4 py-2 bg-construction-blue text-white rounded-lg shadow-sm hover:bg-construction-blue/90 transition-colors disabled:opacity-50"
          >
            {isOptimizing ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Optimizing...
              </>
            ) : (
              <>
                <Brain className="w-5 h-5 mr-2" />
                AI Optimize
              </>
            )}
          </motion.button>
          <button className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <Upload className="w-5 h-5 mr-2" />
            Import P6
          </button>
        </div>
      </div>

      {/* Scenario Comparison */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
          Schedule Scenarios
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {mockScenarios.map((scenario) => (
            <motion.div
              key={scenario.id}
              whileHover={{ scale: 1.02 }}
              onClick={() => setSelectedScenario(scenario)}
              className={cn(
                "p-4 rounded-lg border-2 cursor-pointer transition-all",
                selectedScenario.id === scenario.id
                  ? "border-construction-blue bg-construction-blue/5 dark:bg-construction-blue/10"
                  : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
              )}
            >
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  {scenario.name}
                </h3>
                {scenario.id === '4' && (
                  <Brain className="w-5 h-5 text-purple-500" />
                )}
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Duration</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {scenario.duration} days
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Cost</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    ${(scenario.cost / 1000000).toFixed(1)}M
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Risk</span>
                  <span className={cn(
                    "font-medium",
                    scenario.riskScore < 30 && "text-green-500",
                    scenario.riskScore >= 30 && scenario.riskScore < 60 && "text-yellow-500",
                    scenario.riskScore >= 60 && "text-red-500"
                  )}>
                    {scenario.riskScore}%
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Score</span>
                  <div className="flex items-center">
                    <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                      <div 
                        className="bg-construction-blue h-2 rounded-full"
                        style={{ width: `${scenario.optimizationScore}%` }}
                      />
                    </div>
                    <span className="font-medium text-gray-900 dark:text-white text-xs">
                      {scenario.optimizationScore}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Schedule Visualization */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
          >
            {/* Visualization Controls */}
            <div className="border-b border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setViewMode('gantt')}
                    className={cn(
                      "px-3 py-1.5 text-sm rounded-lg transition-colors",
                      viewMode === 'gantt'
                        ? "bg-construction-blue text-white"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                    )}
                  >
                    Gantt Chart
                  </button>
                  <button
                    onClick={() => setViewMode('4d')}
                    className={cn(
                      "px-3 py-1.5 text-sm rounded-lg transition-colors",
                      viewMode === '4d'
                        ? "bg-construction-blue text-white"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                    )}
                  >
                    4D Simulation
                  </button>
                  <button
                    onClick={() => setViewMode('network')}
                    className={cn(
                      "px-3 py-1.5 text-sm rounded-lg transition-colors",
                      viewMode === 'network'
                        ? "bg-construction-blue text-white"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                    )}
                  >
                    Network Diagram
                  </button>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="p-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                  </button>
                  <select
                    value={simulationSpeed}
                    onChange={(e) => setSimulationSpeed(Number(e.target.value))}
                    className="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value={0.5}>0.5x</option>
                    <option value={1}>1x</option>
                    <option value={2}>2x</option>
                    <option value={5}>5x</option>
                  </select>
                  <button className="p-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600">
                    <Maximize2 className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Gantt Chart Visualization */}
            {viewMode === 'gantt' && (
              <div className="p-6">
                <div className="space-y-4">
                  {mockTasks.map((task, index) => {
                    const totalDays = 365
                    const startOffset = Math.floor((task.plannedStartDate.getTime() - new Date('2024-01-01').getTime()) / (1000 * 60 * 60 * 24))
                    const widthPercentage = (task.duration / totalDays) * 100
                    const leftPercentage = (startOffset / totalDays) * 100
                    
                    return (
                      <motion.div
                        key={task.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="relative"
                      >
                        <div className="flex items-center mb-2">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300 w-32">
                            {task.name}
                          </span>
                          <div className="flex-1 relative h-8 bg-gray-100 dark:bg-gray-700 rounded-lg ml-4">
                            <div
                              className={cn(
                                "absolute h-full rounded-lg flex items-center px-2",
                                getStatusColor(task.status)
                              )}
                              style={{
                                left: `${leftPercentage}%`,
                                width: `${widthPercentage}%`
                              }}
                            >
                              {task.progress > 0 && (
                                <div
                                  className="absolute inset-0 bg-black/20 rounded-lg"
                                  style={{ width: `${task.progress}%` }}
                                />
                              )}
                              <span className="text-xs text-white font-medium relative z-10">
                                {task.progress}%
                              </span>
                            </div>
                            {/* Dependencies */}
                            {task.dependencies.length > 0 && (
                              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-full">
                                <GitBranch className="w-4 h-4 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <span className="text-sm text-gray-600 dark:text-gray-400 ml-4">
                            {task.duration}d
                          </span>
                        </div>
                      </motion.div>
                    )
                  })}
                </div>

                {/* Time scale */}
                <div className="mt-6 flex justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>Jan</span>
                  <span>Apr</span>
                  <span>Jul</span>
                  <span>Oct</span>
                  <span>Dec</span>
                </div>
              </div>
            )}

            {/* 4D Simulation Placeholder */}
            {viewMode === '4d' && (
              <div className="h-96 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                <div className="text-center">
                  <Zap className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    4D BIM Simulation
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                    Visual construction sequence simulation
                  </p>
                </div>
              </div>
            )}

            {/* Network Diagram Placeholder */}
            {viewMode === 'network' && (
              <div className="h-96 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                <div className="text-center">
                  <GitBranch className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    Critical Path Network
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                    Activity dependencies and float analysis
                  </p>
                </div>
              </div>
            )}

            {/* Critical Path Info */}
            <div className="border-t border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Critical Path: <span className="font-medium text-red-500">{selectedScenario.criticalPathLength} days</span>
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    Float: <span className="font-medium text-green-500">
                      {selectedScenario.duration - selectedScenario.criticalPathLength} days
                    </span>
                  </span>
                </div>
                <button className="text-sm text-construction-blue hover:text-construction-blue/80">
                  View Details →
                </button>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Side Panel */}
        <div className="space-y-6">
          {/* Scenario Details */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Scenario Analysis
            </h3>
            
            <div className="space-y-4">
              {/* Key Metrics */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Duration</span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {selectedScenario.duration} days
                  </span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Cost</span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    ${(selectedScenario.cost / 1000000).toFixed(1)}M
                  </span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Resources</span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {selectedScenario.resources} workers
                  </span>
                </div>
              </div>

              {/* Risk Assessment */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Risk Assessment
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Overall Risk</span>
                    <div className="flex items-center">
                      <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                        <div 
                          className={cn(
                            "h-2 rounded-full",
                            selectedScenario.riskScore < 30 && "bg-green-500",
                            selectedScenario.riskScore >= 30 && selectedScenario.riskScore < 60 && "bg-yellow-500",
                            selectedScenario.riskScore >= 60 && "bg-red-500"
                          )}
                          style={{ width: `${selectedScenario.riskScore}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium">
                        {selectedScenario.riskScore}%
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Feasibility</span>
                    <span className={cn(
                      "text-sm font-medium",
                      selectedScenario.feasibility >= 80 && "text-green-500",
                      selectedScenario.feasibility >= 60 && selectedScenario.feasibility < 80 && "text-yellow-500",
                      selectedScenario.feasibility < 60 && "text-red-500"
                    )}>
                      {selectedScenario.feasibility}%
                    </span>
                  </div>
                </div>
              </div>

              {/* AI Recommendations */}
              {selectedScenario.id === '4' && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                    <Brain className="w-4 h-4 mr-2 text-purple-500" />
                    AI Recommendations
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-start">
                      <CheckCircle2 className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Optimized crew allocation saves 23 days
                      </p>
                    </div>
                    <div className="flex items-start">
                      <CheckCircle2 className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Parallel MEP installation reduces critical path
                      </p>
                    </div>
                    <div className="flex items-start">
                      <Info className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Consider prefabrication for 15% time savings
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>

          {/* What-If Analysis */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                What-If Analysis
              </h3>
              <button
                onClick={() => setShowWhatIf(!showWhatIf)}
                className="text-construction-blue hover:text-construction-blue/80"
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>

            {showWhatIf ? (
              <div className="space-y-4">
                <div>
                  <label className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Enable Overtime
                    </span>
                    <input
                      type="checkbox"
                      checked={whatIfParams.overtime}
                      onChange={(e) => setWhatIfParams({...whatIfParams, overtime: e.target.checked})}
                      className="rounded text-construction-blue"
                    />
                  </label>
                </div>
                <div>
                  <label className="text-sm text-gray-700 dark:text-gray-300 block mb-2">
                    Additional Crews
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="10"
                    value={whatIfParams.additionalCrews}
                    onChange={(e) => setWhatIfParams({...whatIfParams, additionalCrews: Number(e.target.value)})}
                    className="w-full"
                  />
                  <span className="text-xs text-gray-500">{whatIfParams.additionalCrews} crews</span>
                </div>
                <div>
                  <label className="text-sm text-gray-700 dark:text-gray-300 block mb-2">
                    Weather Buffer (days)
                  </label>
                  <input
                    type="number"
                    value={whatIfParams.weatherBuffer}
                    onChange={(e) => setWhatIfParams({...whatIfParams, weatherBuffer: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <button className="w-full px-4 py-2 bg-construction-blue text-white rounded-lg hover:bg-construction-blue/90 transition-colors">
                  Run Analysis
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      Time Impact
                    </span>
                    <span className="text-sm font-medium text-green-500">
                      -12 days
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      Cost Impact
                    </span>
                    <span className="text-sm font-medium text-red-500">
                      +$2.3M
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      Risk Change
                    </span>
                    <span className="text-sm font-medium text-yellow-500">
                      +8%
                    </span>
                  </div>
                </div>
              </div>
            )}
          </motion.div>

          {/* Weather Impact */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Weather Forecast
            </h3>
            <div className="grid grid-cols-7 gap-2">
              {weatherForecast.map((day) => (
                <div key={day.date} className="text-center">
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                    {day.date}
                  </p>
                  <day.icon className={cn(
                    "w-6 h-6 mx-auto mb-1",
                    getWeatherImpactColor(day.impact)
                  )} />
                  <p className={cn(
                    "text-xs font-medium",
                    getWeatherImpactColor(day.impact)
                  )}>
                    {day.impact}
                  </p>
                </div>
              ))}
            </div>
            <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-500 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Weather Alert
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                    Rain expected Thu-Fri. Consider rescheduling exterior work.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Resource Optimization Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Resource Optimization
          </h3>
          <div className="flex items-center space-x-4">
            <button className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
              Labor
            </button>
            <button className="text-sm text-construction-blue font-medium">
              Equipment
            </button>
            <button className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
              Materials
            </button>
          </div>
        </div>

        {/* Resource chart visualization would go here */}
        <div className="h-48 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-lg flex items-center justify-center">
          <BarChart3 className="w-12 h-12 text-gray-400 dark:text-gray-500" />
        </div>

        <div className="mt-4 grid grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">87%</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Crane Utilization</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">92%</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Labor Efficiency</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">$1.2M</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Cost Savings</p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
