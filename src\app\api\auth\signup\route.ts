import { NextRequest, NextResponse } from 'next/server'
import { signUp } from '@/lib/db/auth'
import { z } from 'zod'
import { abbreviatedToFull } from '@/lib/company-type-map'

const signUpSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  name: z.string().optional(),
  companyName: z.string().optional(),
  companyType: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = signUpSchema.parse(body)
    
    // Convert abbreviated company type to full name
    if (validatedData.companyType) {
      const fullCompanyType = abbreviatedToFull(validatedData.companyType)
      if (fullCompanyType) {
        validatedData.companyType = fullCompanyType
      }
    }
    
    // Create user and session
    const { user, session, token, expiresAt } = await signUp(validatedData)
    
    // Create response
    const response = NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        companyType: user.companyType,
        companyName: user.companyName,
      },
      message: 'Account created successfully',
      redirectUrl: '/dashboard',
    })
    
    // Set the session cookie in the response
    response.cookies.set('session', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: expiresAt,
      path: '/',
    })
    
    return response
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }
    
    if (error instanceof Error && error.message === 'User already exists') {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 409 }
      )
    }
    
    console.error('Signup error:', error)
    return NextResponse.json(
      { error: 'Failed to create account' },
      { status: 500 }
    )
  }
}