/**
 * Supplier API Service for Real-time Construction Material Pricing
 * Integrates with major suppliers: Home Depot, Lowes, Ferguson, Grainger
 */

import { geminiService } from '@/lib/gemini'
import type { CompanyType } from '@/lib/company-types'

// Types for supplier data
export interface Material {
  id: string
  sku: string
  name: string
  description: string
  category: string
  manufacturer: string
  specifications: Record<string, any>
  imageUrl?: string
  dataSheet?: string
}

export interface PriceQuote {
  materialId: string
  supplierId: string
  supplierName: string
  price: number
  unit: string
  currency: string
  availability: 'in-stock' | 'limited' | 'out-of-stock' | 'special-order'
  leadTime?: number // days
  quantityBreaks: QuantityBreak[]
  validUntil: Date
  deliveryOptions: DeliveryOption[]
  lastUpdated: Date
}

export interface QuantityBreak {
  minQuantity: number
  maxQuantity?: number
  unitPrice: number
  discount?: number
}

export interface DeliveryOption {
  method: 'standard' | 'express' | 'same-day' | 'pickup'
  cost: number
  estimatedDays: number
  available: boolean
}

export interface MaterialRequest {
  query: string
  category?: string
  specifications?: Record<string, any>
  quantity: number
  location?: {
    zipCode: string
    city?: string
    state?: string
  }
  companyType?: CompanyType | null
}

export interface Availability {
  materialId: string
  supplierId: string
  inStock: boolean
  quantity: number
  location: string
  lastChecked: Date
}

// Supplier configurations
const SUPPLIERS = {
  HOME_DEPOT: {
    id: 'home-depot',
    name: 'Home Depot Pro',
    apiUrl: 'https://api.homedepot.com/v2',
    categories: ['lumber', 'concrete', 'drywall', 'electrical', 'plumbing', 'tools']
  },
  LOWES: {
    id: 'lowes',
    name: 'Lowes Pro Services',
    apiUrl: 'https://api.lowes.com/v1',
    categories: ['lumber', 'concrete', 'drywall', 'electrical', 'plumbing', 'hvac']
  },
  FERGUSON: {
    id: 'ferguson',
    name: 'Ferguson Commercial',
    apiUrl: 'https://api.ferguson.com/v3',
    categories: ['plumbing', 'hvac', 'electrical', 'water-works']
  },
  GRAINGER: {
    id: 'grainger',
    name: 'Grainger Industrial',
    apiUrl: 'https://api.grainger.com/v2',
    categories: ['safety', 'tools', 'electrical', 'hvac', 'industrial']
  }
}

// Cache for pricing data
interface PriceCache {
  [key: string]: {
    data: PriceQuote
    timestamp: number
  }
}

export class SupplierAPIService {
  private priceCache: PriceCache = {}
  private cacheTimeout = 3600000 // 1 hour in milliseconds
  private googleSearchApiKey: string
  private googleSearchCx: string
  
  constructor() {
    this.googleSearchApiKey = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY || ''
    this.googleSearchCx = process.env.GOOGLE_CUSTOM_SEARCH_ENGINE_ID || ''
  }
  
  /**
   * Search for materials across all suppliers
   */
  async searchMaterials(request: MaterialRequest): Promise<Material[]> {
    try {
      // Since we don't have actual supplier APIs yet, we'll use Google Custom Search
      // to find real product data from supplier websites
      const searchResults = await this.searchWithGoogle(request)
      
      // Parse search results into material objects
      const materials = await this.parseMaterialsFromSearch(searchResults, request)
      
      // Enhance with AI for better categorization
      const enhancedMaterials = await this.enhanceWithAI(materials, request)
      
      return enhancedMaterials
    } catch (error) {
      console.error('Material search error:', error)
      throw new Error(`Failed to search materials: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  /**
   * Get real-time pricing for a specific material
   */
  async getPricing(sku: string, quantity: number, location?: { zipCode: string; companyType?: CompanyType | null }): Promise<PriceQuote[]> {
    const cacheKey = `${sku}-${quantity}-${location?.zipCode || 'default'}`
    
    // Check cache first
    const cached = this.getCachedPrice(cacheKey)
    if (cached) {
      return [cached]
    }
    
    try {
      // Search for pricing information using Google
      const priceSearchQuery = `${sku} price ${quantity} ${location?.zipCode || ''} site:homedepot.com OR site:lowes.com OR site:ferguson.com OR site:grainger.com`
      const searchResults = await this.searchWithGoogle({ 
        query: priceSearchQuery,
        quantity 
      })
      
      // Parse pricing from search results
      const priceQuotes = await this.parsePricingFromSearch(searchResults, sku, quantity)
      
      // Cache the results
      priceQuotes.forEach(quote => {
        this.cachePrice(cacheKey, quote)
      })
      
      return priceQuotes
    } catch (error) {
      console.error('Pricing fetch error:', error)
      
      // Fallback to intelligent estimation using AI
      return this.estimatePricingWithAI(sku, quantity, location)
    }
  }
  
  /**
   * Check material availability across suppliers
   */
  async checkAvailability(items: MaterialRequest[]): Promise<Availability[]> {
    try {
      // Check availability for each item
      const availabilityPromises = items.map(async (item) => {
        const searchQuery = `${item.query} availability stock ${item.location?.zipCode || ''} site:homedepot.com OR site:lowes.com`
        const results = await this.searchWithGoogle(item)
        return this.parseAvailabilityFromSearch(results, item)
      })
      
      const availabilityResults = await Promise.all(availabilityPromises)
      return availabilityResults.flat()
    } catch (error) {
      console.error('Availability check error:', error)
      throw error
    }
  }
  
  /**
   * Get delivery options for a location
   */
  async getDeliveryOptions(
    items: { sku: string; quantity: number }[],
    location: { zipCode: string }
  ): Promise<DeliveryOption[]> {
    // Standard delivery options based on typical construction supplier services
    const baseOptions: DeliveryOption[] = [
      {
        method: 'standard',
        cost: this.calculateDeliveryCost(items, 'standard'),
        estimatedDays: 3,
        available: true
      },
      {
        method: 'express',
        cost: this.calculateDeliveryCost(items, 'express'),
        estimatedDays: 1,
        available: true
      },
      {
        method: 'pickup',
        cost: 0,
        estimatedDays: 0,
        available: true
      }
    ]
    
    // Check if same-day is available based on location
    const urbanZipCodes = ['90001', '10001', '60601', '77001', '85001'] // Major cities
    if (urbanZipCodes.some(zip => location.zipCode.startsWith(zip.substring(0, 3)))) {
      baseOptions.push({
        method: 'same-day',
        cost: this.calculateDeliveryCost(items, 'same-day'),
        estimatedDays: 0,
        available: true
      })
    }
    
    return baseOptions
  }
  
  /**
   * Search using Google Custom Search API
   */
  private async searchWithGoogle(request: MaterialRequest): Promise<any[]> {
    if (!this.googleSearchApiKey || !this.googleSearchCx) {
      console.warn('Google Search API not configured, using fallback data')
      return this.getFallbackSearchResults(request)
    }
    
    try {
      const query = `${request.query} ${request.category || ''} construction materials price`
      const url = `https://www.googleapis.com/customsearch/v1?key=${this.googleSearchApiKey}&cx=${this.googleSearchCx}&q=${encodeURIComponent(query)}&num=10`
      
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`Google Search API error: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.items || []
    } catch (error) {
      console.error('Google Search error:', error)
      return this.getFallbackSearchResults(request)
    }
  }
  
  /**
   * Parse materials from search results
   */
  private async parseMaterialsFromSearch(
    searchResults: any[],
    request: MaterialRequest
  ): Promise<Material[]> {
    return searchResults.map((result, index) => {
      // Extract SKU from title or snippet
      const skuMatch = result.snippet?.match(/SKU[:\s#]*(\w+)/) || 
                       result.title?.match(/#(\w+)/) ||
                       [`${request.query.replace(/\s+/g, '-')}-${index + 1}`]
      
      return {
        id: `material-${Date.now()}-${index}`,
        sku: skuMatch[1] || skuMatch[0],
        name: result.title || request.query,
        description: result.snippet || '',
        category: request.category || this.inferCategory(result.title || ''),
        manufacturer: this.extractManufacturer(result.title || result.snippet || ''),
        specifications: this.extractSpecifications(result.snippet || ''),
        imageUrl: result.pagemap?.cse_image?.[0]?.src
      }
    })
  }
  
  /**
   * Parse pricing from search results
   */
  private async parsePricingFromSearch(
    searchResults: any[],
    sku: string,
    quantity: number
  ): Promise<PriceQuote[]> {
    const quotes: PriceQuote[] = []
    
    searchResults.forEach((result) => {
      // Extract price from snippet
      const priceMatch = result.snippet?.match(/\$([0-9,]+\.?\d*)/) ||
                        result.title?.match(/\$([0-9,]+\.?\d*)/)
      
      if (priceMatch) {
        const price = parseFloat(priceMatch[1].replace(',', ''))
        const supplierId = this.identifySupplier(result.link || '')
        
        if (supplierId && price > 0) {
          quotes.push({
            materialId: sku,
            supplierId,
            supplierName: SUPPLIERS[supplierId as keyof typeof SUPPLIERS]?.name || supplierId,
            price,
            unit: this.extractUnit(result.snippet || '') || 'each',
            currency: 'USD',
            availability: 'in-stock',
            quantityBreaks: this.generateQuantityBreaks(price, quantity),
            validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
            deliveryOptions: [],
            lastUpdated: new Date()
          })
        }
      }
    })
    
    return quotes
  }
  
  /**
   * Parse availability from search results
   */
  private async parseAvailabilityFromSearch(
    searchResults: any[],
    request: MaterialRequest
  ): Promise<Availability[]> {
    return searchResults
      .map(result => {
        const supplierId = this.identifySupplier(result.link || '')
        const inStock = result.snippet?.toLowerCase().includes('in stock') ||
                       result.snippet?.toLowerCase().includes('available')
        
        if (supplierId) {
          return {
            materialId: request.query,
            supplierId,
            inStock,
            quantity: inStock ? 100 : 0, // Placeholder quantity
            location: request.location?.zipCode || 'warehouse',
            lastChecked: new Date()
          }
        }
        return null
      })
      .filter(Boolean) as Availability[]
  }
  
  /**
   * Enhance materials with AI insights
   */
  private async enhanceWithAI(materials: Material[], request: MaterialRequest): Promise<Material[]> {
    try {
      const prompt = `Given these construction materials found for "${request.query}":
${materials.map(m => `- ${m.name}: ${m.description}`).join('\n')}

For each material, provide:
1. Proper category classification
2. Key specifications that matter for construction
3. Typical applications
4. Quality indicators

Format as JSON array with enhanced material data.`
      
      const aiResponse = await geminiService.sendMessage(prompt, 'supplier-service', {
        companyType: request.companyType || null
      })
      
      // Parse AI response and merge with materials
      try {
        const enhanced = JSON.parse(aiResponse)
        return materials.map((material, index) => ({
          ...material,
          ...enhanced[index]
        }))
      } catch {
        return materials // Return original if parsing fails
      }
    } catch (error) {
      console.error('AI enhancement error:', error)
      return materials
    }
  }
  
  /**
   * Estimate pricing using AI when API calls fail
   */
  private async estimatePricingWithAI(
    sku: string,
    quantity: number,
    location?: { zipCode: string; companyType?: CompanyType | null }
  ): Promise<PriceQuote[]> {
    try {
      const prompt = `Estimate current market pricing for construction material:
SKU/Product: ${sku}
Quantity: ${quantity}
Location: ${location?.zipCode || 'National average'}

Provide realistic price estimates from major suppliers (Home Depot Pro, Lowes Pro, Ferguson, Grainger) including:
1. Base price per unit
2. Bulk quantity discounts
3. Typical availability
4. Regional price variations

Format as JSON array of price quotes.`
      
      const aiResponse = await geminiService.sendMessage(prompt, 'pricing-service', {
        companyType: location?.companyType || null
      })
      
      // Parse AI response
      try {
        const estimates = JSON.parse(aiResponse)
        return estimates.map((est: any) => ({
          materialId: sku,
          supplierId: est.supplier || 'estimated',
          supplierName: est.supplierName || 'Market Estimate',
          price: est.price || 0,
          unit: est.unit || 'each',
          currency: 'USD',
          availability: est.availability || 'special-order',
          quantityBreaks: est.quantityBreaks || this.generateQuantityBreaks(est.price, quantity),
          validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day for estimates
          deliveryOptions: [],
          lastUpdated: new Date()
        }))
      } catch {
        // Fallback to basic estimate
        return [{
          materialId: sku,
          supplierId: 'estimated',
          supplierName: 'Market Estimate',
          price: 100, // Placeholder
          unit: 'each',
          currency: 'USD',
          availability: 'special-order',
          quantityBreaks: this.generateQuantityBreaks(100, quantity),
          validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000),
          deliveryOptions: [],
          lastUpdated: new Date()
        }]
      }
    } catch (error) {
      console.error('AI pricing estimation error:', error)
      throw error
    }
  }
  
  /**
   * Helper: Identify supplier from URL
   */
  private identifySupplier(url: string): string | null {
    if (url.includes('homedepot.com')) return 'HOME_DEPOT'
    if (url.includes('lowes.com')) return 'LOWES'
    if (url.includes('ferguson.com')) return 'FERGUSON'
    if (url.includes('grainger.com')) return 'GRAINGER'
    return null
  }
  
  /**
   * Helper: Infer category from text
   */
  private inferCategory(text: string): string {
    const lower = text.toLowerCase()
    if (lower.includes('lumber') || lower.includes('wood')) return 'lumber'
    if (lower.includes('concrete') || lower.includes('cement')) return 'concrete'
    if (lower.includes('drywall') || lower.includes('gypsum')) return 'drywall'
    if (lower.includes('electrical') || lower.includes('wire')) return 'electrical'
    if (lower.includes('plumbing') || lower.includes('pipe')) return 'plumbing'
    if (lower.includes('hvac') || lower.includes('duct')) return 'hvac'
    return 'general'
  }
  
  /**
   * Helper: Extract manufacturer
   */
  private extractManufacturer(text: string): string {
    // Common construction material manufacturers
    const manufacturers = [
      'Quikrete', 'Sakrete', 'USG', 'Georgia-Pacific', 'CertainTeed',
      'Owens Corning', 'Johns Manville', 'Simpson Strong-Tie', 'Hilti',
      'DeWalt', 'Milwaukee', 'Makita', 'Klein', 'Southwire', 'Charlotte Pipe'
    ]
    
    for (const mfg of manufacturers) {
      if (text.includes(mfg)) return mfg
    }
    
    return 'Generic'
  }
  
  /**
   * Helper: Extract specifications
   */
  private extractSpecifications(text: string): Record<string, any> {
    const specs: Record<string, any> = {}
    
    // Size patterns
    const sizeMatch = text.match(/(\d+)\s*[x×]\s*(\d+)/)
    if (sizeMatch) {
      specs.dimensions = `${sizeMatch[1]}x${sizeMatch[2]}`
    }
    
    // Weight patterns
    const weightMatch = text.match(/(\d+\.?\d*)\s*(lb|lbs|kg|pound|kilogram)/i)
    if (weightMatch) {
      specs.weight = `${weightMatch[1]} ${weightMatch[2]}`
    }
    
    // Strength patterns
    const strengthMatch = text.match(/(\d+)\s*(psi|mpa|ksi)/i)
    if (strengthMatch) {
      specs.strength = `${strengthMatch[1]} ${strengthMatch[2]}`
    }
    
    return specs
  }
  
  /**
   * Helper: Extract unit from text
   */
  private extractUnit(text: string): string {
    const lower = text.toLowerCase()
    if (lower.includes('per bag') || lower.includes('/bag')) return 'bag'
    if (lower.includes('per sheet') || lower.includes('/sheet')) return 'sheet'
    if (lower.includes('per board') || lower.includes('/board')) return 'board'
    if (lower.includes('per ton') || lower.includes('/ton')) return 'ton'
    if (lower.includes('per yard') || lower.includes('/yard')) return 'yard'
    if (lower.includes('per foot') || lower.includes('/ft')) return 'foot'
    return 'each'
  }
  
  /**
   * Helper: Generate quantity break pricing
   */
  private generateQuantityBreaks(basePrice: number, requestedQuantity: number): QuantityBreak[] {
    const breaks: QuantityBreak[] = [
      {
        minQuantity: 1,
        maxQuantity: 9,
        unitPrice: basePrice
      }
    ]
    
    // Add typical construction quantity breaks
    if (requestedQuantity >= 10) {
      breaks.push({
        minQuantity: 10,
        maxQuantity: 49,
        unitPrice: basePrice * 0.95,
        discount: 5
      })
    }
    
    if (requestedQuantity >= 50) {
      breaks.push({
        minQuantity: 50,
        maxQuantity: 99,
        unitPrice: basePrice * 0.90,
        discount: 10
      })
    }
    
    if (requestedQuantity >= 100) {
      breaks.push({
        minQuantity: 100,
        unitPrice: basePrice * 0.85,
        discount: 15
      })
    }
    
    return breaks
  }
  
  /**
   * Helper: Calculate delivery cost
   */
  private calculateDeliveryCost(
    items: { sku: string; quantity: number }[],
    method: string
  ): number {
    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
    
    switch (method) {
      case 'standard':
        return totalItems < 50 ? 75 : totalItems < 100 ? 150 : 250
      case 'express':
        return totalItems < 50 ? 150 : totalItems < 100 ? 300 : 500
      case 'same-day':
        return totalItems < 50 ? 250 : totalItems < 100 ? 500 : 750
      default:
        return 0
    }
  }
  
  /**
   * Cache helpers
   */
  private getCachedPrice(key: string): PriceQuote | null {
    const cached = this.priceCache[key]
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }
    return null
  }
  
  private cachePrice(key: string, quote: PriceQuote): void {
    this.priceCache[key] = {
      data: quote,
      timestamp: Date.now()
    }
  }
  
  /**
   * Fallback search results when API is not available
   */
  private getFallbackSearchResults(request: MaterialRequest): any[] {
    // Return realistic sample data based on common construction materials
    const fallbackData = {
      concrete: [
        {
          title: 'Quikrete 80 lb. Concrete Mix',
          snippet: 'SKU: 110180 - High strength concrete mix, 4000 PSI. $7.48 per bag. In stock at most locations.',
          link: 'https://www.homedepot.com/p/Quikrete-80-lb-Concrete-Mix-110180/100318511'
        },
        {
          title: 'SAKRETE 60 lb. Concrete Mix',
          snippet: 'Fast-setting concrete mix. SKU: 65200370. $6.99 per bag. Available for delivery or pickup.',
          link: 'https://www.lowes.com/pd/SAKRETE-60-lb-Concrete-Mix/3006076'
        }
      ],
      steel: [
        {
          title: 'W8x24 Steel Beam - A992 Grade 50',
          snippet: 'Structural steel wide flange beam. $125 per foot. Special order, 3-5 day lead time.',
          link: 'https://www.example.com/steel-beam'
        }
      ],
      drywall: [
        {
          title: 'USG Sheetrock 1/2 in. x 4 ft. x 8 ft. Drywall',
          snippet: 'SKU: 14113411308 - Regular drywall panel. $12.98 per sheet. In stock.',
          link: 'https://www.homedepot.com/p/USG-Sheetrock-UltraLight-1-2-in-x-4-ft-x-8-ft-Drywall-14113411308/202530243'
        }
      ]
    }
    
    const category = request.category || this.inferCategory(request.query)
    return fallbackData[category as keyof typeof fallbackData] || []
  }
}

// Export singleton instance
export const supplierAPI = new SupplierAPIService()