{"dashboard": {"id": null, "uid": "ai-construction-main", "title": "AI Construction Management - Main Dashboard", "tags": ["production", "ai-construction"], "timezone": "browser", "schemaVersion": 38, "version": 1, "refresh": "30s", "time": {"from": "now-6h", "to": "now"}, "panels": [{"id": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "type": "graph", "title": "Request Rate", "targets": [{"expr": "sum(rate(http_requests_total[5m])) by (status)", "legendFormat": "{{status}}"}], "yaxes": [{"format": "reqps", "label": "Requests/sec"}, {"format": "short"}]}, {"id": 2, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "type": "graph", "title": "Response Time (95th percentile)", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, route))", "legendFormat": "{{route}}"}], "yaxes": [{"format": "s", "label": "Response Time"}, {"format": "short"}]}, {"id": 3, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}, "type": "stat", "title": "Service Health", "targets": [{"expr": "up{job=\"app\"}", "legendFormat": "Application"}, {"expr": "up{job=\"postgresql\"}", "legendFormat": "Database"}, {"expr": "up{job=\"redis\"}", "legendFormat": "Redis"}], "options": {"graphMode": "none", "colorMode": "background", "orientation": "vertical"}}, {"id": 4, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}, "type": "gauge", "title": "Memory Usage", "targets": [{"expr": "(nodejs_heap_size_used_bytes / nodejs_heap_size_total_bytes) * 100", "legendFormat": "Heap Usage %"}], "options": {"showThresholdLabels": true, "showThresholdMarkers": true}, "fieldConfig": {"defaults": {"min": 0, "max": 100, "unit": "percent", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 85}]}}}}, {"id": 5, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}, "type": "stat", "title": "AI API Usage", "targets": [{"expr": "sum(rate(gemini_api_requests_total[5m])) * 300", "legendFormat": "Requests (5m)"}, {"expr": "sum(increase(gemini_api_errors_total[5m]))", "legendFormat": "Errors (5m)"}], "options": {"graphMode": "area", "colorMode": "value", "orientation": "horizontal"}}, {"id": 6, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "type": "graph", "title": "Database Performance", "targets": [{"expr": "pg_stat_database_xact_commit{datname=\"construction_db\"}", "legendFormat": "Commits/sec"}, {"expr": "pg_stat_database_xact_rollback{datname=\"construction_db\"}", "legendFormat": "Rollbacks/sec"}, {"expr": "pg_stat_database_blks_hit{datname=\"construction_db\"} / (pg_stat_database_blks_hit{datname=\"construction_db\"} + pg_stat_database_blks_read{datname=\"construction_db\"})", "legendFormat": "<PERSON><PERSON>"}]}, {"id": 7, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "type": "table", "title": "Active Jobs", "targets": [{"expr": "takeoff_jobs_by_status", "format": "table", "instant": true}], "options": {"showHeader": true}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}}}}, {"id": 8, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "type": "logs", "title": "Application Errors", "targets": [{"expr": "{job=\"app\"} |= \"error\"", "refId": "A"}], "options": {"showTime": true, "showLabels": true, "showCommonLabels": false, "wrapLogMessage": true, "sortOrder": "Descending", "dedupStrategy": "none"}}]}}