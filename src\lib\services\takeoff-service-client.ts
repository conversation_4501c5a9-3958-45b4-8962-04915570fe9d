// Client-side takeoff service that uses API routes
// This file can be safely imported in client components

import type { TakeoffItem, Estimate, ProcessedDrawing } from '@/types'
import type { CompanyType } from '@/lib/company-types'

// Project context interface
interface ProjectContext {
  id: string
  name: string
  type: 'commercial' | 'residential' | 'industrial' | 'institutional'
  location: {
    address: string
    zipCode: string
    coordinates?: { lat: number; lng: number }
  }
  specifications?: Record<string, any>
  companyType?: CompanyType | null
}

// Generate takeoff items via API
export async function generateTakeoffItems(
  projectContext: ProjectContext,
  drawingFile?: File
): Promise<TakeoffItem[]> {
  try {
    const formData = new FormData()
    formData.append('projectContext', JSON.stringify(projectContext))
    if (drawingFile) {
      formData.append('drawingFile', drawingFile)
      formData.append('fileNames', drawingFile.name)
    }

    console.log('Making request to /api/takeoff with projectContext:', projectContext)
    
    // Ensure we're using the correct base URL
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : ''
    const apiUrl = `${baseUrl}/api/takeoff`
    console.log('Full API URL:', apiUrl)
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      body: formData
    })
    
    console.log('Response status:', response.status)
    console.log('Response headers:', response.headers)

    // Special handling for 404 - might be a routing issue
    if (response.status === 404) {
      console.error('API route not found - this might be a Next.js routing issue')
      throw new Error('API route not found. Please check the server is running.')
    }

    if (!response.ok) {
      // Check if the response is JSON
      const contentType = response.headers.get('content-type')
      console.log('Response content-type:', contentType)
      
      if (contentType && contentType.includes('application/json')) {
        const error = await response.json()
        throw new Error(error.details || error.error || 'Failed to generate takeoff items')
      } else {
        // If not JSON (e.g., HTML error page), log the response text for debugging
        const text = await response.text()
        console.error('Non-JSON response:', text.substring(0, 200))
        throw new Error(`API request failed with status ${response.status}`)
      }
    }

    // Try to parse JSON response
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Expected JSON response but got:', contentType)
      const text = await response.text()
      console.error('Response body:', text.substring(0, 200))
      throw new Error('Invalid response format from API')
    }
    
    const result = await response.json()
    // Check if there's cross-trade info in metadata
    if (result.metadata?.crossTradeInfo) {
      console.log('Cross-trade situation detected:', result.metadata.crossTradeInfo)
      // Store the cross-trade info for the UI to display
      ;(window as any).__crossTradeInfo = result.metadata.crossTradeInfo
    }
    return result.data
  } catch (error) {
    console.error('Error generating takeoff items:', error)
    throw error
  }
}

// Generate takeoff items for multiple files via API
export async function generateTakeoffItemsMultiple(
  projectContext: ProjectContext,
  drawingFiles: File[]
): Promise<TakeoffItem[]> {
  try {
    const formData = new FormData()
    formData.append('projectContext', JSON.stringify(projectContext))
    formData.append('multipleFiles', 'true')
    
    // Add each file with a unique key
    drawingFiles.forEach((file, index) => {
      formData.append(`drawingFile_${index}`, file)
      formData.append('fileNames', file.name)
    })

    console.log('Making request to /api/takeoff with multiple files:', drawingFiles.length)
    
    // Ensure we're using the correct base URL
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : ''
    const apiUrl = `${baseUrl}/api/takeoff`
    console.log('Full API URL:', apiUrl)
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      body: formData
    })
    
    console.log('Response status:', response.status)
    console.log('Response headers:', response.headers)

    if (!response.ok) {
      // Check if the response is JSON
      const contentType = response.headers.get('content-type')
      console.log('Response content-type:', contentType)
      
      if (contentType && contentType.includes('application/json')) {
        const error = await response.json()
        throw new Error(error.details || error.error || 'Failed to generate takeoff items')
      } else {
        // If not JSON (e.g., HTML error page), log the response text for debugging
        const text = await response.text()
        console.error('Non-JSON response:', text.substring(0, 200))
        throw new Error(`API request failed with status ${response.status}`)
      }
    }

    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error generating takeoff items for multiple files:', error)
    throw error
  }
}

// Get AI insights via API
export async function getAIInsights(takeoffItems: TakeoffItem[], companyType?: CompanyType | null): Promise<any[]> {
  try {
    const response = await fetch('/api/takeoff', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        operation: 'insights',
        data: takeoffItems,
        companyType: companyType
      })
    })

    if (!response.ok) {
      const contentType = response.headers.get('content-type')
      
      // Handle 401 Unauthorized specifically
      if (response.status === 401) {
        console.warn('Authentication required - redirecting to login')
        window.location.href = '/login'
        return []
      }
      
      if (contentType && contentType.includes('application/json')) {
        const error = await response.json()
        throw new Error(error.details || error.error || error.message || 'Failed to get AI insights')
      } else {
        // Log the first part of the response for debugging
        const text = await response.text()
        console.error('Non-JSON response:', text.substring(0, 200))
        throw new Error(`API request failed with status ${response.status}`)
      }
    }

    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error getting AI insights:', error)
    // Check if it's an authentication error
    if (error instanceof Error && error.message.includes('Authentication required')) {
      console.warn('User not authenticated - redirecting to login')
      window.location.href = '/login'
    }
    return []
  }
}

// Get category stats via API
export async function getCategoryStats(takeoffItems: TakeoffItem[]): Promise<any[]> {
  try {
    const response = await fetch('/api/takeoff', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        operation: 'categoryStats',
        data: takeoffItems
      })
    })

    if (!response.ok) {
      const contentType = response.headers.get('content-type')
      
      // Handle 401 Unauthorized specifically
      if (response.status === 401) {
        console.warn('Authentication required - redirecting to login')
        window.location.href = '/login'
        return []
      }
      
      if (contentType && contentType.includes('application/json')) {
        const error = await response.json()
        throw new Error(error.details || error.error || error.message || 'Failed to get category stats')
      } else {
        // Log the first part of the response for debugging
        const text = await response.text()
        console.error('Non-JSON response:', text.substring(0, 200))
        throw new Error(`API request failed with status ${response.status}`)
      }
    }

    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error getting category stats:', error)
    // Check if it's an authentication error
    if (error instanceof Error && error.message.includes('Authentication required')) {
      console.warn('User not authenticated - redirecting to login')
      window.location.href = '/login'
    }
    return []
  }
}

// Get cost breakdown via API
export async function getCostBreakdown(takeoffItems: TakeoffItem[]): Promise<any> {
  try {
    const response = await fetch('/api/takeoff', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        operation: 'costBreakdown',
        data: takeoffItems
      })
    })

    if (!response.ok) {
      const contentType = response.headers.get('content-type')
      
      // Handle 401 Unauthorized specifically
      if (response.status === 401) {
        console.warn('Authentication required - redirecting to login')
        window.location.href = '/login'
        return {
          materials: 0,
          labor: 0,
          equipment: 0,
          overhead: 0,
          total: 0
        }
      }
      
      if (contentType && contentType.includes('application/json')) {
        const error = await response.json()
        throw new Error(error.details || error.error || error.message || 'Failed to get cost breakdown')
      } else {
        // Log the first part of the response for debugging
        const text = await response.text()
        console.error('Non-JSON response:', text.substring(0, 200))
        throw new Error(`API request failed with status ${response.status}`)
      }
    }

    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error getting cost breakdown:', error)
    // Check if it's an authentication error
    if (error instanceof Error && error.message.includes('Authentication required')) {
      console.warn('User not authenticated - redirecting to login')
      window.location.href = '/login'
    }
    return {
      materials: 0,
      labor: 0,
      equipment: 0,
      overhead: 0,
      total: 0
    }
  }
}

// Generate estimate via API
export async function generateEstimate(
  takeoffItems: TakeoffItem[],
  projectDetails: any
): Promise<Estimate> {
  try {
    const response = await fetch('/api/takeoff', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        operation: 'estimate',
        data: {
          items: takeoffItems,
          projectDetails
        }
      })
    })

    if (!response.ok) {
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        const error = await response.json()
        throw new Error(error.details || error.error || 'Failed to generate estimate')
      } else {
        throw new Error(`API request failed with status ${response.status}`)
      }
    }

    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error generating estimate:', error)
    throw error
  }
}

// Process drawing via API
export async function processDrawing(
  drawingFile: File,
  companyType?: CompanyType | null
): Promise<{ 
  success: boolean
  projectId: string
  itemsDetected: number
  processingTime: number 
}> {
  try {
    const formData = new FormData()
    formData.append('drawingFile', drawingFile)
    if (companyType) {
      formData.append('companyType', companyType)
    }

    const response = await fetch('/api/takeoff', {
      method: 'PATCH',
      body: formData
    })

    if (!response.ok) {
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        const error = await response.json()
        // Check if it's a PDF-related error and provide helpful message
        if (error.details && error.details.includes('PDF')) {
          console.log('PDF file detected - processing with PDF analyzer')
        }
        throw new Error(error.details || error.error || 'Failed to process drawing')
      } else {
        throw new Error(`API request failed with status ${response.status}`)
      }
    }

    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error processing drawing:', error)
    throw error
  }
}