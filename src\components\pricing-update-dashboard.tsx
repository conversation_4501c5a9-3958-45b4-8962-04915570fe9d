'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>resh<PERSON><PERSON>, CheckCircle, Clock } from 'lucide-react'

interface PricingStatus {
  status: {
    enabled: boolean
    isUpdating: boolean
    config: {
      updateFrequency: string
      inflationRate: number
    }
  }
  estimatedAdjustments: {
    electrical: {
      materials: string
      labor: string
      overall: string
    }
    general: {
      materials: string
      labor: string
      overall: string
    }
    lastManualUpdate: string
    recommendedAction: string
  }
  currentYear: number
  dataYear: number
  needsUpdate: boolean
}

export function PricingUpdateDashboard() {
  const [status, setStatus] = useState<PricingStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isUpdating, setIsUpdating] = useState(false)
  const [lastUpdateResult, setLastUpdateResult] = useState<string | null>(null)

  useEffect(() => {
    fetchStatus()
  }, [])

  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/pricing/update')
      const data = await response.json()
      setStatus(data)
    } catch (error) {
      console.error('Failed to fetch pricing status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const triggerUpdate = async () => {
    setIsUpdating(true)
    setLastUpdateResult(null)
    
    try {
      const response = await fetch('/api/pricing/update', {
        method: 'POST'
      })
      const result = await response.json()
      
      if (result.success) {
        setLastUpdateResult(`✅ ${result.message}`)
        await fetchStatus() // Refresh status
      } else {
        setLastUpdateResult(`❌ Update failed: ${result.message}`)
      }
    } catch (error) {
      setLastUpdateResult(`❌ Update failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsUpdating(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Loading pricing status...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!status) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">Failed to load pricing status</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>Pricing Data Status</span>
            {status.needsUpdate ? (
              <Badge variant="destructive" className="ml-2">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Needs Update
              </Badge>
            ) : (
              <Badge variant="default" className="ml-2">
                <CheckCircle className="h-3 w-3 mr-1" />
                Current
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Current pricing data is from {status.dataYear}. We're now in {status.currentYear}.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <div className="text-sm font-medium text-gray-500">Auto Updates</div>
              <div className="text-lg font-semibold">
                {status.status.enabled ? (
                  <span className="text-green-600">Enabled</span>
                ) : (
                  <span className="text-red-600">Disabled</span>
                )}
              </div>
              <div className="text-xs text-gray-500">
                {status.status.enabled ? `${status.status.config.updateFrequency}` : 'Manual only'}
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium text-gray-500">Update Status</div>
              <div className="text-lg font-semibold">
                {status.status.isUpdating ? (
                  <span className="text-blue-600 flex items-center">
                    <RefreshCw className="h-4 w-4 animate-spin mr-1" />
                    Updating
                  </span>
                ) : (
                  <span className="text-gray-600">Idle</span>
                )}
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium text-gray-500">Inflation Rate</div>
              <div className="text-lg font-semibold">
                {(status.status.config.inflationRate * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Annual estimate</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Price Adjustments */}
      <Card>
        <CardHeader>
          <CardTitle>Estimated 2025 Price Adjustments</CardTitle>
          <CardDescription>
            Based on market trends and inflation since last update ({status.estimatedAdjustments.lastManualUpdate})
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Electrical Contractor */}
            <div className="space-y-3">
              <h4 className="font-semibold text-lg">Electrical Contractor</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Materials:</span>
                  <Badge variant="outline" className="text-orange-600">
                    {status.estimatedAdjustments.electrical.materials}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Labor:</span>
                  <Badge variant="outline" className="text-blue-600">
                    {status.estimatedAdjustments.electrical.labor}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Overall:</span>
                  <Badge variant="outline" className="text-red-600 font-semibold">
                    {status.estimatedAdjustments.electrical.overall}
                  </Badge>
                </div>
              </div>
            </div>

            {/* General Contractor */}
            <div className="space-y-3">
              <h4 className="font-semibold text-lg">General Contractor</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Materials:</span>
                  <Badge variant="outline" className="text-orange-600">
                    {status.estimatedAdjustments.general.materials}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Labor:</span>
                  <Badge variant="outline" className="text-blue-600">
                    {status.estimatedAdjustments.general.labor}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Overall:</span>
                  <Badge variant="outline" className="text-red-600 font-semibold">
                    {status.estimatedAdjustments.general.overall}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Actions</CardTitle>
          <CardDescription>
            {status.estimatedAdjustments.recommendedAction}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button 
              onClick={triggerUpdate}
              disabled={isUpdating || status.status.isUpdating}
              className="w-full md:w-auto"
            >
              {isUpdating ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  Updating Prices...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Update Prices Now
                </>
              )}
            </Button>
            
            {lastUpdateResult && (
              <div className="p-3 bg-gray-50 rounded-md text-sm">
                {lastUpdateResult}
              </div>
            )}
            
            {status.needsUpdate && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <div className="font-medium text-yellow-800">Pricing Data Outdated</div>
                    <div className="text-sm text-yellow-700 mt-1">
                      Your estimates may be off by 8-15% due to 2025 price increases. 
                      Update pricing data to ensure accurate takeoffs.
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
