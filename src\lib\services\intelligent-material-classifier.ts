/**
 * Intelligent Material Classification Service
 * Uses AI and semantic understanding for accurate trade-specific material classification
 */

import type { CompanyType } from '@/lib/company-types'
import type { TakeoffItem, DetectedMaterial } from '@/types'

import { geminiService } from '@/lib/gemini'
import { takeoffLogger } from './logger-wrapper'

// CSI MasterFormat Division mapping for construction materials
export const CSI_DIVISIONS: Record<string, { name: string; trades: string[] }> = {
  '00': { name: 'Procurement and Contracting', trades: ['General Contractor'] },
  '01': { name: 'General Requirements', trades: ['General Contractor'] },
  '02': { name: 'Existing Conditions', trades: ['Demolition Contractor', 'General Contractor'] },
  '03': { name: 'Concrete', trades: ['Concrete Contractor', 'General Contractor'] },
  '04': { name: 'Masonry', trades: ['Masonry Contractor', 'General Contractor'] },
  '05': { name: 'Metals', trades: ['Steel/Metal Contractor', 'General Contractor'] },
  '06': { name: 'Wood, Plastics, and Composites', trades: ['General Contractor'] },
  '07': { name: 'Thermal and Moisture Protection', trades: ['Roofing Contractor', 'Insulation Contractor'] },
  '08': { name: 'Openings', trades: ['General Contractor', 'Glass & Glazing Contractor'] },
  '09': { name: 'Finishes', trades: ['Drywall Contractor', 'Painting Contractor', 'Flooring Contractor'] },
  '10': { name: 'Specialties', trades: ['General Contractor'] },
  '11': { name: 'Equipment', trades: ['General Contractor'] },
  '12': { name: 'Furnishings', trades: ['General Contractor'] },
  '13': { name: 'Special Construction', trades: ['General Contractor'] },
  '14': { name: 'Conveying Equipment', trades: ['Elevator Contractor'] },
  '21': { name: 'Fire Suppression', trades: ['Fire Protection Contractor'] },
  '22': { name: 'Plumbing', trades: ['Plumbing Contractor'] },
  '23': { name: 'Heating, Ventilating, and Air Conditioning', trades: ['HVAC Contractor'] },
  '25': { name: 'Integrated Automation', trades: ['General Contractor'] },
  '26': { name: 'Electrical', trades: ['Electrical Contractor'] },
  '27': { name: 'Communications', trades: ['Electrical Contractor'] },
  '28': { name: 'Electronic Safety and Security', trades: ['Electrical Contractor'] },
  '31': { name: 'Earthwork', trades: ['Excavation/Earthwork Contractor', 'General Contractor'] },
  '32': { name: 'Exterior Improvements', trades: ['Landscaping Contractor', 'General Contractor'] },
  '33': { name: 'Utilities', trades: ['General Contractor'] },
  '34': { name: 'Transportation', trades: ['General Contractor'] },
  '35': { name: 'Waterway and Marine Construction', trades: ['Marine/Underwater Contractor'] },
  '40': { name: 'Process Integration', trades: ['General Contractor'] },
  '41': { name: 'Material Processing Equipment', trades: ['General Contractor'] },
  '42': { name: 'Process Heating, Cooling, and Drying Equipment', trades: ['HVAC Contractor'] },
  '43': { name: 'Process Gas and Liquid Equipment', trades: ['Plumbing Contractor'] },
  '44': { name: 'Pollution Control Equipment', trades: ['General Contractor'] },
  '45': { name: 'Industry-Specific Equipment', trades: ['General Contractor'] },
  '46': { name: 'Water and Wastewater Equipment', trades: ['Plumbing Contractor'] },
  '48': { name: 'Electrical Power Generation', trades: ['Electrical Contractor', 'Solar/Renewable Energy Contractor'] }
} as const

// Material context for intelligent classification
export interface MaterialContext {
  drawingType?: string
  drawingSection?: string
  nearbyMaterials?: string[]
  projectType?: 'commercial' | 'residential' | 'industrial' | 'institutional'
  specifications?: string[]
  pageTitle?: string
}

// Classification result with confidence and reasoning
export interface ClassificationResult {
  csiDivision: string
  csiDivisionName: string
  recommendedTrade: CompanyType
  isValidForTrade: boolean
  confidence: number
  reasoning: string
  alternativeClassification?: {
    csiDivision: string
    trade: CompanyType
    reasoning: string
  }
}

// Material relationship for understanding dependencies
export interface MaterialRelationship {
  material: string
  relatedMaterials: string[]
  requiredTogether: boolean
  tradeAlignment: boolean
  missingRelated?: string[]
}

export class IntelligentMaterialClassifier {
  /**
   * Classify material using AI with semantic understanding
   */
  async classifyMaterial(
    material: string | DetectedMaterial | TakeoffItem,
    companyType: CompanyType,
    context: MaterialContext = {}
  ): Promise<ClassificationResult> {
    const materialDescription = typeof material === 'string' 
      ? material 
      : 'description' in material 
        ? material.description 
        : material.name

    const materialCategory = typeof material === 'object' 
      ? material.category || ('type' in material ? material.type : '') || ''
      : ''

    const prompt = this.buildClassificationPrompt(
      materialDescription || '',
      materialCategory,
      companyType,
      context
    )

    try {
      const response = await geminiService.sendMessage(prompt, 'material-classification', {
        companyType
      })

      // Parse AI response
      const result = this.parseClassificationResponse(response, companyType)
      
      takeoffLogger.debug('Material classification result', {
        material: materialDescription,
        companyType,
        result
      })

      return result
    } catch (error) {
      takeoffLogger.error('Failed to classify material', {
        material: materialDescription,
        error
      })
      
      // Fallback classification
      return this.fallbackClassification(materialDescription || '', companyType)
    }
  }

  /**
   * Build intelligent classification prompt
   */
  private buildClassificationPrompt(
    material: string,
    category: string,
    companyType: CompanyType,
    context: MaterialContext
  ): string {
    const relevantDivisions = this.getRelevantDivisions(companyType)
    
    return `You are an expert construction estimator with deep knowledge of CSI MasterFormat divisions.

TASK: Classify this construction material using semantic understanding and context.

MATERIAL TO CLASSIFY:
- Description: ${material}
- Current Category: ${category || 'Unknown'}
- Analyzing for: ${companyType}

CONTEXT:
${context.drawingType ? `- Drawing Type: ${context.drawingType}` : ''}
${context.drawingSection ? `- Drawing Section: ${context.drawingSection}` : ''}
${context.projectType ? `- Project Type: ${context.projectType}` : ''}
${context.nearbyMaterials?.length ? `- Nearby Materials: ${context.nearbyMaterials.join(', ')}` : ''}

RELEVANT CSI DIVISIONS FOR ${companyType}:
${relevantDivisions.map(d => `- Division ${d.number}: ${d.name}`).join('\n')}

SEMANTIC UNDERSTANDING RULES:
1. Consider functional purpose, not just keywords
2. "Transformer pad" = Concrete work (Div 03), NOT Electrical (Div 26)
3. "Equipment pad" = Concrete work (Div 03), NOT Equipment
4. Materials ending in "pad" that support equipment = Concrete/Structural
5. Understand trade responsibility based on installation, not usage

ANALYZE AND RETURN JSON:
{
  "csiDivision": "XX",
  "csiDivisionName": "Division Name",
  "recommendedTrade": "Trade Name",
  "isValidForTrade": boolean (true if ${companyType} should include this),
  "confidence": 0.0-1.0,
  "reasoning": "Explanation of classification logic",
  "semanticAnalysis": {
    "functionalPurpose": "What this material does",
    "installationTrade": "Who installs it",
    "systemAssociation": "What system it belongs to"
  }
}

CRITICAL: Use semantic understanding. A "transformer pad" is a concrete foundation that supports electrical equipment, so it belongs to Concrete contractors, not Electrical contractors.`
  }

  /**
   * Get relevant CSI divisions for a trade
   */
  private getRelevantDivisions(companyType: CompanyType): Array<{number: string; name: string}> {
    return Object.entries(CSI_DIVISIONS)
      .filter(([_, division]) => division.trades.includes(companyType as any))
      .map(([number, division]) => ({
        number,
        name: division.name
      }))
  }

  /**
   * Parse AI classification response
   */
  private parseClassificationResponse(
    response: string,
    requestingTrade: CompanyType
  ): ClassificationResult {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in response')
      }

      const parsed = JSON.parse(jsonMatch[0])
      
      // Validate and ensure proper classification
      const csiDivision = parsed.csiDivision || '00'
      const divisionInfo = CSI_DIVISIONS[csiDivision as keyof typeof CSI_DIVISIONS]
      
      return {
        csiDivision,
        csiDivisionName: parsed.csiDivisionName || divisionInfo?.name || 'Unknown',
        recommendedTrade: parsed.recommendedTrade || requestingTrade,
        isValidForTrade: parsed.isValidForTrade ?? false,
        confidence: parsed.confidence || 0.5,
        reasoning: parsed.reasoning || 'AI classification'
      }
    } catch (error) {
      takeoffLogger.warn('Failed to parse classification response', { error })
      return this.fallbackClassification('', requestingTrade)
    }
  }

  /**
   * Fallback classification when AI fails
   */
  private fallbackClassification(
    material: string,
    companyType: CompanyType
  ): ClassificationResult {
    // Use basic division mapping as fallback
    const defaultDivisions: Record<CompanyType, string> = {
      'Electrical Contractor': '26',
      'Plumbing Contractor': '22',
      'HVAC Contractor': '23',
      'Concrete Contractor': '03',
      'Steel/Metal Contractor': '05',
      'General Contractor': '01',
      'Masonry Contractor': '04',
      'Roofing Contractor': '07',
      'Painting Contractor': '09',
      'Flooring Contractor': '09',
      'Drywall Contractor': '09',
      'Insulation Contractor': '07',
      'Glass & Glazing Contractor': '08',
      'Fire Protection Contractor': '21',
      'Landscaping Contractor': '32',
      'Demolition Contractor': '02',
      'Excavation/Earthwork Contractor': '31',
      'Elevator Contractor': '14',
      'Solar/Renewable Energy Contractor': '48',
      'Marine/Underwater Contractor': '35'
    }

    const division = defaultDivisions[companyType] || '00'
    const divisionInfo = CSI_DIVISIONS[division as keyof typeof CSI_DIVISIONS]

    return {
      csiDivision: division,
      csiDivisionName: divisionInfo?.name || 'General',
      recommendedTrade: companyType,
      isValidForTrade: true,
      confidence: 0.3,
      reasoning: 'Fallback classification - AI unavailable'
    }
  }

  /**
   * Analyze material relationships for validation
   */
  async analyzeMaterialRelationships(
    materials: TakeoffItem[],
    companyType: CompanyType
  ): Promise<MaterialRelationship[]> {
    const materialNames = materials.map(m => m.description).join(', ')
    
    const prompt = `Analyze these construction materials for ${companyType} and identify relationships:

Materials: ${materialNames}

For each material, identify:
1. Related materials that should be included together
2. Materials that indicate wrong trade scope
3. Missing materials based on typical installations

Return JSON array of relationships:
[{
  "material": "material name",
  "relatedMaterials": ["related1", "related2"],
  "requiredTogether": true/false,
  "tradeAlignment": true/false,
  "missingRelated": ["missing1", "missing2"]
}]`

    try {
      const response = await geminiService.sendMessage(prompt, 'material-relationships', {
        companyType
      })
      
      const jsonMatch = response.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0])
      }
    } catch (error) {
      takeoffLogger.warn('Failed to analyze material relationships', { error })
    }

    return []
  }

  /**
   * Validate material scope for trade
   */
  async validateTradeScope(
    materials: TakeoffItem[],
    companyType: CompanyType,
    context: MaterialContext = {}
  ): Promise<{
    validMaterials: TakeoffItem[]
    invalidMaterials: TakeoffItem[]
    missingMaterials: string[]
    recommendations: string[]
  }> {
    const validMaterials: TakeoffItem[] = []
    const invalidMaterials: TakeoffItem[] = []
    const missingMaterials: string[] = []
    const recommendations: string[] = []

    // Classify each material
    for (const material of materials) {
      const classification = await this.classifyMaterial(material, companyType, context)
      
      if (classification.isValidForTrade && classification.confidence > 0.6) {
        validMaterials.push(material)
      } else {
        invalidMaterials.push({
          ...material,
          notes: classification.reasoning
        })
        
        if (classification.alternativeClassification) {
          recommendations.push(
            `"${material.description}" belongs to ${classification.alternativeClassification.trade} (${classification.reasoning})`
          )
        }
      }
    }

    // Analyze relationships to find missing materials
    const relationships = await this.analyzeMaterialRelationships(validMaterials, companyType)
    relationships.forEach(rel => {
      if (rel.missingRelated) {
        missingMaterials.push(...rel.missingRelated)
      }
    })

    return {
      validMaterials,
      invalidMaterials,
      missingMaterials: Array.from(new Set(missingMaterials)),
      recommendations
    }
  }
}

// Export singleton instance
export const intelligentClassifier = new IntelligentMaterialClassifier()