/**
 * Quantity Validator Service
 * Validates material quantities against industry standards and project size
 */

import type { TakeoffItem } from '@/types'
import type { CompanyType } from '@/lib/company-types'
import { takeoffLogger } from './logger-wrapper'

export interface ProjectSize {
  grossSquareFeet: number
  floors: number
  height: number // in feet
  footprint: number // building footprint in SF
  perimeter: number // building perimeter in LF
  type: 'commercial' | 'residential' | 'industrial' | 'institutional'
}

export interface QuantityValidation {
  isValid: boolean
  confidence: number
  issues: string[]
  suggestions: string[]
  adjustedQuantity?: number
}

// Industry standard ratios for materials per square foot
const MATERIAL_RATIOS: Record<string, Record<string, { min: number; max: number; unit: string }>> = {
  'General Contractor': {
    'concrete_foundation': { min: 0.01, max: 0.05, unit: 'CY/SF' }, // 1-5 CY per 100 SF
    'structural_steel': { min: 5, max: 25, unit: 'LB/SF' }, // 5-25 pounds per SF
    'drywall': { min: 3.5, max: 4.5, unit: 'SF/SF' }, // Wall area is 3.5-4.5x floor area
    'doors': { min: 0.002, max: 0.01, unit: 'EA/SF' }, // 1 door per 100-500 SF
    'windows': { min: 0.15, max: 0.40, unit: 'SF/SF' } // Window area 15-40% of floor area
  },
  'Electrical Contractor': {
    'wire_12awg': { min: 2, max: 8, unit: 'LF/SF' }, // 2-8 LF of #12 wire per SF
    'outlets': { min: 0.01, max: 0.04, unit: 'EA/SF' }, // 1 outlet per 25-100 SF
    'lighting': { min: 0.05, max: 0.15, unit: 'EA/SF' }, // 1 fixture per 7-20 SF
    'panel_capacity': { min: 3, max: 10, unit: 'VA/SF' }, // 3-10 VA per SF
    'conduit': { min: 0.5, max: 2, unit: 'LF/SF' } // 0.5-2 LF conduit per SF
  },
  'Plumbing Contractor': {
    'water_pipe': { min: 0.2, max: 1.0, unit: 'LF/SF' }, // 0.2-1 LF per SF
    'drain_pipe': { min: 0.15, max: 0.8, unit: 'LF/SF' }, // 0.15-0.8 LF per SF
    'fixtures': { min: 0.001, max: 0.01, unit: 'EA/SF' }, // 1 fixture per 100-1000 SF
    'water_heater': { min: 0.00005, max: 0.0005, unit: 'EA/SF' } // 1 per 2000-20000 SF
  },
  'HVAC Contractor': {
    'tonnage': { min: 300, max: 600, unit: 'SF/TON' }, // 300-600 SF per ton cooling
    'ductwork': { min: 0.05, max: 0.2, unit: 'LB/SF' }, // 0.05-0.2 pounds duct per SF
    'diffusers': { min: 0.01, max: 0.04, unit: 'EA/SF' }, // 1 diffuser per 25-100 SF
    'vav_boxes': { min: 0.001, max: 0.01, unit: 'EA/SF' } // 1 VAV per 100-1000 SF
  }
}

// Project size estimation factors
const SIZE_FACTORS = {
  commercial: {
    floorHeight: 12, // feet
    wallToFloorRatio: 3.8, // SF wall per SF floor
    perimeterFactor: 0.5 // Perimeter = sqrt(footprint) * 4 * factor
  },
  residential: {
    floorHeight: 9,
    wallToFloorRatio: 3.2,
    perimeterFactor: 0.6
  },
  industrial: {
    floorHeight: 20,
    wallToFloorRatio: 2.5,
    perimeterFactor: 0.4
  },
  institutional: {
    floorHeight: 14,
    wallToFloorRatio: 3.5,
    perimeterFactor: 0.5
  }
}

export class QuantityValidator {
  /**
   * Detect project size from drawing information or project context
   */
  detectProjectSize(
    projectName: string,
    projectType: 'commercial' | 'residential' | 'industrial' | 'institutional',
    drawingText?: string,
    specifications?: any
  ): ProjectSize {
    let grossSquareFeet = 10000 // Default 10,000 SF
    let floors = 1
    
    // Try to extract from project name
    const sizeMatch = projectName.match(/(\d+)[\s-]*(sf|sqft|square feet)/i)
    if (sizeMatch) {
      grossSquareFeet = parseInt(sizeMatch[1])
    }
    
    // Try to extract from drawing text
    if (drawingText) {
      const textSizeMatch = drawingText.match(/gross.*?(\d+)[\s-]*(sf|sqft|square feet)/i) ||
                           drawingText.match(/total.*?(\d+)[\s-]*(sf|sqft|square feet)/i) ||
                           drawingText.match(/(\d+)[\s-]*(sf|sqft|square feet).*?total/i)
      if (textSizeMatch) {
        grossSquareFeet = parseInt(textSizeMatch[1])
      }
      
      // Extract floor count
      const floorMatch = drawingText.match(/(\d+)[\s-]*(story|stories|floor|floors)/i)
      if (floorMatch) {
        floors = parseInt(floorMatch[1])
      }
    }
    
    // Apply heuristics based on project type
    if (projectType === 'residential' && grossSquareFeet > 50000) {
      // Likely multi-family
      floors = Math.max(floors, 3)
    } else if (projectType === 'commercial' && grossSquareFeet > 100000) {
      // Likely multi-story
      floors = Math.max(floors, Math.ceil(grossSquareFeet / 25000))
    }
    
    // Calculate derived values
    const factors = SIZE_FACTORS[projectType]
    const footprint = grossSquareFeet / floors
    const height = floors * factors.floorHeight
    const perimeter = Math.sqrt(footprint) * 4 * factors.perimeterFactor
    
    const size = {
      grossSquareFeet,
      floors,
      height,
      footprint,
      perimeter,
      type: projectType
    }
    
    takeoffLogger.info('Project size detected', {
      projectName,
      ...size
    })
    
    return size
  }
  
  /**
   * Validate quantities for a specific trade
   */
  validateQuantities(
    items: TakeoffItem[],
    projectSize: ProjectSize,
    companyType: CompanyType
  ): Map<string, QuantityValidation> {
    const validations = new Map<string, QuantityValidation>()
    const ratios = MATERIAL_RATIOS[companyType] || MATERIAL_RATIOS['General Contractor']
    
    items.forEach(item => {
      const validation = this.validateItem(item, projectSize, companyType, ratios)
      validations.set(item.id, validation)
    })
    
    return validations
  }
  
  /**
   * Validate a single item
   */
  private validateItem(
    item: TakeoffItem,
    projectSize: ProjectSize,
    companyType: CompanyType,
    ratios: Record<string, { min: number; max: number; unit: string }>
  ): QuantityValidation {
    const validation: QuantityValidation = {
      isValid: true,
      confidence: 1.0,
      issues: [],
      suggestions: []
    }
    
    // Find applicable ratio
    const itemKey = this.findRatioKey(item, Object.keys(ratios))
    if (!itemKey) {
      // No specific validation rule, apply general checks
      return this.generalValidation(item, projectSize, companyType)
    }
    
    const ratio = ratios[itemKey]
    const expectedMin = this.calculateExpectedQuantity(projectSize, ratio.min, ratio.unit, item.unit)
    const expectedMax = this.calculateExpectedQuantity(projectSize, ratio.max, ratio.unit, item.unit)
    
    // Check if quantity is within expected range
    if (item.quantity < expectedMin) {
      validation.isValid = false
      validation.confidence *= 0.7
      validation.issues.push(
        `Quantity (${item.quantity} ${item.unit}) is below expected minimum (${expectedMin} ${item.unit})`
      )
      validation.suggestions.push(
        `Consider increasing to at least ${Math.round(expectedMin)} ${item.unit} based on ${projectSize.grossSquareFeet} SF project`
      )
      validation.adjustedQuantity = Math.round(expectedMin * 1.1) // Add 10% buffer
    } else if (item.quantity > expectedMax) {
      validation.isValid = false
      validation.confidence *= 0.8
      validation.issues.push(
        `Quantity (${item.quantity} ${item.unit}) exceeds expected maximum (${expectedMax} ${item.unit})`
      )
      validation.suggestions.push(
        `Consider reducing to no more than ${Math.round(expectedMax)} ${item.unit} based on ${projectSize.grossSquareFeet} SF project`
      )
      validation.adjustedQuantity = Math.round(expectedMax * 0.9) // Reduce by 10%
    }
    
    // Additional trade-specific validations
    this.applyTradeSpecificValidations(item, projectSize, companyType, validation)
    
    return validation
  }
  
  /**
   * Find matching ratio key for an item
   */
  private findRatioKey(item: TakeoffItem, ratioKeys: string[]): string | null {
    const itemDesc = item.description.toLowerCase()
    const itemCat = item.category.toLowerCase()
    
    for (const key of ratioKeys) {
      const keyParts = key.split('_')
      if (keyParts.every(part => itemDesc.includes(part) || itemCat.includes(part))) {
        return key
      }
    }
    
    return null
  }
  
  /**
   * Calculate expected quantity based on ratio and project size
   */
  private calculateExpectedQuantity(
    projectSize: ProjectSize,
    ratio: number,
    ratioUnit: string,
    targetUnit: string
  ): number {
    let baseQuantity = 0
    
    // Parse ratio unit (e.g., "CY/SF", "EA/SF", "LF/SF")
    if (ratioUnit.includes('/SF')) {
      baseQuantity = projectSize.grossSquareFeet * ratio
    } else if (ratioUnit === 'SF/TON') {
      // Special case for HVAC tonnage
      baseQuantity = projectSize.grossSquareFeet / ratio
    }
    
    // Convert units if needed
    return this.convertUnits(baseQuantity, ratioUnit.split('/')[0], targetUnit)
  }
  
  /**
   * Convert between units
   */
  private convertUnits(quantity: number, fromUnit: string, toUnit: string): number {
    // Simple unit conversion - expand as needed
    if (fromUnit === toUnit) return quantity
    
    // Add conversion logic here
    const conversions: Record<string, Record<string, number>> = {
      'CY': { 'CF': 27 }, // 1 cubic yard = 27 cubic feet
      'CF': { 'CY': 1/27 },
      'LF': { 'FT': 1 }, // Linear feet = feet
      'FT': { 'LF': 1 },
      'SF': { 'SY': 1/9 }, // 1 square yard = 9 square feet
      'SY': { 'SF': 9 }
    }
    
    if (conversions[fromUnit]?.[toUnit]) {
      return quantity * conversions[fromUnit][toUnit]
    }
    
    return quantity // Return unchanged if no conversion available
  }
  
  /**
   * General validation for items without specific ratios
   */
  private generalValidation(
    item: TakeoffItem,
    projectSize: ProjectSize,
    companyType: CompanyType
  ): QuantityValidation {
    const validation: QuantityValidation = {
      isValid: true,
      confidence: 0.9,
      issues: [],
      suggestions: []
    }
    
    // Check for unreasonably large quantities
    const maxReasonable = projectSize.grossSquareFeet * 10 // Arbitrary max
    if (item.quantity > maxReasonable && item.unit !== 'LS') {
      validation.isValid = false
      validation.confidence *= 0.6
      validation.issues.push(
        `Quantity seems excessive for a ${projectSize.grossSquareFeet} SF project`
      )
    }
    
    // Check for zero or negative quantities
    if (item.quantity <= 0) {
      validation.isValid = false
      validation.confidence = 0
      validation.issues.push('Quantity must be greater than zero')
      validation.adjustedQuantity = 1
    }
    
    return validation
  }
  
  /**
   * Apply trade-specific validation rules
   */
  private applyTradeSpecificValidations(
    item: TakeoffItem,
    projectSize: ProjectSize,
    companyType: CompanyType,
    validation: QuantityValidation
  ): void {
    switch (companyType) {
      case 'Electrical Contractor':
        // Check panel sizing
        if (item.description.toLowerCase().includes('panel')) {
          const ampMatch = item.description.match(/(\d+)\s*[Aa]/);
          if (ampMatch) {
            const amps = parseInt(ampMatch[1])
            const requiredVA = projectSize.grossSquareFeet * 5 // 5 VA/SF typical
            const requiredAmps = requiredVA / 208 / 1.732 // 208V 3-phase
            
            if (amps < requiredAmps * 0.8) {
              validation.issues.push(`Panel size may be undersized for ${projectSize.grossSquareFeet} SF`)
              validation.suggestions.push(`Consider ${Math.ceil(requiredAmps / 100) * 100}A panel`)
            }
          }
        }
        break
        
      case 'HVAC Contractor':
        // Check tonnage
        if (item.description.toLowerCase().includes('ton') && item.description.match(/\d+/)) {
          const tons = parseInt(item.description.match(/(\d+)/)![1])
          const requiredTons = projectSize.grossSquareFeet / 400 // 400 SF/ton typical
          
          if (Math.abs(tons - requiredTons) / requiredTons > 0.3) {
            validation.issues.push(`HVAC tonnage may not match building load`)
            validation.suggestions.push(`Typical requirement: ${Math.round(requiredTons)} tons`)
          }
        }
        break
        
      case 'Plumbing Contractor':
        // Check fixture counts
        if (item.category === 'Fixtures') {
          const occupancyLoad = projectSize.grossSquareFeet / 150 // 150 SF/person typical
          const requiredFixtures = Math.ceil(occupancyLoad / 50) // 1 fixture per 50 people
          
          if (item.quantity < requiredFixtures) {
            validation.issues.push(`Fixture count may not meet code requirements`)
            validation.suggestions.push(`Minimum ${requiredFixtures} fixtures for occupancy`)
          }
        }
        break
    }
  }
  
  /**
   * Get confidence score for entire takeoff
   */
  calculateOverallConfidence(
    validations: Map<string, QuantityValidation>
  ): number {
    if (validations.size === 0) return 0
    
    let totalConfidence = 0
    let validCount = 0
    
    validations.forEach(validation => {
      totalConfidence += validation.confidence
      if (validation.isValid) validCount++
    })
    
    const avgConfidence = totalConfidence / validations.size
    const validRatio = validCount / validations.size
    
    return avgConfidence * validRatio
  }
  
  /**
   * Suggest quantity adjustments
   */
  suggestAdjustments(
    items: TakeoffItem[],
    validations: Map<string, QuantityValidation>
  ): TakeoffItem[] {
    return items.map(item => {
      const validation = validations.get(item.id)
      if (validation?.adjustedQuantity && !validation.isValid) {
        return {
          ...item,
          quantity: validation.adjustedQuantity,
          confidence: validation.confidence,
          notes: validation.suggestions.join('; ')
        }
      }
      return item
    })
  }
}

// Export singleton instance
export const quantityValidator = new QuantityValidator()