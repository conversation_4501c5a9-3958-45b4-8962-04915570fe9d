'use client'

import { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ChevronRight, 
  ChevronDown, 
  Eye, 
  EyeOff, 
  Package, 
  Box,
  Home,
  Layers,
  Search,
  Filter
} from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { BIMElement } from '@/types'
import { ElementFilter } from '../types/bim.types'
import { cn } from '@/lib/utils'

interface ModelTreeProps {
  elements: BIMElement[]
  selectedElements: string[]
  hiddenElements: string[]
  onElementSelect: (elementId: string, multiSelect?: boolean) => void
  onElementVisibility: (elementId: string, visible: boolean) => void
  onBatchVisibility?: (elementIds: string[], visible: boolean) => void
  filter?: ElementFilter
  onFilterChange?: (filter: ElementFilter) => void
}

interface TreeNodeProps {
  element: BIMElement
  elements: BIMElement[]
  level: number
  selectedElements: string[]
  hiddenElements: string[]
  onElementSelect: (elementId: string, multiSelect?: boolean) => void
  onElementVisibility: (elementId: string, visible: boolean) => void
  searchTerm: string
}

const TreeNode: React.FC<TreeNodeProps> = ({
  element,
  elements,
  level,
  selectedElements,
  hiddenElements,
  onElementSelect,
  onElementVisibility,
  searchTerm,
}) => {
  const [expanded, setExpanded] = useState(level < 2)
  const children = elements.filter(e => e.parent === element.id)
  const hasChildren = children.length > 0
  const isSelected = selectedElements.includes(element.id)
  const isHidden = hiddenElements.includes(element.id)
  
  // Check if element matches search
  const matchesSearch = !searchTerm || 
    element.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    element.type.toLowerCase().includes(searchTerm.toLowerCase())

  // Don't render if doesn't match search and has no matching children
  if (!matchesSearch && !children.some(child => 
    child.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    child.type.toLowerCase().includes(searchTerm.toLowerCase())
  )) {
    return null
  }

  const getIcon = () => {
    switch (element.ifcType?.toLowerCase()) {
      case 'ifcbuilding':
        return <Home className="h-4 w-4" />
      case 'ifcbuildingstorey':
        return <Layers className="h-4 w-4" />
      case 'ifcspace':
        return <Box className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  return (
    <div className="select-none">
      <div
        className={cn(
          "flex items-center gap-1 px-2 py-1 hover:bg-gray-100 cursor-pointer rounded",
          isSelected && "bg-primary/10 hover:bg-primary/20",
          isHidden && "opacity-50"
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
      >
        {/* Expand/Collapse */}
        <button
          onClick={(e) => {
            e.stopPropagation()
            setExpanded(!expanded)
          }}
          className={cn(
            "p-0.5 hover:bg-gray-200 rounded",
            !hasChildren && "invisible"
          )}
        >
          {expanded ? (
            <ChevronDown className="h-3 w-3" />
          ) : (
            <ChevronRight className="h-3 w-3" />
          )}
        </button>

        {/* Checkbox */}
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onElementSelect(element.id)}
          onClick={(e: React.MouseEvent) => e.stopPropagation()}
          className="h-3.5 w-3.5"
        />

        {/* Icon */}
        <div className="text-gray-600">
          {getIcon()}
        </div>

        {/* Name */}
        <span
          className="flex-1 text-sm truncate"
          onClick={() => onElementSelect(element.id)}
        >
          {element.name}
        </span>

        {/* Visibility toggle */}
        <button
          onClick={(e) => {
            e.stopPropagation()
            onElementVisibility(element.id, isHidden)
          }}
          className="p-1 hover:bg-gray-200 rounded"
        >
          {isHidden ? (
            <EyeOff className="h-3.5 w-3.5 text-gray-400" />
          ) : (
            <Eye className="h-3.5 w-3.5 text-gray-600" />
          )}
        </button>
      </div>

      {/* Children */}
      <AnimatePresence>
        {expanded && hasChildren && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {children.map(child => (
              <TreeNode
                key={child.id}
                element={child}
                elements={elements}
                level={level + 1}
                selectedElements={selectedElements}
                hiddenElements={hiddenElements}
                onElementSelect={onElementSelect}
                onElementVisibility={onElementVisibility}
                searchTerm={searchTerm}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default function ModelTree({
  elements,
  selectedElements,
  hiddenElements,
  onElementSelect,
  onElementVisibility,
  onBatchVisibility,
  filter = {},
  onFilterChange,
}: ModelTreeProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilter, setShowFilter] = useState(false)

  // Get root elements (no parent)
  const rootElements = useMemo(() => {
    return elements.filter(e => !e.parent)
  }, [elements])

  // Get unique IFC types for filtering
  const ifcTypes = useMemo(() => {
    const types = new Set<string>()
    elements.forEach(e => {
      if (e.ifcType) types.add(e.ifcType)
    })
    return Array.from(types).sort()
  }, [elements])

  const handleSelectAll = () => {
    if (selectedElements.length === elements.length) {
      // Deselect all
      elements.forEach(e => onElementSelect(e.id, true))
    } else {
      // Select all visible
      const visibleElements = elements.filter(e => !hiddenElements.includes(e.id))
      visibleElements.forEach(e => {
        if (!selectedElements.includes(e.id)) {
          onElementSelect(e.id, true)
        }
      })
    }
  }

  const handleShowAll = () => {
    if (onBatchVisibility) {
      onBatchVisibility(hiddenElements, true)
    } else {
      hiddenElements.forEach(id => onElementVisibility(id, true))
    }
  }

  const handleHideAll = () => {
    if (onBatchVisibility) {
      const allIds = elements.map(e => e.id)
      onBatchVisibility(allIds, false)
    } else {
      elements.forEach(e => onElementVisibility(e.id, false))
    }
  }

  return (
    <div className="flex flex-col h-full bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="p-4 border-b">
        <h3 className="text-sm font-semibold mb-3">Model Structure</h3>
        
        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search elements..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9 h-8 text-sm"
          />
          <Button
            size="sm"
            variant="ghost"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
            onClick={() => setShowFilter(!showFilter)}
          >
            <Filter className="h-3.5 w-3.5" />
          </Button>
        </div>

        {/* Quick actions */}
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            className="flex-1 h-7 text-xs"
            onClick={handleSelectAll}
          >
            {selectedElements.length === elements.length ? 'Deselect All' : 'Select All'}
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="h-7 px-2"
            onClick={handleShowAll}
          >
            <Eye className="h-3.5 w-3.5" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="h-7 px-2"
            onClick={handleHideAll}
          >
            <EyeOff className="h-3.5 w-3.5" />
          </Button>
        </div>
      </div>

      {/* Filter panel */}
      <AnimatePresence>
        {showFilter && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            className="border-b overflow-hidden"
          >
            <div className="p-4 space-y-3">
              <div>
                <label className="text-xs font-medium text-gray-600">Element Type</label>
                <select
                  className="w-full mt-1 text-sm border rounded px-2 py-1"
                  value={filter.ifcType?.[0] || ''}
                  onChange={(e) => onFilterChange?.({
                    ...filter,
                    ifcType: e.target.value ? [e.target.value] : undefined
                  })}
                >
                  <option value="">All Types</option>
                  {ifcTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Tree */}
      <div className="flex-1 overflow-y-auto p-2">
        {rootElements.length === 0 ? (
          <div className="text-center text-gray-500 text-sm py-8">
            No model loaded
          </div>
        ) : (
          rootElements.map(element => (
            <TreeNode
              key={element.id}
              element={element}
              elements={elements}
              level={0}
              selectedElements={selectedElements}
              hiddenElements={hiddenElements}
              onElementSelect={onElementSelect}
              onElementVisibility={onElementVisibility}
              searchTerm={searchTerm}
            />
          ))
        )}
      </div>

      {/* Footer */}
      <div className="p-3 border-t text-xs text-gray-600">
        {elements.length} elements • {selectedElements.length} selected
      </div>
    </div>
  )
}