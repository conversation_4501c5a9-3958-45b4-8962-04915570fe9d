import prisma from '@/lib/prisma'
import { getCurrentUser } from './auth'
import { Takeoff, TakeoffItem } from '@prisma/client'

export interface CreateTakeoffInput {
  projectId: string
  drawingUrl?: string
  items: CreateTakeoffItemInput[]
  totalCost: number
  confidence?: number
}

export interface CreateTakeoffItemInput {
  name: string
  description?: string
  quantity: number
  unit: string
  unitCost: number
  totalCost: number
  category: string
  materialType?: string
  supplier?: string
  leadTime?: number
  confidence?: number
  source?: string
}

// Create a new takeoff
export async function createTakeoff(data: CreateTakeoffInput): Promise<Takeoff & { items: TakeoffItem[] }> {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  // Verify project ownership
  const project = await prisma.project.findFirst({
    where: {
      id: data.projectId,
      userId: user.id,
    },
  })

  if (!project) {
    throw new Error('Project not found')
  }

  // Create takeoff with items
  const takeoff = await prisma.takeoff.create({
    data: {
      projectId: data.projectId,
      userId: user.id,
      drawingUrl: data.drawingUrl,
      totalCost: data.totalCost,
      confidence: data.confidence,
      status: 'completed',
      items: {
        create: data.items,
      },
    },
    include: {
      items: true,
    },
  })

  // Log activity
  await prisma.activity.create({
    data: {
      userId: user.id,
      projectId: data.projectId,
      action: 'TAKEOFF_CREATED',
      description: `Created takeoff with ${data.items.length} items`,
      metadata: JSON.stringify({
        takeoffId: takeoff.id,
        totalCost: data.totalCost,
        itemCount: data.items.length,
      }),
    },
  })

  return takeoff
}

// Get takeoffs for a project
export async function getProjectTakeoffs(projectId: string) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  // Verify project ownership
  const project = await prisma.project.findFirst({
    where: {
      id: projectId,
      userId: user.id,
    },
  })

  if (!project) {
    throw new Error('Project not found')
  }

  const takeoffs = await prisma.takeoff.findMany({
    where: {
      projectId,
      userId: user.id,
    },
    orderBy: { createdAt: 'desc' },
    include: {
      items: {
        orderBy: { totalCost: 'desc' },
      },
      _count: {
        select: { items: true },
      },
    },
  })

  return takeoffs
}

// Get single takeoff with details
export async function getTakeoff(takeoffId: string) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  const takeoff = await prisma.takeoff.findFirst({
    where: {
      id: takeoffId,
      userId: user.id,
    },
    include: {
      items: {
        orderBy: { totalCost: 'desc' },
      },
      project: {
        select: {
          id: true,
          name: true,
          location: true,
          projectType: true,
        },
      },
    },
  })

  if (!takeoff) {
    throw new Error('Takeoff not found')
  }

  return takeoff
}

// Update takeoff status
export async function updateTakeoffStatus(takeoffId: string, status: string) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  const takeoff = await prisma.takeoff.update({
    where: {
      id: takeoffId,
      userId: user.id,
    },
    data: { status },
  })

  return takeoff
}

// Add items to existing takeoff
export async function addTakeoffItems(takeoffId: string, items: CreateTakeoffItemInput[]) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  // Verify ownership
  const takeoff = await prisma.takeoff.findFirst({
    where: {
      id: takeoffId,
      userId: user.id,
    },
  })

  if (!takeoff) {
    throw new Error('Takeoff not found')
  }

  // Add items
  const createdItems = await prisma.takeoffItem.createMany({
    data: items.map(item => ({
      ...item,
      takeoffId,
    })),
  })

  // Update total cost
  const allItems = await prisma.takeoffItem.findMany({
    where: { takeoffId },
  })

  const newTotalCost = allItems.reduce((sum, item) => sum + item.totalCost, 0)

  await prisma.takeoff.update({
    where: { id: takeoffId },
    data: { totalCost: newTotalCost },
  })

  return createdItems
}

// Update takeoff item
export async function updateTakeoffItem(itemId: string, data: Partial<CreateTakeoffItemInput>) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  // Verify ownership through takeoff
  const item = await prisma.takeoffItem.findFirst({
    where: { id: itemId },
    include: {
      takeoff: {
        select: { userId: true, id: true },
      },
    },
  })

  if (!item || item.takeoff.userId !== user.id) {
    throw new Error('Item not found')
  }

  // Update item
  const updatedItem = await prisma.takeoffItem.update({
    where: { id: itemId },
    data,
  })

  // Update takeoff total
  const allItems = await prisma.takeoffItem.findMany({
    where: { takeoffId: item.takeoff.id },
  })

  const newTotalCost = allItems.reduce((sum, item) => sum + item.totalCost, 0)

  await prisma.takeoff.update({
    where: { id: item.takeoff.id },
    data: { totalCost: newTotalCost },
  })

  return updatedItem
}

// Delete takeoff item
export async function deleteTakeoffItem(itemId: string) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  // Verify ownership through takeoff
  const item = await prisma.takeoffItem.findFirst({
    where: { id: itemId },
    include: {
      takeoff: {
        select: { userId: true, id: true },
      },
    },
  })

  if (!item || item.takeoff.userId !== user.id) {
    throw new Error('Item not found')
  }

  // Delete item
  await prisma.takeoffItem.delete({
    where: { id: itemId },
  })

  // Update takeoff total
  const allItems = await prisma.takeoffItem.findMany({
    where: { takeoffId: item.takeoff.id },
  })

  const newTotalCost = allItems.reduce((sum, item) => sum + item.totalCost, 0)

  await prisma.takeoff.update({
    where: { id: item.takeoff.id },
    data: { totalCost: newTotalCost },
  })
}

// Get takeoff statistics for a project
export async function getProjectTakeoffStats(projectId: string) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  const takeoffs = await prisma.takeoff.findMany({
    where: {
      projectId,
      userId: user.id,
    },
    include: {
      items: true,
    },
  })

  const stats = {
    totalTakeoffs: takeoffs.length,
    totalEstimatedCost: takeoffs.reduce((sum, t) => sum + (t.totalCost || 0), 0),
    avgConfidence: takeoffs.length > 0
      ? takeoffs.reduce((sum, t) => sum + (t.confidence || 0), 0) / takeoffs.length
      : 0,
    itemsByCategory: {} as Record<string, number>,
    topSuppliers: {} as Record<string, number>,
  }

  // Aggregate items by category and supplier
  takeoffs.forEach(takeoff => {
    takeoff.items.forEach(item => {
      // Category aggregation
      if (!stats.itemsByCategory[item.category]) {
        stats.itemsByCategory[item.category] = 0
      }
      stats.itemsByCategory[item.category] += item.totalCost

      // Supplier aggregation
      if (item.supplier) {
        if (!stats.topSuppliers[item.supplier]) {
          stats.topSuppliers[item.supplier] = 0
        }
        stats.topSuppliers[item.supplier] += item.totalCost
      }
    })
  })

  return stats
}