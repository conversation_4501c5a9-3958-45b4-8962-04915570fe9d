// 3D Annotation marker component for BIM viewer

'use client'

import React, { useState, useRef } from 'react'
import { Html } from '@react-three/drei'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'
import { Annotation } from '../types'
import { 
  MessageSquare, 
  AlertCircle, 
  HelpCircle, 
  Ruler,
  X,
  Check,
  MessageCircle,
  Send
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'

interface AnnotationMarkerProps {
  annotation: Annotation
  onUpdate: (id: string, updates: Partial<Annotation>) => void
  onDelete: (id: string) => void
  onReply: (annotationId: string, content: string) => void
  isSelected: boolean
  onSelect: (annotation: Annotation | null) => void
}

export function AnnotationMarker({
  annotation,
  onUpdate,
  onDelete,
  onReply,
  isSelected,
  onSelect
}: AnnotationMarkerProps) {
  const [showDetails, setShowDetails] = useState(false)
  const [replyText, setReplyText] = useState('')
  const meshRef = useRef<THREE.Mesh>(null)

  // Pulse animation for open annotations
  useFrame((state) => {
    if (meshRef.current && annotation.status === 'open') {
      const scale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.1
      meshRef.current.scale.setScalar(scale)
    }
  })

  const getIcon = () => {
    switch (annotation.type) {
      case 'comment': return MessageSquare
      case 'issue': return AlertCircle
      case 'rfi': return HelpCircle
      case 'dimension': return Ruler
      default: return MessageSquare
    }
  }

  const getColor = () => {
    switch (annotation.status) {
      case 'open': return '#ef4444' // red
      case 'resolved': return '#10b981' // green
      case 'closed': return '#6b7280' // gray
      default: return '#3b82f6' // blue
    }
  }

  const Icon = getIcon()
  const color = getColor()

  const handleReply = () => {
    if (replyText.trim()) {
      onReply(annotation.id, replyText)
      setReplyText('')
    }
  }

  return (
    <group position={[annotation.position.x, annotation.position.y, annotation.position.z]}>
      {/* 3D Marker */}
      <mesh
        ref={meshRef}
        onClick={(e) => {
          e.stopPropagation()
          setShowDetails(!showDetails)
          onSelect(isSelected ? null : annotation)
        }}
      >
        <sphereGeometry args={[0.2, 16, 16]} />
        <meshStandardMaterial 
          color={color} 
          emissive={color}
          emissiveIntensity={0.5}
          opacity={0.9}
          transparent
        />
      </mesh>

      {/* HTML Overlay */}
      <Html
        center
        distanceFactor={10}
        style={{
          transition: 'all 0.2s',
          opacity: showDetails || isSelected ? 1 : 0,
          pointerEvents: showDetails || isSelected ? 'auto' : 'none',
        }}
      >
        <Card className="w-80 p-4 bg-white/95 dark:bg-gray-900/95 backdrop-blur shadow-xl">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <Icon className="h-5 w-5" style={{ color }} />
              <span className="font-medium capitalize">{annotation.type}</span>
              <Badge 
                variant={annotation.status === 'open' ? 'destructive' : 'secondary'}
                className="text-xs"
              >
                {annotation.status}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                setShowDetails(false)
                onSelect(null)
              }}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Content */}
          <div className="mb-3">
            <p className="text-sm">{annotation.content}</p>
            <p className="text-xs text-gray-500 mt-1">
              by {annotation.userName} • {new Date(annotation.createdAt).toLocaleDateString()}
            </p>
          </div>

          {/* Replies */}
          {annotation.replies && annotation.replies.length > 0 && (
            <div className="mb-3 space-y-2 max-h-32 overflow-y-auto">
              {annotation.replies.map(reply => (
                <div key={reply.id} className="text-xs bg-gray-50 dark:bg-gray-800 p-2 rounded">
                  <p>{reply.content}</p>
                  <p className="text-gray-500 mt-1">
                    {reply.userName} • {new Date(reply.createdAt).toLocaleDateString()}
                  </p>
                </div>
              ))}
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2">
            {annotation.status === 'open' && (
              <>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onUpdate(annotation.id, { status: 'resolved' })}
                  className="flex-1"
                >
                  <Check className="h-3 w-3 mr-1" />
                  Resolve
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => onDelete(annotation.id)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </>
            )}
            {annotation.status === 'resolved' && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onUpdate(annotation.id, { status: 'closed' })}
                className="flex-1"
              >
                Close
              </Button>
            )}
          </div>

          {/* Reply Input */}
          {annotation.status !== 'closed' && (
            <div className="mt-3 flex gap-2">
              <Input
                placeholder="Add a reply..."
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleReply()}
                className="text-sm h-8"
              />
              <Button
                size="sm"
                onClick={handleReply}
                disabled={!replyText.trim()}
                className="h-8 w-8 p-0"
              >
                <Send className="h-3 w-3" />
              </Button>
            </div>
          )}
        </Card>
      </Html>
    </group>
  )
}

interface AnnotationsContainerProps {
  annotations: Annotation[]
  onUpdate: (id: string, updates: Partial<Annotation>) => void
  onDelete: (id: string) => void
  onReply: (annotationId: string, content: string) => void
  selectedAnnotation: Annotation | null
  onSelectAnnotation: (annotation: Annotation | null) => void
}

export function AnnotationsContainer({
  annotations,
  onUpdate,
  onDelete,
  onReply,
  selectedAnnotation,
  onSelectAnnotation
}: AnnotationsContainerProps) {
  return (
    <>
      {annotations.map(annotation => (
        <AnnotationMarker
          key={annotation.id}
          annotation={annotation}
          onUpdate={onUpdate}
          onDelete={onDelete}
          onReply={onReply}
          isSelected={selectedAnnotation?.id === annotation.id}
          onSelect={onSelectAnnotation}
        />
      ))}
    </>
  )
}