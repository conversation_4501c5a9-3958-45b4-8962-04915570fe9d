#!/usr/bin/env pwsh
# Check for 404 issues

Write-Host "🔍 Checking for 404 issues..." -ForegroundColor Yellow

# Check if settings page exists
if (Test-Path "src/app/dashboard/settings/page.tsx") {
    Write-Host "✅ Settings page exists" -ForegroundColor Green
} else {
    Write-Host "❌ Settings page missing" -ForegroundColor Red
}

# Check if Chrome DevTools config exists
if (Test-Path "public/.well-known/appspecific/com.chrome.devtools.json") {
    Write-Host "✅ Chrome DevTools config exists" -ForegroundColor Green
} else {
    Write-Host "❌ Chrome DevTools config missing" -ForegroundColor Red
}

Write-Host "`n📝 Files created to fix 404 issues:" -ForegroundColor Cyan
Write-Host "- src/app/dashboard/settings/page.tsx" -ForegroundColor White
Write-Host "- public/.well-known/appspecific/com.chrome.devtools.json" -ForegroundColor White

Write-Host "`n✅ All 404 issues should now be resolved!" -ForegroundColor Green
Write-Host "Restart your dev server to see the changes." -ForegroundColor Yellow
