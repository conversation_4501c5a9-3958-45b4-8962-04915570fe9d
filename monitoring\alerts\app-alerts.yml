groups:
  - name: application
    interval: 30s
    rules:
      # High Error Rate
      - alert: HighErrorRate
        expr: |
          (
            sum(rate(http_requests_total{status=~"5.."}[5m]))
            /
            sum(rate(http_requests_total[5m]))
          ) > 0.05
        for: 5m
        labels:
          severity: critical
          service: ai-construction-app
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      # High Response Time
      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95, 
            sum(rate(http_request_duration_seconds_bucket[5m])) by (le)
          ) > 2
        for: 5m
        labels:
          severity: warning
          service: ai-construction-app
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value | humanizeDuration }}"

      # Service Down
      - alert: ServiceDown
        expr: up{job="app"} == 0
        for: 2m
        labels:
          severity: critical
          service: ai-construction-app
        annotations:
          summary: "Application service is down"
          description: "The application has been unreachable for 2 minutes"

      # High Memory Usage
      - alert: HighMemoryUsage
        expr: |
          (
            nodejs_heap_size_used_bytes{job="app"}
            /
            nodejs_heap_size_total_bytes{job="app"}
          ) > 0.85
        for: 5m
        labels:
          severity: warning
          service: ai-construction-app
        annotations:
          summary: "High memory usage detected"
          description: "Heap usage is {{ $value | humanizePercentage }}"

      # Database Connection Pool Exhausted
      - alert: DatabaseConnectionPoolExhausted
        expr: |
          (
            prisma_pool_connections_open{job="app"}
            /
            prisma_pool_connections_limit{job="app"}
          ) > 0.9
        for: 5m
        labels:
          severity: warning
          service: ai-construction-app
        annotations:
          summary: "Database connection pool nearly exhausted"
          description: "{{ $value | humanizePercentage }} of connections are in use"

      # AI API Rate Limit Approaching
      - alert: AIAPIRateLimitApproaching
        expr: |
          (
            sum(rate(gemini_api_requests_total[5m]))
            /
            gemini_api_rate_limit
          ) > 0.8
        for: 5m
        labels:
          severity: warning
          service: ai-construction-app
        annotations:
          summary: "Approaching Gemini API rate limit"
          description: "Currently at {{ $value | humanizePercentage }} of rate limit"

      # Job Queue Backlog
      - alert: JobQueueBacklog
        expr: |
          (
            takeoff_jobs_pending{job="app"}
            -
            takeoff_jobs_processing{job="app"}
          ) > 100
        for: 10m
        labels:
          severity: warning
          service: ai-construction-app
        annotations:
          summary: "Large job queue backlog"
          description: "{{ $value }} jobs are waiting to be processed"

      # Socket.io Connection Spike
      - alert: WebSocketConnectionSpike
        expr: |
          rate(socketio_connections_total[5m]) > 100
        for: 2m
        labels:
          severity: info
          service: ai-construction-app
        annotations:
          summary: "Spike in WebSocket connections"
          description: "{{ $value }} new connections per second"