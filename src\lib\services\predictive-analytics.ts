/**
 * Predictive Analytics Service for Construction Projects
 * Uses historical data and ML models to predict costs, timelines, and risks
 */

import { takeoffLogger, logProcessingStep, logError } from './logger-wrapper'
import { geminiService } from '@/lib/gemini'
import type { TakeoffItem, Project, ProjectType } from '@/types'
import type { CompanyType } from '@/lib/company-types'

// Prediction types
export interface CostPrediction {
  estimatedCost: number
  confidenceInterval: {
    low: number
    high: number
  }
  confidence: number
  factors: CostFactor[]
  recommendations: string[]
  historicalComparison: {
    similarProjects: number
    averageCost: number
    percentile: number
  }
}

export interface CostFactor {
  name: string
  impact: number // percentage impact on cost
  direction: 'increase' | 'decrease'
  category: 'market' | 'location' | 'complexity' | 'seasonal' | 'risk'
}

export interface TimePrediction {
  estimatedDuration: number // days
  criticalPath: string[]
  riskFactors: TimeRiskFactor[]
  weatherImpact: number // days
  confidenceLevel: number
  milestones: Milestone[]
}

export interface TimeRiskFactor {
  description: string
  probability: number
  impact: number // days
  mitigation: string
}

export interface Milestone {
  name: string
  estimatedDate: Date
  dependencies: string[]
  confidence: number
}

export interface RiskPrediction {
  overallRiskScore: number
  riskCategories: RiskCategory[]
  topRisks: Risk[]
  mitigationStrategies: string[]
  monitoringPlan: MonitoringItem[]
}

export interface RiskCategory {
  name: string
  score: number
  factors: string[]
}

export interface Risk {
  id: string
  description: string
  category: string
  probability: number
  impact: 'low' | 'medium' | 'high' | 'critical'
  earlyWarningSignals: string[]
  preventiveMeasures: string[]
}

export interface MonitoringItem {
  metric: string
  threshold: number
  frequency: 'daily' | 'weekly' | 'monthly'
  responsible: string
}

// Historical project data structure
export interface HistoricalProject {
  id: string
  type: ProjectType
  size: number // square feet
  location: string
  estimatedCost: number
  actualCost: number
  estimatedDuration: number
  actualDuration: number
  weatherDelays: number
  changeOrders: number
  majorIssues: string[]
  completionDate: Date
}

// ML model parameters
interface ModelParameters {
  seasonalFactors: Record<string, number>
  locationFactors: Record<string, number>
  complexityMultipliers: Record<string, number>
  historicalAccuracy: number
}

export class PredictiveAnalyticsService {
  private historicalData: HistoricalProject[] = []
  private modelParameters: ModelParameters
  
  constructor() {
    // Initialize with default parameters
    this.modelParameters = {
      seasonalFactors: {
        'winter': 1.15, // 15% increase for winter construction
        'spring': 1.05,
        'summer': 0.95,
        'fall': 1.00
      },
      locationFactors: {
        'urban': 1.20,
        'suburban': 1.00,
        'rural': 0.85
      },
      complexityMultipliers: {
        'simple': 0.90,
        'standard': 1.00,
        'complex': 1.25,
        'highly-complex': 1.50
      },
      historicalAccuracy: 0.85
    }
    
    // Load mock historical data
    this.loadHistoricalData()
  }
  
  /**
   * Predict project costs with ML analysis
   */
  async predictCosts(
    takeoffItems: TakeoffItem[],
    projectDetails: {
      type: ProjectType
      size: number
      location: string
      startDate: Date
      complexity?: 'simple' | 'standard' | 'complex' | 'highly-complex'
    },
    companyType?: CompanyType
  ): Promise<CostPrediction> {
    const startTime = Date.now()
    const predictionId = `cost-prediction-${Date.now()}`
    
    takeoffLogger.info('Starting cost prediction', {
      predictionId,
      itemCount: takeoffItems.length,
      projectType: projectDetails.type,
      projectSize: projectDetails.size
    })
    
    try {
      // Calculate base cost from takeoff
      const baseCost = takeoffItems.reduce((sum, item) => sum + item.totalCost, 0)
      
      // Analyze historical similar projects
      const historicalAnalysis = this.analyzeHistoricalProjects(
        projectDetails.type,
        projectDetails.size,
        baseCost
      )
      
      // Calculate cost factors
      const factors = this.calculateCostFactors(
        projectDetails,
        baseCost,
        historicalAnalysis
      )
      
      // Apply ML adjustments
      const adjustedCost = this.applyMLAdjustments(
        baseCost,
        factors,
        projectDetails
      )
      
      // Generate AI insights
      const aiInsights = await this.generateCostInsights(
        takeoffItems,
        projectDetails,
        factors,
        adjustedCost,
        companyType
      )
      
      // Calculate confidence interval
      const confidenceInterval = this.calculateConfidenceInterval(
        adjustedCost,
        historicalAnalysis.variance,
        factors.length
      )
      
      const prediction: CostPrediction = {
        estimatedCost: adjustedCost,
        confidenceInterval,
        confidence: this.calculateConfidence(historicalAnalysis, factors),
        factors,
        recommendations: aiInsights.recommendations,
        historicalComparison: {
          similarProjects: historicalAnalysis.count,
          averageCost: historicalAnalysis.averageCost,
          percentile: historicalAnalysis.percentile
        }
      }
      
      logProcessingStep(takeoffLogger, 'Cost prediction completed', {
        predictionId,
        baseCost,
        adjustedCost,
        confidence: prediction.confidence,
        factors: factors.length
      }, startTime)
      
      return prediction
      
    } catch (error) {
      logError(takeoffLogger, error, 'Cost prediction failed', {
        predictionId
      })
      
      // Return basic prediction on error
      return {
        estimatedCost: takeoffItems.reduce((sum, item) => sum + item.totalCost, 0),
        confidenceInterval: { low: 0, high: 0 },
        confidence: 0.5,
        factors: [],
        recommendations: ['Unable to generate ML predictions'],
        historicalComparison: {
          similarProjects: 0,
          averageCost: 0,
          percentile: 50
        }
      }
    }
  }
  
  /**
   * Predict project timeline
   */
  async predictTimeline(
    projectDetails: {
      type: ProjectType
      size: number
      location: string
      startDate: Date
      scope: string[]
    },
    takeoffItems: TakeoffItem[],
    companyType?: CompanyType
  ): Promise<TimePrediction> {
    const predictionId = `time-prediction-${Date.now()}`
    
    takeoffLogger.info('Starting timeline prediction', {
      predictionId,
      projectType: projectDetails.type,
      projectSize: projectDetails.size,
      scopeItems: projectDetails.scope.length
    })
    
    try {
      // Estimate base duration from project size and type
      const baseDuration = this.estimateBaseDuration(
        projectDetails.type,
        projectDetails.size
      )
      
      // Analyze weather impact
      const weatherImpact = await this.predictWeatherImpact(
        projectDetails.location,
        projectDetails.startDate,
        baseDuration
      )
      
      // Identify critical path
      const criticalPath = this.identifyCriticalPath(
        projectDetails.scope,
        takeoffItems
      )
      
      // Calculate risk factors
      const riskFactors = this.calculateTimeRisks(
        projectDetails,
        takeoffItems,
        weatherImpact
      )
      
      // Generate milestones
      const milestones = this.generateMilestones(
        projectDetails,
        baseDuration,
        criticalPath
      )
      
      // Adjust duration based on all factors
      const adjustedDuration = baseDuration + 
        weatherImpact + 
        riskFactors.reduce((sum, risk) => sum + (risk.probability * risk.impact), 0)
      
      return {
        estimatedDuration: Math.ceil(adjustedDuration),
        criticalPath,
        riskFactors,
        weatherImpact,
        confidenceLevel: this.calculateTimelineConfidence(riskFactors),
        milestones
      }
      
    } catch (error) {
      logError(takeoffLogger, error, 'Timeline prediction failed', {
        predictionId
      })
      
      // Return basic estimate
      const baseDuration = this.estimateBaseDuration(
        projectDetails.type,
        projectDetails.size
      )
      
      return {
        estimatedDuration: baseDuration,
        criticalPath: [],
        riskFactors: [],
        weatherImpact: 0,
        confidenceLevel: 0.5,
        milestones: []
      }
    }
  }
  
  /**
   * Predict project risks
   */
  async predictRisks(
    projectDetails: any,
    takeoffItems: TakeoffItem[],
    companyType?: CompanyType
  ): Promise<RiskPrediction> {
    const predictionId = `risk-prediction-${Date.now()}`
    
    try {
      // Analyze project characteristics
      const projectCharacteristics = this.analyzeProjectCharacteristics(
        projectDetails,
        takeoffItems
      )
      
      // Calculate risk scores by category
      const riskCategories = this.calculateRiskCategories(
        projectCharacteristics,
        projectDetails
      )
      
      // Identify top risks
      const topRisks = await this.identifyTopRisks(
        projectCharacteristics,
        riskCategories,
        companyType
      )
      
      // Generate mitigation strategies
      const mitigationStrategies = await this.generateMitigationStrategies(
        topRisks,
        projectDetails,
        companyType
      )
      
      // Create monitoring plan
      const monitoringPlan = this.createMonitoringPlan(
        topRisks,
        riskCategories
      )
      
      // Calculate overall risk score
      const overallRiskScore = this.calculateOverallRiskScore(riskCategories)
      
      return {
        overallRiskScore,
        riskCategories,
        topRisks,
        mitigationStrategies,
        monitoringPlan
      }
      
    } catch (error) {
      logError(takeoffLogger, error, 'Risk prediction failed', {
        predictionId
      })
      
      return {
        overallRiskScore: 50,
        riskCategories: [],
        topRisks: [],
        mitigationStrategies: ['Unable to generate risk predictions'],
        monitoringPlan: []
      }
    }
  }
  
  /**
   * Analyze historical projects
   */
  private analyzeHistoricalProjects(
    projectType: ProjectType,
    size: number,
    estimatedCost: number
  ) {
    // Filter similar projects
    const similarProjects = this.historicalData.filter(project => 
      project.type === projectType &&
      project.size >= size * 0.7 &&
      project.size <= size * 1.3
    )
    
    if (similarProjects.length === 0) {
      return {
        count: 0,
        averageCost: estimatedCost,
        variance: 0.2,
        percentile: 50
      }
    }
    
    // Calculate statistics
    const costs = similarProjects.map(p => p.actualCost)
    const averageCost = costs.reduce((sum, cost) => sum + cost, 0) / costs.length
    const variance = this.calculateVariance(costs)
    
    // Calculate percentile
    const sortedCosts = [...costs].sort((a, b) => a - b)
    const position = sortedCosts.findIndex(cost => cost >= estimatedCost)
    const percentile = position === -1 ? 100 : (position / sortedCosts.length) * 100
    
    return {
      count: similarProjects.length,
      averageCost,
      variance,
      percentile
    }
  }
  
  /**
   * Calculate cost factors
   */
  private calculateCostFactors(
    projectDetails: any,
    baseCost: number,
    historicalAnalysis: any
  ): CostFactor[] {
    const factors: CostFactor[] = []
    
    // Seasonal factor
    const season = this.getSeason(projectDetails.startDate)
    const seasonalFactor = this.modelParameters.seasonalFactors[season]
    if (seasonalFactor !== 1.0) {
      factors.push({
        name: `${season} construction`,
        impact: (seasonalFactor - 1) * 100,
        direction: seasonalFactor > 1 ? 'increase' : 'decrease',
        category: 'seasonal'
      })
    }
    
    // Location factor
    const locationType = this.getLocationType(projectDetails.location)
    const locationFactor = this.modelParameters.locationFactors[locationType]
    if (locationFactor !== 1.0) {
      factors.push({
        name: `${locationType} location`,
        impact: (locationFactor - 1) * 100,
        direction: locationFactor > 1 ? 'increase' : 'decrease',
        category: 'location'
      })
    }
    
    // Complexity factor
    const complexity = projectDetails.complexity || 'standard'
    const complexityFactor = this.modelParameters.complexityMultipliers[complexity]
    if (complexityFactor !== 1.0) {
      factors.push({
        name: `${complexity} complexity`,
        impact: (complexityFactor - 1) * 100,
        direction: complexityFactor > 1 ? 'increase' : 'decrease',
        category: 'complexity'
      })
    }
    
    // Historical deviation
    if (historicalAnalysis.count > 0) {
      const deviation = ((baseCost - historicalAnalysis.averageCost) / historicalAnalysis.averageCost) * 100
      if (Math.abs(deviation) > 10) {
        factors.push({
          name: 'Historical cost adjustment',
          impact: -deviation * 0.5, // Adjust by half the deviation
          direction: deviation > 0 ? 'decrease' : 'increase',
          category: 'market'
        })
      }
    }
    
    return factors
  }
  
  /**
   * Apply ML adjustments to cost
   */
  private applyMLAdjustments(
    baseCost: number,
    factors: CostFactor[],
    projectDetails: any
  ): number {
    let adjustedCost = baseCost
    
    // Apply each factor
    factors.forEach(factor => {
      const adjustment = baseCost * (factor.impact / 100)
      adjustedCost += factor.direction === 'increase' ? adjustment : -adjustment
    })
    
    // Apply confidence-based adjustment
    const confidenceAdjustment = 1 + (1 - this.modelParameters.historicalAccuracy) * 0.1
    adjustedCost *= confidenceAdjustment
    
    return Math.round(adjustedCost)
  }
  
  /**
   * Generate AI insights for cost prediction
   */
  private async generateCostInsights(
    takeoffItems: TakeoffItem[],
    projectDetails: any,
    factors: CostFactor[],
    predictedCost: number,
    companyType?: CompanyType
  ) {
    const prompt = `Analyze this construction cost prediction and provide insights:

Project Type: ${projectDetails.type}
Size: ${projectDetails.size} SF
Location: ${projectDetails.location}
Company Type: ${companyType || 'General Contractor'}
Base Cost: $${takeoffItems.reduce((sum, item) => sum + item.totalCost, 0).toLocaleString()}
Predicted Cost: $${predictedCost.toLocaleString()}

Cost Factors:
${factors.map(f => `- ${f.name}: ${f.direction === 'increase' ? '+' : '-'}${Math.abs(f.impact)}%`).join('\n')}

Top 5 Cost Items:
${takeoffItems
  .sort((a, b) => b.totalCost - a.totalCost)
  .slice(0, 5)
  .map(item => `- ${item.description}: $${item.totalCost.toLocaleString()}`)
  .join('\n')}

Provide:
1. Key cost drivers for this project
2. Potential cost-saving opportunities
3. Risk factors that could increase costs
4. Recommendations for cost optimization

Format as JSON with: keyDrivers[], opportunities[], risks[], recommendations[]`
    
    try {
      const response = await geminiService.sendMessage(prompt, 'cost-insights', {
        companyType
      })
      
      const insights = JSON.parse(response)
      return {
        recommendations: [
          ...insights.recommendations,
          ...insights.opportunities.slice(0, 2)
        ]
      }
    } catch (error) {
      return {
        recommendations: [
          'Review material specifications for cost optimization',
          'Consider value engineering opportunities',
          'Monitor market conditions for price fluctuations'
        ]
      }
    }
  }
  
  /**
   * Calculate confidence interval
   */
  private calculateConfidenceInterval(
    estimate: number,
    variance: number,
    factorCount: number
  ) {
    // Base margin of error
    let marginPercent = 0.1 // 10% base
    
    // Adjust based on variance
    marginPercent += variance * 0.5
    
    // Adjust based on number of factors
    marginPercent += factorCount * 0.02
    
    // Cap at 30%
    marginPercent = Math.min(marginPercent, 0.3)
    
    return {
      low: Math.round(estimate * (1 - marginPercent)),
      high: Math.round(estimate * (1 + marginPercent))
    }
  }
  
  /**
   * Calculate prediction confidence
   */
  private calculateConfidence(historicalAnalysis: any, factors: CostFactor[]): number {
    let confidence = 0.5 // Base confidence
    
    // Increase confidence based on historical data
    if (historicalAnalysis.count > 10) {
      confidence += 0.2
    } else if (historicalAnalysis.count > 5) {
      confidence += 0.1
    }
    
    // Adjust based on variance
    if (historicalAnalysis.variance < 0.1) {
      confidence += 0.1
    } else if (historicalAnalysis.variance > 0.3) {
      confidence -= 0.1
    }
    
    // Adjust based on factors
    if (factors.length < 3) {
      confidence += 0.1
    } else if (factors.length > 5) {
      confidence -= 0.1
    }
    
    // Apply model accuracy
    confidence *= this.modelParameters.historicalAccuracy
    
    return Math.max(0.3, Math.min(0.95, confidence))
  }
  
  /**
   * Helper methods
   */
  private getSeason(date: Date): string {
    const month = date.getMonth()
    if (month >= 2 && month <= 4) return 'spring'
    if (month >= 5 && month <= 7) return 'summer'
    if (month >= 8 && month <= 10) return 'fall'
    return 'winter'
  }
  
  private getLocationType(location: string): string {
    const urban = ['city', 'downtown', 'metro', 'urban']
    const rural = ['rural', 'country', 'remote']
    
    const locationLower = location.toLowerCase()
    
    if (urban.some(term => locationLower.includes(term))) return 'urban'
    if (rural.some(term => locationLower.includes(term))) return 'rural'
    
    return 'suburban'
  }
  
  private calculateVariance(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2))
    const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length
    return variance / (mean * mean) // Coefficient of variation
  }
  
  private estimateBaseDuration(type: ProjectType, size: number): number {
    // Base duration estimates (days per 1000 SF)
    const durationRates: Record<ProjectType, number> = {
      commercial: 15,
      residential: 10,
      industrial: 20,
      infrastructure: 25,
      institutional: 18
    }
    
    const rate = durationRates[type] || 15
    return Math.ceil((size / 1000) * rate)
  }
  
  private async predictWeatherImpact(
    location: string,
    startDate: Date,
    duration: number
  ): Promise<number> {
    // Simplified weather impact calculation
    const season = this.getSeason(startDate)
    const seasonalDelays: Record<string, number> = {
      winter: duration * 0.15,
      spring: duration * 0.08,
      summer: duration * 0.03,
      fall: duration * 0.05
    }
    
    return seasonalDelays[season] || 0
  }
  
  private identifyCriticalPath(scope: string[], items: TakeoffItem[]): string[] {
    // Simplified critical path identification
    const majorPhases = [
      'Site Preparation',
      'Foundation',
      'Structural Frame',
      'Envelope',
      'MEP Rough-In',
      'Interior Finishes',
      'Final Inspections'
    ]
    
    return majorPhases.filter(phase => 
      scope.some(s => s.toLowerCase().includes(phase.toLowerCase()))
    )
  }
  
  private calculateTimeRisks(
    projectDetails: any,
    items: TakeoffItem[],
    weatherImpact: number
  ): TimeRiskFactor[] {
    const risks: TimeRiskFactor[] = []
    
    // Weather risk
    if (weatherImpact > 5) {
      risks.push({
        description: 'Adverse weather conditions',
        probability: 0.3,
        impact: weatherImpact,
        mitigation: 'Build weather contingency into schedule'
      })
    }
    
    // Material availability risk
    const longLeadItems = items.filter(item => (item.leadTime || 0) > 14)
    if (longLeadItems.length > 0) {
      risks.push({
        description: 'Long lead time materials',
        probability: 0.4,
        impact: Math.max(...longLeadItems.map(i => i.leadTime || 0)) - 14,
        mitigation: 'Order long lead items early'
      })
    }
    
    // Complexity risk
    if (projectDetails.complexity === 'complex' || projectDetails.complexity === 'highly-complex') {
      risks.push({
        description: 'Project complexity delays',
        probability: 0.5,
        impact: projectDetails.size / 1000 * 2,
        mitigation: 'Add coordination meetings and reviews'
      })
    }
    
    return risks
  }
  
  private generateMilestones(
    projectDetails: any,
    duration: number,
    criticalPath: string[]
  ): Milestone[] {
    const milestones: Milestone[] = []
    const startDate = new Date(projectDetails.startDate)
    
    criticalPath.forEach((phase, index) => {
      const phaseDuration = duration / criticalPath.length
      const phaseStart = new Date(startDate)
      phaseStart.setDate(phaseStart.getDate() + Math.floor(phaseDuration * index))
      
      milestones.push({
        name: phase,
        estimatedDate: phaseStart,
        dependencies: index > 0 ? [criticalPath[index - 1]] : [],
        confidence: 0.8 - (index * 0.05) // Confidence decreases for later milestones
      })
    })
    
    return milestones
  }
  
  private calculateTimelineConfidence(risks: TimeRiskFactor[]): number {
    const totalRiskImpact = risks.reduce((sum, risk) => 
      sum + (risk.probability * risk.impact), 0
    )
    
    // Higher risk impact = lower confidence
    const riskFactor = Math.min(totalRiskImpact / 30, 0.5)
    return Math.max(0.5, 0.9 - riskFactor)
  }
  
  private analyzeProjectCharacteristics(
    projectDetails: any,
    items: TakeoffItem[]
  ) {
    return {
      size: projectDetails.size,
      complexity: projectDetails.complexity || 'standard',
      totalCost: items.reduce((sum, item) => sum + item.totalCost, 0),
      itemCount: items.length,
      highValueItems: items.filter(item => item.totalCost > 10000).length,
      hasSpecialtyWork: items.some(item => 
        ['Electrical', 'Plumbing', 'HVAC'].includes(item.category)
      )
    }
  }
  
  private calculateRiskCategories(
    characteristics: any,
    projectDetails: any
  ): RiskCategory[] {
    const categories: RiskCategory[] = []
    
    // Cost risk
    const costRiskScore = this.calculateCostRiskScore(characteristics)
    categories.push({
      name: 'Cost Overrun',
      score: costRiskScore,
      factors: this.getCostRiskFactors(costRiskScore, characteristics)
    })
    
    // Schedule risk
    const scheduleRiskScore = this.calculateScheduleRiskScore(projectDetails, characteristics)
    categories.push({
      name: 'Schedule Delay',
      score: scheduleRiskScore,
      factors: this.getScheduleRiskFactors(scheduleRiskScore, projectDetails)
    })
    
    // Quality risk
    const qualityRiskScore = this.calculateQualityRiskScore(characteristics)
    categories.push({
      name: 'Quality Issues',
      score: qualityRiskScore,
      factors: this.getQualityRiskFactors(qualityRiskScore, characteristics)
    })
    
    // Safety risk
    const safetyRiskScore = this.calculateSafetyRiskScore(projectDetails, characteristics)
    categories.push({
      name: 'Safety Incidents',
      score: safetyRiskScore,
      factors: this.getSafetyRiskFactors(safetyRiskScore, projectDetails)
    })
    
    return categories
  }
  
  private calculateCostRiskScore(characteristics: any): number {
    let score = 30 // Base score
    
    if (characteristics.complexity === 'complex') score += 20
    if (characteristics.complexity === 'highly-complex') score += 30
    if (characteristics.highValueItems > 10) score += 15
    if (characteristics.hasSpecialtyWork) score += 10
    
    return Math.min(100, score)
  }
  
  private getCostRiskFactors(score: number, characteristics: any): string[] {
    const factors = []
    
    if (score > 70) factors.push('High project complexity')
    if (characteristics.highValueItems > 10) factors.push('Multiple high-value items')
    if (characteristics.hasSpecialtyWork) factors.push('Specialty contractor coordination')
    if (score > 50) factors.push('Market price volatility')
    
    return factors
  }
  
  private calculateScheduleRiskScore(projectDetails: any, characteristics: any): number {
    let score = 25
    
    const season = this.getSeason(new Date(projectDetails.startDate))
    if (season === 'winter') score += 20
    if (characteristics.complexity !== 'simple') score += 15
    if (characteristics.itemCount > 100) score += 10
    
    return Math.min(100, score)
  }
  
  private getScheduleRiskFactors(score: number, projectDetails: any): string[] {
    const factors = []
    const season = this.getSeason(new Date(projectDetails.startDate))
    
    if (season === 'winter') factors.push('Winter weather conditions')
    if (score > 60) factors.push('Complex coordination requirements')
    factors.push('Material delivery delays')
    if (score > 40) factors.push('Permitting and inspection delays')
    
    return factors
  }
  
  private calculateQualityRiskScore(characteristics: any): number {
    let score = 20
    
    if (characteristics.complexity === 'highly-complex') score += 25
    if (characteristics.itemCount > 150) score += 15
    
    return Math.min(100, score)
  }
  
  private getQualityRiskFactors(score: number, characteristics: any): string[] {
    const factors = []
    
    if (characteristics.complexity === 'highly-complex') factors.push('Complex specifications')
    if (score > 50) factors.push('Multiple trade coordination')
    factors.push('Quality control processes')
    
    return factors
  }
  
  private calculateSafetyRiskScore(projectDetails: any, characteristics: any): number {
    let score = 30
    
    if (projectDetails.type === 'industrial') score += 20
    if (characteristics.hasSpecialtyWork) score += 10
    if (projectDetails.size > 50000) score += 15
    
    return Math.min(100, score)
  }
  
  private getSafetyRiskFactors(score: number, projectDetails: any): string[] {
    const factors = []
    
    if (projectDetails.type === 'industrial') factors.push('Industrial hazards')
    if (score > 60) factors.push('Height work required')
    factors.push('Multiple contractors on site')
    if (projectDetails.size > 50000) factors.push('Large site coordination')
    
    return factors
  }
  
  private async identifyTopRisks(
    characteristics: any,
    categories: RiskCategory[],
    companyType?: CompanyType
  ): Promise<Risk[]> {
    const risks: Risk[] = []
    
    // Add top risks from each category
    categories.forEach(category => {
      if (category.score > 60) {
        risks.push({
          id: `risk-${category.name.toLowerCase().replace(/\s+/g, '-')}`,
          description: `High ${category.name.toLowerCase()} risk`,
          category: category.name,
          probability: category.score / 100,
          impact: category.score > 80 ? 'critical' : category.score > 60 ? 'high' : 'medium',
          earlyWarningSignals: this.getEarlyWarningSignals(category.name),
          preventiveMeasures: this.getPreventiveMeasures(category.name)
        })
      }
    })
    
    return risks.sort((a, b) => b.probability - a.probability).slice(0, 5)
  }
  
  private getEarlyWarningSignals(categoryName: string): string[] {
    const signals: Record<string, string[]> = {
      'Cost Overrun': [
        'Material price increases > 5%',
        'Change order requests increasing',
        'Subcontractor bid variances'
      ],
      'Schedule Delay': [
        'Milestone dates slipping',
        'RFI response times increasing',
        'Weather forecast changes'
      ],
      'Quality Issues': [
        'Failed inspections',
        'Rework requests',
        'Specification clarifications'
      ],
      'Safety Incidents': [
        'Near-miss reports increasing',
        'PPE compliance issues',
        'Safety meeting attendance dropping'
      ]
    }
    
    return signals[categoryName] || []
  }
  
  private getPreventiveMeasures(categoryName: string): string[] {
    const measures: Record<string, string[]> = {
      'Cost Overrun': [
        'Lock in material prices early',
        'Maintain contingency budget',
        'Regular cost tracking meetings'
      ],
      'Schedule Delay': [
        'Build float into schedule',
        'Expedite long-lead items',
        'Weekly schedule reviews'
      ],
      'Quality Issues': [
        'Pre-installation meetings',
        'Mock-up requirements',
        'Third-party inspections'
      ],
      'Safety Incidents': [
        'Daily safety briefings',
        'Site-specific safety plan',
        'Regular safety audits'
      ]
    }
    
    return measures[categoryName] || []
  }
  
  private async generateMitigationStrategies(
    risks: Risk[],
    projectDetails: any,
    companyType?: CompanyType
  ): Promise<string[]> {
    const strategies: string[] = []
    
    risks.forEach(risk => {
      if (risk.impact === 'critical' || risk.impact === 'high') {
        strategies.push(...risk.preventiveMeasures)
      }
    })
    
    // Add general strategies
    strategies.push(
      'Implement comprehensive project management software',
      'Establish clear communication protocols',
      'Regular stakeholder meetings',
      'Maintain detailed documentation'
    )
    
    return Array.from(new Set(strategies)).slice(0, 10) // Remove duplicates and limit
  }
  
  private createMonitoringPlan(
    risks: Risk[],
    categories: RiskCategory[]
  ): MonitoringItem[] {
    const items: MonitoringItem[] = []
    
    // Cost monitoring
    const costCategory = categories.find(c => c.name === 'Cost Overrun')
    if (costCategory && costCategory.score > 50) {
      items.push({
        metric: 'Budget variance',
        threshold: 5, // 5% variance
        frequency: 'weekly',
        responsible: 'Project Manager'
      })
    }
    
    // Schedule monitoring
    const scheduleCategory = categories.find(c => c.name === 'Schedule Delay')
    if (scheduleCategory && scheduleCategory.score > 50) {
      items.push({
        metric: 'Schedule variance',
        threshold: 3, // 3 days
        frequency: 'weekly',
        responsible: 'Project Manager'
      })
    }
    
    // Quality monitoring
    items.push({
      metric: 'Inspection pass rate',
      threshold: 95, // 95% pass rate
      frequency: 'weekly',
      responsible: 'Quality Manager'
    })
    
    // Safety monitoring
    items.push({
      metric: 'Safety incidents',
      threshold: 0, // Zero incidents
      frequency: 'daily',
      responsible: 'Safety Officer'
    })
    
    return items
  }
  
  private calculateOverallRiskScore(categories: RiskCategory[]): number {
    const totalScore = categories.reduce((sum, cat) => sum + cat.score, 0)
    return Math.round(totalScore / categories.length)
  }
  
  /**
   * Load historical project data
   */
  private loadHistoricalData() {
    // Mock historical data - in production would load from database
    this.historicalData = [
      {
        id: 'hist-001',
        type: 'commercial',
        size: 50000,
        location: 'Urban',
        estimatedCost: 7500000,
        actualCost: 8200000,
        estimatedDuration: 365,
        actualDuration: 410,
        weatherDelays: 15,
        changeOrders: 12,
        majorIssues: ['Material delays', 'Design changes'],
        completionDate: new Date('2024-06-15')
      },
      {
        id: 'hist-002',
        type: 'commercial',
        size: 45000,
        location: 'Suburban',
        estimatedCost: 6750000,
        actualCost: 6900000,
        estimatedDuration: 330,
        actualDuration: 340,
        weatherDelays: 5,
        changeOrders: 8,
        majorIssues: ['Permit delays'],
        completionDate: new Date('2024-03-20')
      },
      // Add more historical projects...
    ]
  }
}

// Export singleton instance
export const predictiveAnalytics = new PredictiveAnalyticsService()