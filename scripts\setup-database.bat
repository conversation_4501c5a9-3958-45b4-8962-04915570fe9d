@echo off
echo Setting up PostgreSQL database for AI Construction Management
echo.

REM Check if .env.local exists
if not exist .env.local (
    echo Error: .env.local file not found!
    echo Creating .env.local from .env.example...
    copy .env.example .env.local
    echo Please update the DATABASE_URL in .env.local with your PostgreSQL connection string
    pause
    exit /b 1
)

echo Found .env.local configuration
echo.

REM Generate Prisma Client
echo Generating Prisma Client...
call npm run db:generate
if errorlevel 1 (
    echo Error: Failed to generate Prisma Client
    pause
    exit /b 1
)

echo.
echo Pushing database schema...
call npm run db:push
if errorlevel 1 (
    echo Error: Failed to push database schema
    echo Make sure your PostgreSQL server is running and the DATABASE_URL is correct
    pause
    exit /b 1
)

echo.
echo Seeding database with initial data...
call npm run db:seed
if errorlevel 1 (
    echo Warning: Failed to seed database (this is optional)
) else (
    echo Database seeded successfully
)

echo.
echo Database setup completed successfully!
echo.
echo Demo credentials:
echo   Admin: <EMAIL> / demo123456
echo   Contractor: <EMAIL> / demo123456
echo.
echo You can now run 'npm run dev' to start the application
echo Or run 'npm run db:studio' to explore your database
pause