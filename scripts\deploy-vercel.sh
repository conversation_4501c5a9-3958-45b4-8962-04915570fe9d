#!/bin/bash

# Vercel Deployment Script for AI Construction Management Platform
# This script prepares and deploys the application to Vercel

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting Vercel deployment...${NC}"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo -e "${RED}Vercel CLI is not installed. Installing...${NC}"
    npm i -g vercel
fi

# Environment
ENVIRONMENT=${1:-"production"}

# Create Vercel configuration
echo -e "${YELLOW}Creating Vercel configuration...${NC}"
cat > vercel.json <<EOF
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "regions": ["iad1"],
  "functions": {
    "src/app/api/**.ts": {
      "maxDuration": 30
    },
    "src/app/api/takeoff/**.ts": {
      "maxDuration": 60
    }
  },
  "env": {
    "GEMINI_MODEL": "@gemini_model",
    "JWT_SECRET": "@jwt_secret",
    "DATABASE_URL": "@database_url",
    "REDIS_URL": "@redis_url"
  },
  "build": {
    "env": {
      "NEXT_PUBLIC_APP_URL": "@next_public_app_url",
      "NEXT_PUBLIC_MAPBOX_TOKEN": "@next_public_mapbox_token"
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        { "key": "Access-Control-Allow-Credentials", "value": "true" },
        { "key": "Access-Control-Allow-Origin", "value": "*" },
        { "key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS,PATCH,DELETE,POST,PUT" },
        { "key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version" }
      ]
    },
    {
      "source": "/(.*)",
      "headers": [
        { "key": "X-Content-Type-Options", "value": "nosniff" },
        { "key": "X-Frame-Options", "value": "DENY" },
        { "key": "X-XSS-Protection", "value": "1; mode=block" },
        { "key": "Referrer-Policy", "value": "strict-origin-when-cross-origin" },
        { "key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()" }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/api/health",
      "destination": "/api/health"
    }
  ]
}
EOF

# Set up environment variables
echo -e "${YELLOW}Setting up environment variables...${NC}"
if [ "$ENVIRONMENT" == "production" ]; then
    echo -e "${YELLOW}Using production environment${NC}"
    vercel link --yes
    
    # Pull production environment variables
    vercel env pull .env.production
    
    # Deploy to production
    echo -e "${YELLOW}Deploying to production...${NC}"
    vercel --prod
else
    echo -e "${YELLOW}Using preview environment${NC}"
    vercel link --yes
    
    # Deploy to preview
    echo -e "${YELLOW}Deploying to preview...${NC}"
    vercel
fi

# Run post-deployment tasks
echo -e "${YELLOW}Running post-deployment tasks...${NC}"

# Get deployment URL
DEPLOYMENT_URL=$(vercel ls --json | jq -r '.[0].url')
echo -e "${GREEN}Deployment URL: https://${DEPLOYMENT_URL}${NC}"

# Health check
echo -e "${YELLOW}Running health check...${NC}"
sleep 10
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://${DEPLOYMENT_URL}/api/health")

if [ "$HTTP_STATUS" == "200" ]; then
    echo -e "${GREEN}Health check passed!${NC}"
else
    echo -e "${RED}Health check failed with status: ${HTTP_STATUS}${NC}"
    exit 1
fi

# Cleanup
rm -f vercel.json

echo -e "${GREEN}Vercel deployment completed successfully!${NC}"
echo -e "${GREEN}Visit your deployment at: https://${DEPLOYMENT_URL}${NC}"