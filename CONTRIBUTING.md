# Contributing to AI Construction Management Platform

Thank you for your interest in contributing to the AI Construction Management Platform! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Setup](#development-setup)
4. [How to Contribute](#how-to-contribute)
5. [Development Workflow](#development-workflow)
6. [Coding Standards](#coding-standards)
7. [Testing Guidelines](#testing-guidelines)
8. [Documentation](#documentation)
9. [Pull Request Process](#pull-request-process)
10. [Community](#community)

## Code of Conduct

### Our Pledge

We are committed to providing a welcoming and inspiring community for all. Contributors are expected to:

- Use welcoming and inclusive language
- Be respectful of differing viewpoints and experiences
- Gracefully accept constructive criticism
- Focus on what is best for the community
- Show empathy towards other community members

### Unacceptable Behavior

- Harassment, discrimination, or offensive comments
- Trolling or insulting/derogatory comments
- Public or private harassment
- Publishing others' private information
- Other conduct which could reasonably be considered inappropriate

## Getting Started

### Prerequisites

Before contributing, ensure you have:

- Node.js 18.x or higher
- PostgreSQL 14.x or higher
- Git
- A GitHub account
- Basic knowledge of TypeScript, React, and Next.js

### Understanding the Project

1. **Read the Documentation**
   - Review the [README.md](README.md)
   - Study the [Architecture Documentation](docs/ARCHITECTURE.md)
   - Understand the [API Documentation](docs/API.md)

2. **Explore the Codebase**
   - Familiarize yourself with the project structure
   - Review existing code patterns
   - Check the tech stack and dependencies

3. **Join the Community**
   - Join our Discord server
   - Subscribe to the mailing list
   - Follow project updates

## Development Setup

### 1. Fork and Clone

```bash
# Fork the repository on GitHub
# Then clone your fork
git clone https://github.com/YOUR_USERNAME/ai-construction-management.git
cd ai-construction-management

# Add upstream remote
git remote add upstream https://github.com/original-org/ai-construction-management.git
```

### 2. Install Dependencies

```bash
# Install Node.js dependencies
npm install

# Install pre-commit hooks
npm run prepare
```

### 3. Environment Configuration

```bash
# Copy example environment file
cp .env.example .env.local

# Configure your environment variables
# Edit .env.local with your settings
```

Required environment variables:
```env
DATABASE_URL="postgresql://user:password@localhost:5432/construction_dev"
JWT_SECRET="your-development-secret"
GEMINI_API_KEY="your-gemini-api-key"
```

### 4. Database Setup

```bash
# Run migrations
npx prisma migrate dev

# Seed development data
npx prisma db seed
```

### 5. Start Development Server

```bash
# Start the development server
npm run dev

# In another terminal, start the Socket.io server
npm run socket:dev
```

## How to Contribute

### Types of Contributions

#### 1. Bug Reports
- Search existing issues first
- Use the bug report template
- Include reproduction steps
- Provide system information

#### 2. Feature Requests
- Check the roadmap first
- Use the feature request template
- Explain the use case
- Consider implementation approach

#### 3. Code Contributions
- Bug fixes
- New features
- Performance improvements
- Refactoring

#### 4. Documentation
- Fix typos or clarify existing docs
- Add missing documentation
- Create tutorials or guides
- Improve code comments

#### 5. Testing
- Add missing tests
- Improve test coverage
- Fix failing tests
- Performance testing

### Finding Issues to Work On

Look for issues labeled:
- `good-first-issue` - Great for newcomers
- `help-wanted` - Community help needed
- `bug` - Bug fixes
- `enhancement` - New features
- `documentation` - Documentation improvements

## Development Workflow

### 1. Branching Strategy

```bash
# Update your fork
git checkout main
git pull upstream main
git push origin main

# Create a feature branch
git checkout -b feature/your-feature-name
# OR for bug fixes
git checkout -b fix/issue-description
```

Branch naming conventions:
- `feature/` - New features
- `fix/` - Bug fixes
- `docs/` - Documentation
- `refactor/` - Code refactoring
- `test/` - Test improvements
- `perf/` - Performance improvements

### 2. Making Changes

1. **Write Clean Code**
   - Follow the coding standards
   - Add appropriate comments
   - Keep functions small and focused

2. **Test Your Changes**
   - Write unit tests for new code
   - Ensure existing tests pass
   - Test edge cases

3. **Update Documentation**
   - Update relevant documentation
   - Add JSDoc comments
   - Update API docs if needed

### 3. Committing Changes

Follow conventional commit format:

```bash
# Format: <type>(<scope>): <subject>

# Examples:
git commit -m "feat(projects): add bulk project import"
git commit -m "fix(auth): resolve JWT expiration issue"
git commit -m "docs(api): update endpoint documentation"
git commit -m "test(safety): add unit tests for incident reports"
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Code style changes
- `refactor`: Code refactoring
- `perf`: Performance improvements
- `test`: Test changes
- `build`: Build system changes
- `ci`: CI configuration changes
- `chore`: Other changes

## Coding Standards

### TypeScript Guidelines

```typescript
// Use explicit types
interface ProjectData {
  id: string;
  name: string;
  status: ProjectStatus;
}

// Prefer interfaces over types for objects
interface User {
  id: string;
  email: string;
}

// Use enums for constants
enum ProjectStatus {
  PLANNING = 'PLANNING',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED'
}

// Use proper error handling
try {
  const result = await someAsyncOperation();
  return result;
} catch (error) {
  logger.error('Operation failed', error);
  throw new ApplicationError('Operation failed', error);
}
```

### React/Next.js Guidelines

```typescript
// Use functional components with TypeScript
interface ProjectCardProps {
  project: Project;
  onUpdate: (id: string) => void;
}

export function ProjectCard({ project, onUpdate }: ProjectCardProps) {
  // Component logic
}

// Use proper hooks
import { useState, useEffect, useMemo } from 'react';

// Custom hooks in separate files
export function useProject(id: string) {
  // Hook implementation
}

// Proper component organization
components/
├── ui/           # Basic UI components
├── features/     # Feature-specific components
└── layouts/      # Layout components
```

### Style Guidelines

```typescript
// Use Tailwind CSS classes
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow">

// Component-specific styles with CSS modules when needed
import styles from './Component.module.css';

// Consistent spacing and formatting
<Button
  variant="primary"
  size="lg"
  onClick={handleClick}
  disabled={isLoading}
>
  {isLoading ? 'Loading...' : 'Submit'}
</Button>
```

### File Organization

```typescript
// Each component in its own directory
components/
└── ProjectCard/
    ├── index.tsx         # Main component
    ├── ProjectCard.tsx   # Component implementation
    ├── types.ts          # TypeScript types
    ├── utils.ts          # Utility functions
    └── __tests__/        # Component tests
```

## Testing Guidelines

### Unit Testing

```typescript
// Use descriptive test names
describe('ProjectService', () => {
  describe('createProject', () => {
    it('should create a project with valid data', async () => {
      // Test implementation
    });

    it('should throw error for invalid data', async () => {
      // Test implementation
    });
  });
});

// Test edge cases
it('should handle empty project list', () => {
  // Test implementation
});

// Mock external dependencies
jest.mock('@/lib/gemini', () => ({
  analyzeProject: jest.fn().mockResolvedValue({ success: true })
}));
```

### Integration Testing

```typescript
// Test API endpoints
describe('POST /api/projects', () => {
  it('should create a project', async () => {
    const response = await request(app)
      .post('/api/projects')
      .send(validProjectData)
      .expect(201);

    expect(response.body).toHaveProperty('id');
  });
});
```

### E2E Testing

```typescript
// Test user workflows
test('user can create and edit project', async ({ page }) => {
  await page.goto('/dashboard/projects');
  await page.click('button:has-text("New Project")');
  // Continue test flow
});
```

### Test Coverage

Maintain minimum coverage:
- Statements: 80%
- Branches: 75%
- Functions: 80%
- Lines: 80%

## Documentation

### Code Documentation

```typescript
/**
 * Creates a new project with AI-powered insights
 * @param data - Project creation data
 * @param userId - ID of the user creating the project
 * @returns Created project with generated insights
 * @throws {ValidationError} If data is invalid
 * @throws {AuthorizationError} If user lacks permissions
 */
export async function createProject(
  data: CreateProjectDTO,
  userId: string
): Promise<Project> {
  // Implementation
}
```

### API Documentation

Update API docs when:
- Adding new endpoints
- Changing request/response formats
- Modifying authentication requirements
- Adding new error codes

### README Updates

Keep README current with:
- New features
- Changed requirements
- Updated setup instructions
- New environment variables

## Pull Request Process

### 1. Before Creating a PR

- [ ] Code follows style guidelines
- [ ] Tests pass locally (`npm run test`)
- [ ] Linting passes (`npm run lint`)
- [ ] Type checking passes (`npm run type-check`)
- [ ] Documentation is updated
- [ ] Branch is up to date with main

### 2. Creating the PR

1. **Title**: Use conventional commit format
   ```
   feat(projects): add bulk import functionality
   ```

2. **Description Template**:
   ```markdown
   ## Description
   Brief description of changes

   ## Type of Change
   - [ ] Bug fix
   - [ ] New feature
   - [ ] Breaking change
   - [ ] Documentation update

   ## Testing
   - [ ] Unit tests pass
   - [ ] Integration tests pass
   - [ ] Manual testing completed

   ## Checklist
   - [ ] Code follows style guidelines
   - [ ] Self-review completed
   - [ ] Documentation updated
   - [ ] No new warnings

   ## Screenshots (if applicable)
   ```

### 3. PR Review Process

1. **Automated Checks**
   - CI/CD pipeline runs
   - Tests must pass
   - Code coverage maintained
   - No linting errors

2. **Code Review**
   - At least one approval required
   - Address all feedback
   - Resolve all conversations

3. **Final Steps**
   - Squash commits if needed
   - Update branch with main
   - Ensure clean commit history

## Community

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General discussions and questions
- **Discord**: Real-time chat and support
- **Email**: <EMAIL> for security issues

### Getting Help

- Check documentation first
- Search existing issues
- Ask in Discord #help channel
- Create a detailed issue if needed

### Recognition

Contributors are recognized through:
- Contributors list in README
- Shoutouts in release notes
- Community spotlight features
- Contributor badges

## Additional Resources

### Useful Links

- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### Development Tools

- **VS Code Extensions**:
  - ESLint
  - Prettier
  - Tailwind CSS IntelliSense
  - Prisma
  - GitLens

- **Browser Extensions**:
  - React Developer Tools
  - Redux DevTools (for Zustand)

### Learning Resources

- Project architecture walkthrough videos
- Codebase tour documentation
- Example implementations
- Best practices guide

## Thank You!

Thank you for contributing to the AI Construction Management Platform. Your efforts help make construction project management more efficient and intelligent for teams worldwide.

Every contribution, no matter how small, is valuable and appreciated!