# Fix Prisma permissions and setup database on Windows
Write-Host "Fixing Prisma setup for Windows..." -ForegroundColor Cyan

# Force remove Prisma client (Windows compatible)
$prismaPath = "node_modules\.prisma"
if (Test-Path $prismaPath) {
    Write-Host "Removing old Prisma client..." -ForegroundColor Yellow
    try {
        # Use Windows commands to force delete
        cmd /c "rmdir /s /q $prismaPath" 2>$null
        Remove-Item -Path $prismaPath -Recurse -Force -ErrorAction SilentlyContinue
    } catch {
        Write-Host "Warning: Could not fully remove old Prisma client" -ForegroundColor Yellow
    }
}

# Set environment variable for this session
$env:DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/ai_construction_db"

Write-Host "`nGenerating Prisma Client..." -ForegroundColor Yellow
npx prisma generate

if ($LASTEXITCODE -eq 0) {
    Write-Host "Prisma Client generated successfully!" -ForegroundColor Green
    
    Write-Host "`nPushing database schema..." -ForegroundColor Yellow
    npx prisma db push --skip-generate
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Database schema pushed successfully!" -ForegroundColor Green
        
        Write-Host "`nSeeding database..." -ForegroundColor Yellow
        npx tsx prisma/seed.ts
        
        Write-Host "`n✅ Database setup completed!" -ForegroundColor Green
        Write-Host "`nYou can now run: npm run dev" -ForegroundColor Cyan
    } else {
        Write-Host "`n❌ Failed to push database schema" -ForegroundColor Red
        Write-Host "Make sure PostgreSQL is running and accessible" -ForegroundColor Yellow
    }
} else {
    Write-Host "`n❌ Failed to generate Prisma Client" -ForegroundColor Red
}