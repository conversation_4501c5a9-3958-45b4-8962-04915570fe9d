#!/usr/bin/env pwsh
# Git push script using GitHub token from .env.local

Write-Host "🔐 Setting up Git with GitHub token authentication..." -ForegroundColor Yellow

# Load environment variables from .env.local
$envFile = Get-Content ".env.local" -Raw
$githubToken = ""
$githubUsername = ""
$githubEmail = ""

# Parse the .env.local file
foreach ($line in $envFile -split "`n") {
    if ($line -match "^GITHUB_TOKEN=(.+)$") {
        $githubToken = $matches[1].Trim()
    }
    elseif ($line -match "^GITHUB_USERNAME=(.+)$") {
        $githubUsername = $matches[1].Trim()
    }
    elseif ($line -match "^GITHUB_EMAIL=(.+)$") {
        $githubEmail = $matches[1].Trim()
    }
}

# Check if token is configured
if ($githubToken -eq "your-github-personal-access-token-here" -or $githubToken -eq "") {
    Write-Host "❌ GitHub token not configured!" -ForegroundColor Red
    Write-Host "`nPlease update .env.local with your GitHub Personal Access Token:" -ForegroundColor Yellow
    Write-Host "1. Go to: https://github.com/settings/tokens" -ForegroundColor White
    Write-Host "2. Click 'Generate new token (classic)'" -ForegroundColor White
    Write-Host "3. Give it a name (e.g., 'AI Construction Push')" -ForegroundColor White
    Write-Host "4. Select 'repo' scope for full access" -ForegroundColor White
    Write-Host "5. Generate token and copy it" -ForegroundColor White
    Write-Host "6. Update GITHUB_TOKEN in .env.local" -ForegroundColor White
    Write-Host "7. Update GITHUB_EMAIL in .env.local with your email" -ForegroundColor White
    exit 1
}

Write-Host "✅ Found GitHub configuration" -ForegroundColor Green
Write-Host "   Username: $githubUsername" -ForegroundColor Gray
Write-Host "   Email: $githubEmail" -ForegroundColor Gray

# Configure git user
Write-Host "`n📝 Configuring git user..." -ForegroundColor Cyan
git config user.name $githubUsername
git config user.email $githubEmail

# Check if repository is initialized
if (!(Test-Path ".git")) {
    Write-Host "`n📁 Initializing git repository..." -ForegroundColor Cyan
    git init
    git checkout -b main
}

# Create authenticated remote URL
$remoteUrl = "https://${githubUsername}:${githubToken}@github.com/mikeaper323/AI-Construction.git"

# Check if remote exists
$remotes = git remote
if ($remotes -contains "origin") {
    Write-Host "`n🔗 Updating remote origin with authentication..." -ForegroundColor Cyan
    git remote set-url origin $remoteUrl
} else {
    Write-Host "`n🔗 Adding remote origin with authentication..." -ForegroundColor Cyan
    git remote add origin $remoteUrl
}

# Push to GitHub
Write-Host "`n🚀 Pushing to GitHub..." -ForegroundColor Yellow
git push -u origin main

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n✅ Successfully pushed to GitHub!" -ForegroundColor Green
    Write-Host "View your repository at: https://github.com/mikeaper323/AI-Construction" -ForegroundColor Cyan
    
    # Clean up by removing token from remote URL for security
    Write-Host "`n🔒 Cleaning up authentication..." -ForegroundColor Gray
    git remote set-url origin https://github.com/mikeaper323/AI-Construction.git
} else {
    Write-Host "`n❌ Push failed!" -ForegroundColor Red
    Write-Host "Check your token permissions and try again." -ForegroundColor Yellow
    
    # Clean up token from URL for security
    git remote set-url origin https://github.com/mikeaper323/AI-Construction.git
}
