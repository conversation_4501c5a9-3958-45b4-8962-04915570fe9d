// 3D Cursor visualization component for BIM viewer

'use client'

import React, { useRef, useEffect } from 'react'
import { extend, useFrame, useThree } from '@react-three/fiber'
import { Text } from '@react-three/drei'
import * as THREE from 'three'
import { Cursor3D, UserPresence } from '../types'

// Extend Three.js objects
extend({ 
  ConeGeometry: THREE.ConeGeometry, 
  MeshBasicMaterial: THREE.MeshBasicMaterial,
  Group: THREE.Group
})

interface CursorVisualizationProps {
  cursor: Cursor3D
  user: UserPresence
  visible: boolean
}

export function CursorVisualization({ cursor, user, visible }: CursorVisualizationProps) {
  const groupRef = useRef<THREE.Group>(null)
  const coneRef = useRef<THREE.Mesh>(null)
  const { camera } = useThree()

  // Update position
  useEffect(() => {
    if (groupRef.current && cursor.position) {
      groupRef.current.position.set(
        cursor.position.x,
        cursor.position.y,
        cursor.position.z
      )
    }
  }, [cursor.position])

  // Update rotation based on direction
  useEffect(() => {
    if (coneRef.current && cursor.direction) {
      const direction = new THREE.Vector3(
        cursor.direction.x,
        cursor.direction.y,
        cursor.direction.z
      ).normalize()

      // Create rotation from direction
      const quaternion = new THREE.Quaternion()
      quaternion.setFromUnitVectors(
        new THREE.Vector3(0, -1, 0), // Cone points down by default
        direction
      )
      
      coneRef.current.quaternion.copy(quaternion)
    }
  }, [cursor.direction])

  // Make text face camera
  useFrame(() => {
    if (groupRef.current) {
      const textMesh = groupRef.current.children.find(
        child => child.userData.isText
      )
      if (textMesh) {
        textMesh.lookAt(camera.position)
      }
    }
  })

  if (!visible) return null

  return (
    <group ref={groupRef}>
      {/* Cursor cone */}
      <mesh ref={coneRef}>
        <coneGeometry args={[0.2, 0.5, 8]} />
        <meshBasicMaterial 
          color={user.color} 
          opacity={0.8} 
          transparent 
        />
      </mesh>

      {/* User name label */}
      <Text
        position={[0, 0.8, 0]}
        fontSize={0.3}
        color={user.color}
        anchorX="center"
        anchorY="middle"
        userData={{ isText: true }}
      >
        {user.userName}
      </Text>

      {/* Selection ring */}
      <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, -0.25, 0]}>
        <ringGeometry args={[0.3, 0.35, 32]} />
        <meshBasicMaterial 
          color={user.color} 
          opacity={0.5} 
          transparent 
        />
      </mesh>
    </group>
  )
}

interface CursorsContainerProps {
  cursors: Cursor3D[]
  users: UserPresence[]
  visible: boolean
}

export function CursorsContainer({ cursors, users, visible }: CursorsContainerProps) {
  const getUserForCursor = (cursor: Cursor3D) => {
    return users.find(u => u.userId === cursor.userId)
  }

  return (
    <>
      {cursors.map(cursor => {
        const user = getUserForCursor(cursor)
        if (!user) return null

        return (
          <CursorVisualization
            key={cursor.userId}
            cursor={cursor}
            user={user}
            visible={visible}
          />
        )
      })}
    </>
  )
}