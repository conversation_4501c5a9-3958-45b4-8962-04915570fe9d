import { NextRequest, NextResponse } from 'next/server'
import { geminiService } from '@/lib/gemini'
import type { Project, SafetyMetrics, Schedule, Task } from '@/types'
import type { CompanyType } from '@/lib/company-types'

// Mock data for demonstration - in production, this would come from a database
const mockProjectData: Project = {
  id: 'project-1',
  name: 'Downtown Tower',
  description: 'A 45-story mixed-use tower in downtown',
  projectType: 'commercial',
  status: 'active',
  startDate: new Date('2024-01-15'),
  endDate: new Date('2025-06-30'),
  budget: *********,
  actualCost: 78000000,
  location: '123 Main Street, Los Angeles, CA 90001',
  userId: 'user-1',
  progressPercentage: 67,
  riskScore: 25,
  safetyScore: 94,
  createdAt: new Date('2023-12-01'),
  updatedAt: new Date('2024-06-20')
}

const mockSafetyMetrics: SafetyMetrics = {
  projectId: 'project-1',
  totalIncidents: 3,
  lostTimeIncidents: 0,
  nearMisses: 12,
  safetyScore: 94,
  ppeCompliance: 96,
  hazardsIdentified: 45,
  hazardsResolved: 42,
  trainingCompletion: 89,
  lastUpdated: new Date()
}

const mockScheduleData: Schedule = {
  id: 'schedule-1',
  projectId: 'project-1',
  taskName: 'Main Construction Schedule',
  description: 'Primary construction schedule v3',
  startDate: new Date('2024-01-15'),
  endDate: new Date('2025-06-30'),
  status: 'IN_PROGRESS',
  priority: 'HIGH',
  dependencies: '',
  assignedTo: 'Project Manager',
  createdAt: new Date('2023-12-01'),
  updatedAt: new Date('2024-06-20')
}

const mockTasks: Task[] = [
    {
      id: 'task-1',
      scheduleId: 'schedule-1',
      name: 'Foundation Work',
      type: 'work_package',
      status: 'completed',
      plannedStartDate: new Date('2024-01-15'),
      plannedEndDate: new Date('2024-03-15'),
      actualStartDate: new Date('2024-01-15'),
      actualEndDate: new Date('2024-03-10'),
      duration: 60,
      progress: 100,
      dependencies: [],
      resources: [],
      constraints: [],
      riskFactors: []
    },
    {
      id: 'task-2',
      scheduleId: 'schedule-1',
      name: 'Structural Steel',
      type: 'work_package',
      status: 'in_progress',
      plannedStartDate: new Date('2024-03-16'),
      plannedEndDate: new Date('2024-07-30'),
      actualStartDate: new Date('2024-03-16'),
      duration: 137,
      progress: 78,
      dependencies: ['task-1'],
      resources: [],
      constraints: [],
      riskFactors: []
    },
    {
      id: 'task-3',
      scheduleId: 'schedule-1',
      name: 'MEP Installation',
      type: 'work_package',
      status: 'not_started',
      plannedStartDate: new Date('2024-08-01'),
      plannedEndDate: new Date('2024-12-15'),
      duration: 136,
      progress: 0,
      dependencies: ['task-2'],
      resources: [],
      constraints: [],
      riskFactors: []
    }
  ]

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, userId = 'default-user', includeContext = true, companyType } = body

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    // Prepare context if requested
    let context = undefined
    if (includeContext) {
      context = {
        projectData: mockProjectData,
        safetyMetrics: mockSafetyMetrics,
        scheduleData: mockScheduleData,
        companyType: companyType as CompanyType | null
      }
    } else if (companyType) {
      context = {
        companyType: companyType as CompanyType | null
      }
    }

    // Get response from Gemini
    const response = await geminiService.sendMessage(message, userId, context)

    return NextResponse.json({
      success: true,
      data: {
        message: response,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('AI API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to process AI request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle analysis requests
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { analysisType, data, companyType } = body

    if (!analysisType || !data) {
      return NextResponse.json(
        { error: 'Analysis type and data are required' },
        { status: 400 }
      )
    }

    const validTypes = ['progress', 'safety', 'schedule', 'cost', 'quality']
    if (!validTypes.includes(analysisType)) {
      return NextResponse.json(
        { error: 'Invalid analysis type' },
        { status: 400 }
      )
    }

    const result = await geminiService.analyzeConstructionData(
      analysisType as any,
      data,
      companyType as CompanyType | null
    )

    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Analysis API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to analyze data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle content generation requests
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { contentType, data, companyType } = body

    if (!contentType || !data) {
      return NextResponse.json(
        { error: 'Content type and data are required' },
        { status: 400 }
      )
    }

    const validTypes = ['safety_report', 'progress_report', 'risk_assessment', 'schedule_optimization']
    if (!validTypes.includes(contentType)) {
      return NextResponse.json(
        { error: 'Invalid content type' },
        { status: 400 }
      )
    }

    const result = await geminiService.generateContent(
      contentType as any,
      data,
      companyType as CompanyType | null
    )

    return NextResponse.json({
      success: true,
      data: {
        content: result,
        type: contentType,
        generatedAt: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('Content generation API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to generate content',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
