/**
 * Logger wrapper that handles ESM/CommonJS compatibility issues
 * This wrapper ensures the logger works in both client and server environments
 */

let fs: any
let path: any

// Only import Node.js modules on the server
if (typeof window === 'undefined') {
  fs = require('fs')
  path = require('path')
}

// Simple console-based logger for API routes with file writing
class SimpleLogger {
  private service: string
  private logDir: string

  constructor(service: string) {
    this.service = service
    
    // Setup log directory
    if (typeof window === 'undefined' && path) {
      this.logDir = path.join(process.cwd(), 'logs', service)
      this.ensureLogDirectory()
    } else {
      this.logDir = ''
    }
  }

  private ensureLogDirectory() {
    try {
      if (fs && !fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true })
      }
    } catch (error) {
      console.error(`Failed to create log directory: ${error}`)
    }
  }

  private writeToFile(level: string, message: string, metadata?: any) {
    if (!this.logDir || typeof window !== 'undefined' || !fs || !path) return
    
    try {
      const date = new Date()
      const dateStr = date.toISOString().split('T')[0]
      const logFile = path.join(this.logDir, `combined-${dateStr}.log`)
      
      const logEntry = {
        timestamp: date.toISOString(),
        level,
        service: this.service,
        message,
        ...metadata
      }
      
      fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n')
      
      // Also write errors to error log
      if (level === 'error') {
        const errorFile = path.join(this.logDir, `error-${dateStr}.log`)
        fs.appendFileSync(errorFile, JSON.stringify(logEntry) + '\n')
      }
    } catch (error) {
      // Silently fail - don't break the app if logging fails
    }
  }

  private log(level: string, message: string, metadata?: any) {
    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      level,
      service: this.service,
      message,
      ...metadata
    }

    // Format the log message with colors and emojis for better visibility
    const prefix = `[${timestamp}] [${this.service.toUpperCase()}]`
    const levelEmoji = {
      error: '❌',
      warn: '⚠️',
      info: '✅',
      debug: '🔍',
      http: '🌐'
    }[level] || '📝'
    
    const formattedMessage = `${levelEmoji} ${prefix} ${level.toUpperCase()}: ${message}`
    
    // Include metadata if present
    const metadataStr = metadata && Object.keys(metadata).length > 0 
      ? ` | ${JSON.stringify(metadata)}` 
      : ''

    if (level === 'error') {
      console.error(formattedMessage + metadataStr)
    } else if (level === 'warn') {
      console.warn(formattedMessage + metadataStr)
    } else if (level === 'debug' && process.env.NODE_ENV === 'development') {
      console.debug(formattedMessage + metadataStr)
    } else {
      console.log(formattedMessage + metadataStr)
    }
    
    // Also write to file
    this.writeToFile(level, message, metadata)
  }

  info(message: string, metadata?: any) {
    this.log('info', message, metadata)
  }

  error(message: string, metadata?: any) {
    this.log('error', message, metadata)
  }

  warn(message: string, metadata?: any) {
    this.log('warn', message, metadata)
  }

  debug(message: string, metadata?: any) {
    this.log('debug', message, metadata)
  }

  http(message: string, metadata?: any) {
    this.log('http', message, metadata)
  }
}

// Logger factory that returns appropriate logger based on environment
export function createLogger(service: string) {
  // In API routes, use simple logger to avoid ESM issues
  if (typeof window === 'undefined' && process.env.NODE_ENV !== 'test') {
    return new SimpleLogger(service)
  }
  
  // For client-side or test environments, return simple logger as well
  // The full winston logger with chalk can be loaded separately if needed
  return new SimpleLogger(service)
}

// Export convenience loggers
export const visionLogger = createLogger('vision')
export const takeoffLogger = createLogger('takeoff')
export const geminiLogger = createLogger('gemini')
export const apiLogger = createLogger('api')

// Helper functions that work with simple logger
export function logProcessingStep(
  logger: any,
  step: string,
  details: any = {},
  startTime?: number
) {
  const duration = startTime ? Date.now() - startTime : undefined
  logger.info(`Processing step: ${step}`, {
    ...details,
    duration: duration ? `${duration}ms` : undefined,
  })
}

export function logError(
  logger: any,
  error: Error | unknown,
  context: string,
  details: any = {}
) {
  if (error instanceof Error) {
    logger.error(`Error in ${context}: ${error.message}`, {
      ...details,
      stack: error.stack,
      name: error.name,
    })
  } else {
    logger.error(`Error in ${context}`, {
      ...details,
      error: String(error),
    })
  }
}

export function logApiRequest(
  method: string,
  path: string,
  details: any = {}
) {
  apiLogger.http(`${method} ${path}`, {
    ...details,
    timestamp: new Date().toISOString(),
  })
}

export function logApiResponse(
  method: string,
  path: string,
  status: number,
  duration: number,
  details: any = {}
) {
  const level = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'info'
  const logger = apiLogger as any
  logger[level](`${method} ${path} ${status} ${duration}ms`, {
    ...details,
    status,
    duration,
  })
}