#!/usr/bin/env pwsh

# AI Construction Management - Build Verification Script
# This script checks if the app builds without errors after fixing the CompanyType export issue

Write-Host "========================================" -ForegroundColor Cyan
Write-Host " AI Construction Management Build Check" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Host "[ERROR] Please run this script from the project root directory" -ForegroundColor Red
    exit 1
}

# Step 1: Check if node_modules exists
Write-Host "[1/5] Checking dependencies..." -ForegroundColor Yellow
if (-not (Test-Path "node_modules")) {
    Write-Host "   Installing dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] Failed to install dependencies" -ForegroundColor Red
        exit 1
    }
}
Write-Host "   ✓ Dependencies installed" -ForegroundColor Green

# Step 2: Check TypeScript compilation
Write-Host "[2/5] Running TypeScript check..." -ForegroundColor Yellow
npm run type-check 2>&1 | Out-String | ForEach-Object {
    if ($_ -match "error") {
        Write-Host $_ -ForegroundColor Red
    } else {
        Write-Host $_ -ForegroundColor Gray
    }
}
if ($LASTEXITCODE -ne 0) {
    Write-Host "[ERROR] TypeScript compilation failed" -ForegroundColor Red
    exit 1
}
Write-Host "   ✓ TypeScript check passed" -ForegroundColor Green

# Step 3: Check ESLint
Write-Host "[3/5] Running ESLint..." -ForegroundColor Yellow
npm run lint 2>&1 | Out-String | ForEach-Object {
    if ($_ -match "error") {
        Write-Host $_ -ForegroundColor Red
    } else {
        Write-Host $_ -ForegroundColor Gray
    }
}
# Note: We don't exit on lint errors as they might be warnings

# Step 4: Try to build the project
Write-Host "[4/5] Building the project..." -ForegroundColor Yellow
npm run build 2>&1 | Tee-Object -Variable buildOutput | Out-Null
if ($LASTEXITCODE -ne 0) {
    Write-Host "[ERROR] Build failed:" -ForegroundColor Red
    Write-Host $buildOutput -ForegroundColor Red
    exit 1
}
Write-Host "   ✓ Build completed successfully" -ForegroundColor Green

# Step 5: Check for common issues
Write-Host "[5/5] Checking for common issues..." -ForegroundColor Yellow

# Check if .env.local exists
if (-not (Test-Path ".env.local")) {
    Write-Host "   ⚠ Warning: .env.local not found. Copy .env.example to .env.local and add your API keys" -ForegroundColor Yellow
} else {
    Write-Host "   ✓ Environment file exists" -ForegroundColor Green
}

# Check if the CompanyContext fix is applied
$contextFile = Get-Content "src/contexts/CompanyContext.tsx" -Raw
if ($contextFile -match "export { CompanyType }") {
    Write-Host "   ⚠ Warning: Found re-export of CompanyType that might cause issues" -ForegroundColor Yellow
} else {
    Write-Host "   ✓ CompanyType export issue fixed" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " ✅ Build verification complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run 'npm run dev' to start the development server" -ForegroundColor White
Write-Host "2. Visit http://localhost:3000" -ForegroundColor White
Write-Host ""
