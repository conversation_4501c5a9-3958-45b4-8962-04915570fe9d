# Environment Variables

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/ai_construction_db"

# Authentication
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN="7d"

# Google Gemini API
GEMINI_API_KEY="your-gemini-api-key"
GEMINI_MODEL="gemini-2.0-flash"

# Mapbox
NEXT_PUBLIC_MAPBOX_TOKEN="your-mapbox-token"

# AWS S3 (for file storage)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-west-2"
AWS_S3_BUCKET="ai-construction-uploads"

# Socket.io
NEXT_PUBLIC_SOCKET_URL="http://localhost:3001"

# External APIs
WEATHER_API_KEY="your-weather-api-key"
BIM_API_ENDPOINT="https://api.bim-provider.com/v1"
BIM_API_KEY="your-bim-api-key"
GOOGLE_CUSTOM_SEARCH_API_KEY="your-google-custom-search-api-key"
GOOGLE_CUSTOM_SEARCH_ENGINE_ID="your-search-engine-id"

# Email Service
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Monitoring
SENTRY_DSN="your-sentry-dsn"

# Feature Flags
ENABLE_AI_ASSISTANT="true"
ENABLE_ROBOTICS="true"
ENABLE_AR_FEATURES="true"

# Development
NODE_ENV="development"
