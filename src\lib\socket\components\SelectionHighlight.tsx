// Selection highlight component for BIM elements

'use client'

import React, { useEffect, useRef } from 'react'
import { useThree } from '@react-three/fiber'
import * as THREE from 'three'
import { ElementSelection } from '../types'

interface SelectionHighlightProps {
  selection: ElementSelection
  color: string
  visible: boolean
  scene: THREE.Scene
}

export function SelectionHighlight({ 
  selection, 
  color, 
  visible,
  scene 
}: SelectionHighlightProps) {
  const { gl, camera } = useThree()
  const outlinePassRef = useRef<any>(null)
  const selectedObjectsRef = useRef<THREE.Object3D[]>([])

  useEffect(() => {
    if (!visible) {
      // Clear selection
      if (outlinePassRef.current) {
        outlinePassRef.current.selectedObjects = []
      }
      return
    }

    // Find objects by element IDs
    const selectedObjects: THREE.Object3D[] = []
    
    selection.elementIds.forEach(elementId => {
      scene.traverse((child) => {
        if (child.userData.elementId === elementId || child.name === elementId) {
          selectedObjects.push(child)
        }
      })
    })

    selectedObjectsRef.current = selectedObjects

    // Create outline effect if not exists
    if (!outlinePassRef.current && selectedObjects.length > 0) {
      // Dynamic import to avoid SSR issues
      import('three/examples/jsm/postprocessing/OutlinePass.js').then(({ OutlinePass }) => {
        import('three/examples/jsm/postprocessing/EffectComposer.js').then(({ EffectComposer }) => {
          import('three/examples/jsm/postprocessing/RenderPass.js').then(({ RenderPass }) => {
            const composer = new EffectComposer(gl)
            const renderPass = new RenderPass(scene, camera)
            composer.addPass(renderPass)

            const outlinePass = new OutlinePass(
              new THREE.Vector2(window.innerWidth, window.innerHeight),
              scene,
              camera
            )
            
            outlinePass.edgeStrength = 3
            outlinePass.edgeGlow = 1
            outlinePass.edgeThickness = 2
            outlinePass.pulsePeriod = 2
            
            const outlineColor = new THREE.Color(color)
            outlinePass.visibleEdgeColor.set(outlineColor)
            outlinePass.hiddenEdgeColor.set(outlineColor)
            
            outlinePass.selectedObjects = selectedObjects
            
            composer.addPass(outlinePass)
            outlinePassRef.current = outlinePass
          })
        })
      })
    } else if (outlinePassRef.current) {
      // Update existing outline
      outlinePassRef.current.selectedObjects = selectedObjects
      const outlineColor = new THREE.Color(color)
      outlinePassRef.current.visibleEdgeColor.set(outlineColor)
      outlinePassRef.current.hiddenEdgeColor.set(outlineColor)
    }

    // Also add temporary emissive material for better visibility
    selectedObjects.forEach(obj => {
      if (obj instanceof THREE.Mesh && obj.material) {
        const material = obj.material as THREE.MeshStandardMaterial
        if (material.emissive) {
          material.emissive = new THREE.Color(color)
          material.emissiveIntensity = 0.3
        }
      }
    })

    // Cleanup function
    return () => {
      selectedObjectsRef.current.forEach(obj => {
        if (obj instanceof THREE.Mesh && obj.material) {
          const material = obj.material as THREE.MeshStandardMaterial
          if (material.emissive) {
            material.emissive = new THREE.Color(0x000000)
            material.emissiveIntensity = 0
          }
        }
      })
    }
  }, [selection, color, visible, scene, gl, camera])

  return null
}

interface SelectionsContainerProps {
  selections: ElementSelection[]
  getUserColor: (userId: string) => string
  visible: boolean
  scene: THREE.Scene
}

export function SelectionsContainer({
  selections,
  getUserColor,
  visible,
  scene
}: SelectionsContainerProps) {
  return (
    <>
      {selections.map(selection => (
        <SelectionHighlight
          key={selection.userId}
          selection={selection}
          color={getUserColor(selection.userId)}
          visible={visible}
          scene={scene}
        />
      ))}
    </>
  )
}

// Alternative approach using shader material for outline effect
export class SelectionOutlineMaterial extends THREE.ShaderMaterial {
  constructor(color: string = '#ffffff', thickness: number = 0.02) {
    super({
      uniforms: {
        outlineColor: { value: new THREE.Color(color) },
        thickness: { value: thickness }
      },
      vertexShader: `
        uniform float thickness;
        void main() {
          vec3 newPosition = position + normal * thickness;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
        }
      `,
      fragmentShader: `
        uniform vec3 outlineColor;
        void main() {
          gl_FragColor = vec4(outlineColor, 1.0);
        }
      `,
      side: THREE.BackSide
    })
  }
}