#!/usr/bin/env pwsh
# Quick type check script

Write-Host "🔍 Running TypeScript type check..." -ForegroundColor Yellow

# Run type check
Write-Host "`nChecking types..." -ForegroundColor Cyan
npm run type-check

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n✅ No TypeScript errors found!" -ForegroundColor Green
} else {
    Write-Host "`n❌ TypeScript errors found. Please fix them before proceeding." -ForegroundColor Red
}

Write-Host "`n🔍 Running ESLint..." -ForegroundColor Yellow
npm run lint

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n✅ No linting errors found!" -ForegroundColor Green
} else {
    Write-Host "`n❌ Linting errors found. Please fix them before proceeding." -ForegroundColor Red
}
