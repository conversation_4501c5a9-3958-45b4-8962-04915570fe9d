
'use client'

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { motion } from 'framer-motion';
import {
  Plus,
  Search,
  Filter,
  Building2,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
} from 'lucide-react';
import Link from 'next/link';
import type { Project, ProjectStatus, ProjectType } from '@/types';

// Define a more specific type for the data used in this component
// This makes the component more robust and avoids over-fetching data
type ProjectListItem = {
  id: string;
  name: string;
  description: string | null;
  status: ProjectStatus;
};

const statusColors: Record<ProjectStatus, string> = {
  planning: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
  active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  on_hold: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  completed: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
};

const typeIcons: Record<ProjectType, typeof Building2> = {
  commercial: Building2,
  residential: Building2,
  infrastructure: Building2,
  industrial: Building2,
  institutional: Building2,
};

async function fetchProjects(filters: { status?: string, search?: string, limit?: number, offset?: number }): Promise<{ projects: ProjectListItem[], total: number }> {
  const { data } = await axios.get('/api/projects', { params: filters });
  return data;
}

export default function ProjectsClient({ projects: initialProjects }: { projects: ProjectListItem[] }) {
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState('');

  const { data, isLoading, error } = useQuery({
    queryKey: ['projects', { search, status }],
    queryFn: () => fetchProjects({ search, status }),
    initialData: { projects: initialProjects, total: initialProjects.length },
  });

  const projects = data?.projects || [];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Projects</h1>
        <Link href="/dashboard/projects/new">
          <button className="bg-blue-500 text-white px-4 py-2 rounded-md flex items-center">
            <Plus className="mr-2" />
            New Project
          </button>
        </Link>
      </div>

      <div className="flex space-x-4">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search projects..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border rounded-md"
          />
        </div>
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            className="pl-10 pr-4 py-2 border rounded-md appearance-none"
          >
            <option value="">All Statuses</option>
            <option value="planning">Planning</option>
            <option value="active">Active</option>
            <option value="on_hold">On Hold</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {isLoading && <p>Loading...</p>}
      {error && <p className="text-red-500">Error loading projects.</p>}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.map((project) => (
          <motion.div
            key={project.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
          >
            <div className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[project.status]}`}>
                    {project.status.replace('_', ' ')}
                  </div>
                  <h2 className="text-xl font-bold mt-2">{project.name}</h2>
                </div>
                <MoreVertical className="text-gray-400" />
              </div>
              <p className="text-gray-600 dark:text-gray-400 mt-2">{project.description}</p>
            </div>
            <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 flex justify-end space-x-2">
              <Link href={`/dashboard/projects/${project.id}`}>
                <button className="text-gray-500 hover:text-gray-700">
                  <Eye size={20} />
                </button>
              </Link>
              <Link href={`/dashboard/projects/${project.id}/edit`}>
                <button className="text-gray-500 hover:text-gray-700">
                  <Edit size={20} />
                </button>
              </Link>
              <button className="text-red-500 hover:text-red-700">
                <Trash2 size={20} />
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
