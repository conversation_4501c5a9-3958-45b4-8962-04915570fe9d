// Socket.io client service for BIM collaboration

import { io, Socket } from 'socket.io-client'
import { 
  ClientToServerEvents, 
  ServerToClientEvents,
  UserPresence,
  Cursor3D,
  ElementSelection,
  Annotation,
  ModelChange,
  CollaborationRoom,
  SocketEvents,
  SocketError,
  ViewStateChange,
  AnnotationReply
} from './types'
import { BIMViewerState } from '@/types'

export type SocketClient = Socket<ServerToClientEvents, ClientToServerEvents>

export interface CollaborationCallbacks {
  onRoomJoined?: (room: CollaborationRoom) => void
  onRoomLeft?: (roomId: string, userId: string) => void
  onUserJoined?: (user: UserPresence) => void
  onUserLeft?: (userId: string) => void
  onUsersUpdate?: (users: UserPresence[]) => void
  onCursorUpdate?: (cursors: Cursor3D[]) => void
  onSelectionUpdate?: (selection: ElementSelection) => void
  onSelectionClear?: (userId: string) => void
  onAnnotationsSync?: (annotations: Annotation[]) => void
  onModelSync?: (changes: ModelChange[]) => void
  onModelConflict?: (conflict: ModelChange, resolution?: ModelChange) => void
  onViewStateSync?: (viewStates: Record<string, BIMViewerState>) => void
  onError?: (error: SocketError) => void
}

export class BIMCollaborationClient {
  private socket: SocketClient | null = null
  private callbacks: CollaborationCallbacks = {}
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private currentRoom: string | null = null
  private userId: string | null = null

  constructor(private serverUrl?: string) {
    this.serverUrl = serverUrl || process.env.NEXT_PUBLIC_SOCKET_URL || ''
  }

  // Connect to Socket.io server
  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve()
        return
      }

      this.socket = io(this.serverUrl, {
        transports: ['websocket', 'polling'],
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay
      })

      this.setupEventListeners()

      this.socket.on('connect', () => {
        console.log('Connected to collaboration server')
        this.reconnectAttempts = 0
        resolve()
      })

      this.socket.on('connect_error', (error) => {
        console.error('Connection error:', error)
        this.reconnectAttempts++
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          reject(new Error('Failed to connect to collaboration server'))
        }
      })
    })
  }

  // Disconnect from server
  public disconnect(): void {
    if (this.currentRoom && this.userId) {
      this.leaveRoom(this.currentRoom, this.userId)
    }
    this.socket?.disconnect()
    this.socket = null
    this.currentRoom = null
    this.userId = null
  }

  // Set callbacks
  public setCallbacks(callbacks: CollaborationCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks }
  }

  // Join a collaboration room
  public joinRoom(roomId: string, userId: string, userName: string, userRole: string): void {
    if (!this.socket?.connected) {
      console.error('Socket not connected')
      return
    }

    this.currentRoom = roomId
    this.userId = userId

    this.socket.emit(SocketEvents.JOIN_ROOM, {
      roomId,
      userId,
      userName,
      userRole
    })
  }

  // Leave current room
  public leaveRoom(roomId: string, userId: string): void {
    if (!this.socket?.connected) return

    this.socket.emit(SocketEvents.LEAVE_ROOM, { roomId, userId })
    this.currentRoom = null
  }

  // Send cursor position
  public sendCursorPosition(cursor: Cursor3D): void {
    if (!this.socket?.connected || !this.currentRoom) return
    this.socket.emit(SocketEvents.CURSOR_MOVE, cursor)
  }

  // Select elements
  public selectElements(elementIds: string[], mode: 'single' | 'multiple' | 'box' = 'single'): void {
    if (!this.socket?.connected || !this.userId) return

    const selection: ElementSelection = {
      userId: this.userId,
      elementIds,
      mode,
      timestamp: new Date()
    }

    this.socket.emit(SocketEvents.ELEMENT_SELECT, selection)
  }

  // Deselect elements
  public deselectElements(elementIds: string[]): void {
    if (!this.socket?.connected || !this.userId) return
    this.socket.emit(SocketEvents.ELEMENT_DESELECT, { userId: this.userId, elementIds })
  }

  // Create annotation
  public createAnnotation(
    annotation: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'>
  ): void {
    if (!this.socket?.connected) return
    this.socket.emit(SocketEvents.ANNOTATION_CREATE, annotation)
  }

  // Update annotation
  public updateAnnotation(id: string, updates: Partial<Annotation>): void {
    if (!this.socket?.connected) return
    this.socket.emit(SocketEvents.ANNOTATION_UPDATE, { id, updates })
  }

  // Delete annotation
  public deleteAnnotation(id: string): void {
    if (!this.socket?.connected || !this.userId) return
    this.socket.emit(SocketEvents.ANNOTATION_DELETE, { id, userId: this.userId })
  }

  // Reply to annotation
  public replyToAnnotation(annotationId: string, content: string): void {
    if (!this.socket?.connected || !this.userId) return

    const reply: Omit<AnnotationReply, 'id' | 'createdAt'> = {
      userId: this.userId,
      userName: '', // This should be provided by the caller
      content
    }

    this.socket.emit(SocketEvents.ANNOTATION_REPLY, { annotationId, reply })
  }

  // Send model change
  public sendModelChange(change: Omit<ModelChange, 'id' | 'timestamp'>): void {
    if (!this.socket?.connected) return
    this.socket.emit(SocketEvents.MODEL_CHANGE, change)
  }

  // Update view state
  public updateViewState(viewState: Partial<BIMViewerState>): void {
    if (!this.socket?.connected || !this.userId) return

    const change: ViewStateChange = {
      userId: this.userId,
      viewState,
      timestamp: new Date()
    }

    this.socket.emit(SocketEvents.VIEW_STATE_CHANGE, change)
  }

  // Check if connected
  public isConnected(): boolean {
    return this.socket?.connected || false
  }

  // Get current room
  public getCurrentRoom(): string | null {
    return this.currentRoom
  }

  // Setup event listeners
  private setupEventListeners(): void {
    if (!this.socket) return

    // Room events
    this.socket.on(SocketEvents.ROOM_JOINED, ({ room }) => {
      this.callbacks.onRoomJoined?.(room)
    })

    this.socket.on(SocketEvents.ROOM_LEFT, ({ roomId, userId }) => {
      this.callbacks.onRoomLeft?.(roomId, userId)
    })

    // User presence events
    this.socket.on(SocketEvents.USER_JOINED, ({ user }) => {
      this.callbacks.onUserJoined?.(user)
    })

    this.socket.on(SocketEvents.USER_LEFT, ({ userId }) => {
      this.callbacks.onUserLeft?.(userId)
    })

    this.socket.on(SocketEvents.USERS_LIST, ({ users }) => {
      this.callbacks.onUsersUpdate?.(users)
    })

    // Cursor events
    this.socket.on(SocketEvents.CURSOR_UPDATE, ({ cursors }) => {
      this.callbacks.onCursorUpdate?.(cursors)
    })

    // Selection events
    this.socket.on(SocketEvents.SELECTION_UPDATE, ({ selection }) => {
      this.callbacks.onSelectionUpdate?.(selection)
    })

    this.socket.on(SocketEvents.SELECTION_CLEAR, ({ userId }) => {
      this.callbacks.onSelectionClear?.(userId)
    })

    // Annotation events
    this.socket.on(SocketEvents.ANNOTATIONS_SYNC, ({ annotations }) => {
      this.callbacks.onAnnotationsSync?.(annotations)
    })

    // Model events
    this.socket.on(SocketEvents.MODEL_SYNC, ({ changes }) => {
      this.callbacks.onModelSync?.(changes)
    })

    this.socket.on(SocketEvents.MODEL_CONFLICT, ({ conflict, resolution }) => {
      this.callbacks.onModelConflict?.(conflict, resolution)
    })

    // View state events
    this.socket.on(SocketEvents.VIEW_STATE_SYNC, ({ viewStates }) => {
      this.callbacks.onViewStateSync?.(viewStates)
    })

    // Error events
    this.socket.on(SocketEvents.ERROR, (error) => {
      console.error('Socket error:', error)
      this.callbacks.onError?.(error)
    })

    // Connection events
    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from collaboration server:', reason)
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, attempt to reconnect
        this.socket?.connect()
      }
    })

    this.socket.on('reconnect' as any, (attemptNumber: number) => {
      console.log(`Reconnected after ${attemptNumber} attempts`)
      // Rejoin room if was in one
      if (this.currentRoom && this.userId) {
        // The component using this should handle rejoining with proper user info
      }
    })
  }
}

// Singleton instance
let collaborationClient: BIMCollaborationClient | null = null

export function getCollaborationClient(serverUrl?: string): BIMCollaborationClient {
  if (!collaborationClient) {
    collaborationClient = new BIMCollaborationClient(serverUrl)
  }
  return collaborationClient
}

// Cleanup function
export function cleanupCollaborationClient(): void {
  if (collaborationClient) {
    collaborationClient.disconnect()
    collaborationClient = null
  }
}