import { NextResponse } from 'next/server';
import { metrics } from '@/lib/monitoring';

export async function GET() {
  try {
    // Export metrics in Prometheus format
    const metricsData = metrics.exportMetrics();
    
    return new NextResponse(metricsData, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; version=0.0.4',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    console.error('Failed to export metrics:', error);
    return NextResponse.json(
      { error: 'Failed to export metrics' },
      { status: 500 }
    );
  }
}