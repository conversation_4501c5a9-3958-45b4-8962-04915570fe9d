'use client'

import { motion } from 'framer-motion'
import { Building2 } from 'lucide-react'
import Link from 'next/link'
import type { User } from '@prisma/client'

export default function DashboardClient({ user }: { user: User }) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1
    }
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Welcome back, {user.name || 'User'}!
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          You&apos;re successfully logged into the AI Construction Management platform.
        </p>
      </motion.div>

      {/* Quick Links */}
      <motion.div variants={itemVariants} className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <Link
            href="/dashboard/projects"
            className="group relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-6 hover:shadow-md transition-all"
          >
            <div className="flex items-center space-x-3">
              <div className="rounded-lg p-3 bg-blue-50 dark:bg-blue-950">
                <Building2 className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  View Projects
                </h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  Manage your construction projects
                </p>
              </div>
            </div>
          </Link>
        </div>
      </motion.div>

      {/* User Info */}
      <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Your Profile
        </h3>
        <div className="space-y-2">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            <span className="font-medium">Email:</span> {user.email}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            <span className="font-medium">Role:</span> {user.role}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            <span className="font-medium">Company:</span> {user.companyName || 'Not specified'}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            <span className="font-medium">Company Type:</span> {user.companyType}
          </p>
        </div>
      </motion.div>
    </motion.div>
  )
}