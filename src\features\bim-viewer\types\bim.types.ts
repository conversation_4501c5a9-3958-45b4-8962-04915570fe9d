// BIM Viewer specific types
import { BIMModel, BIMElement } from '@/types'

export interface ViewerConfig {
  backgroundColor: string
  gridSize: number
  gridDivisions: number
  ambientLightIntensity: number
  directionalLightIntensity: number
  enableShadows: boolean
  enablePostProcessing: boolean
}

export interface SelectionEvent {
  elements: BIMElement[]
  event: MouseEvent
  point: { x: number; y: number; z: number }
}

export interface MeasurementData {
  id: string
  type: 'distance' | 'area' | 'volume' | 'angle'
  points: Array<{ x: number; y: number; z: number }>
  value: number
  unit: string
  visible: boolean
}

export interface ViewerTools {
  select: boolean
  measure: boolean
  section: boolean
  annotate: boolean
  isolate: boolean
  explode: boolean
}

export interface ModelLoadEvent {
  model: BIMModel
  loadTime: number
  elementCount: number
  boundingBox: {
    min: { x: number; y: number; z: number }
    max: { x: number; y: number; z: number }
  }
}

export interface ViewerAnnotation {
  id: string
  elementId?: string
  position: { x: number; y: number; z: number }
  title: string
  description: string
  author: string
  type: 'issue' | 'note' | 'rfi' | 'safety'
  status: 'open' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high'
  attachments?: string[]
  createdAt: Date
  updatedAt: Date
}

export interface FloorPlan {
  id: string
  level: number
  name: string
  elevation: number
  visible: boolean
  opacity: number
}

export interface ViewerCamera {
  position: { x: number; y: number; z: number }
  target: { x: number; y: number; z: number }
  fov: number
  near: number
  far: number
}

export interface SavedView {
  id: string
  name: string
  camera: ViewerCamera
  hiddenElements: string[]
  clippingPlanes: Array<{
    normal: { x: number; y: number; z: number }
    position: { x: number; y: number; z: number }
  }>
  timestamp: Date
}

export interface ElementFilter {
  ifcType?: string[]
  floor?: number[]
  discipline?: string[]
  status?: string[]
  search?: string
}

export interface ViewerPerformance {
  fps: number
  renderTime: number
  geometryCount: number
  triangleCount: number
  drawCalls: number
  memoryUsage: number
}