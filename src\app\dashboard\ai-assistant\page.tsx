'use client'

// AI Assistant powered by Google Gemini
// This component provides an intelligent chat interface for construction project management
// Features include real-time project analysis, safety monitoring, schedule optimization, and cost analysis

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Send,
  Brain,
  Mic,
  Paperclip,
  Image as ImageIcon,
  FileText,
  Calendar,
  BarChart3,
  Shield,
  Building2,
  Calculator,
  Bot,
  Sparkles,
  ChevronRight,
  Settings,
  History,
  BookOpen,
  Lightbulb,
  Zap,
  Search,
  Target,
  AlertTriangle,
  CheckCircle2,
  Info,
  HelpCircle,
  User,
  Plus,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { AIConversation, AIMessage } from '@/types'
import { useCompany } from '@/contexts/CompanyContext'

// Mock conversation history
const mockConversations: AIConversation[] = [
  {
    id: '1',
    userId: 'user-1',
    projectId: 'project-1',
    messages: [
      {
        id: '1',
        role: 'user',
        content: 'What&apos;s the current progress on the Downtown Tower project?',
        timestamp: new Date('2024-06-20T10:00:00')
      },
      {
        id: '2',
        role: 'assistant',
        content: 'The Downtown Tower project is currently at 67% completion, which is 2% ahead of schedule. Here are the key highlights:\n\n• Overall Progress: 67% (Target: 65%)\n• Budget Utilization: 78% of $125M\n• Safety Score: 94/100\n• Active Zones: All 4 zones are active\n• Critical Path: On track with 280 days remaining\n\nThe project is performing well with no major concerns. Would you like me to provide more details on any specific aspect?',
        timestamp: new Date('2024-06-20T10:00:30')
      }
    ],
    context: {},
    createdAt: new Date('2024-06-20T10:00:00'),
    updatedAt: new Date('2024-06-20T10:00:30')
  }
]

const suggestedPrompts = [
  {
    category: 'Progress',
    icon: Building2,
    prompts: [
      'Show me progress across all active projects',
      'Which zones are falling behind schedule?',
      'Generate a progress report for this week'
    ]
  },
  {
    category: 'Safety',
    icon: Shield,
    prompts: [
      'What are the top safety risks this week?',
      'Show me AI-detected safety violations',
      'How is our safety score trending?'
    ]
  },
  {
    category: 'Scheduling',
    icon: Calendar,
    prompts: [
      'Optimize the schedule for Zone A',
      'What tasks are on the critical path?',
      'Show weather impact on schedule'
    ]
  },
  {
    category: 'Cost',
    icon: Calculator,
    prompts: [
      'Analyze cost overruns by category',
      'Where can we reduce costs?',
      'Generate a budget forecast'
    ]
  }
]

const aiCapabilities = [
  { icon: Brain, label: 'Natural Language', description: 'Ask questions in plain English' },
  { icon: Zap, label: 'Real-time Analysis', description: 'Live data from all systems' },
  { icon: Target, label: 'Predictive Insights', description: 'AI-powered forecasting' },
  { icon: Bot, label: 'Automated Actions', description: 'Execute tasks directly' }
]

export default function AIAssistantPage() {
  const [messages, setMessages] = useState<AIMessage[]>(mockConversations[0]?.messages || [])
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [showHistory, setShowHistory] = useState(false)
  const [attachments, setAttachments] = useState<File[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const { companyType } = useCompany()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSend = async () => {
    if (!inputValue.trim() && attachments.length === 0) return

    const newUserMessage: AIMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue,
      attachments: attachments.map(file => ({
        type: file.type.startsWith('image/') ? 'image' : 'document',
        url: URL.createObjectURL(file),
        name: file.name
      })),
      timestamp: new Date()
    }

    setMessages([...messages, newUserMessage])
    setInputValue('')
    setAttachments([])
    setIsTyping(true)

    try {
      // Call the Gemini API
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputValue,
          userId: 'user-1', // In production, this would come from auth
          includeContext: true,
          companyType: companyType,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get AI response')
      }

      const data = await response.json()

      const aiResponse: AIMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.data.message,
        timestamp: new Date(data.data.timestamp)
      }
      
      setMessages(prev => [...prev, aiResponse])
    } catch (error) {
      console.error('Error getting AI response:', error)
      
      // Fallback to mock response if API fails
      const aiResponse: AIMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error processing your request. Please try again later or contact support if the issue persists.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, aiResponse])
    } finally {
      setIsTyping(false)
    }
  }

  // Generate specialized reports using Gemini AI
  const generateReport = async (reportType: string) => {
    setIsTyping(true)
    
    try {
      const response = await fetch('/api/ai', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contentType: reportType,
          data: {
            projectId: 'project-1',
            dateRange: 'last_week',
            format: 'detailed'
          },
          companyType: companyType,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate report')
      }

      const data = await response.json()
      return data.data.content
    } catch (error) {
      console.error('Error generating report:', error)
      return 'Failed to generate report. Please try again.'
    } finally {
      setIsTyping(false)
    }
  }

  const handlePromptClick = (prompt: string) => {
    setInputValue(prompt)
    inputRef.current?.focus()
  }

  const handleFileAttach = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setAttachments(prev => [...prev, ...files])
  }

  return (
    <div className="flex h-[calc(100vh-10rem)]">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg mr-3">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  AI Construction Assistant
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Powered by Google Gemini AI • {companyType ? `Specialized for ${companyType}` : 'Real-time construction insights'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowHistory(!showHistory)}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  showHistory 
                    ? "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
                    : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                )}
              >
                <History className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
          {messages.length === 0 ? (
            // Empty State
            <div className="h-full flex flex-col items-center justify-center p-8">
              <div className="max-w-2xl w-full">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full mb-4">
                    <Sparkles className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Hello! I&apos;m your AI Construction Assistant
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    I can help you analyze projects, optimize schedules, monitor safety, and more.
                  </p>
                </div>

                {/* AI Capabilities */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                  {aiCapabilities.map((capability, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center"
                    >
                      <capability.icon className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {capability.label}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {capability.description}
                      </p>
                    </motion.div>
                  ))}
                </div>

                {/* Suggested Prompts */}
                <div className="space-y-4">
                  {suggestedPrompts.map((category) => (
                    <div key={category.category}>
                      <div className="flex items-center mb-2">
                        <category.icon className="w-5 h-5 text-gray-400 mr-2" />
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {category.category}
                        </h4>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        {category.prompts.map((prompt, index) => (
                          <button
                            key={index}
                            onClick={() => handlePromptClick(prompt)}
                            className="text-left p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-purple-500 dark:hover:border-purple-500 transition-colors group"
                          >
                            <p className="text-sm text-gray-700 dark:text-gray-300">
                              {prompt}
                            </p>
                            <ChevronRight className="inline w-4 h-4 text-gray-400 group-hover:text-purple-500 ml-1" />
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            // Messages
            <div className="max-w-4xl mx-auto py-6 px-4">
              {messages.map((message, index) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className={cn(
                    "mb-6 flex",
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  )}
                >
                  <div className={cn(
                    "flex max-w-[80%]",
                    message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  )}>
                    <div className={cn(
                      "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                      message.role === 'user' 
                        ? 'bg-gray-600 ml-3' 
                        : 'bg-gradient-to-r from-purple-500 to-indigo-600 mr-3'
                    )}>
                      {message.role === 'user' ? (
                        <User className="w-5 h-5 text-white" />
                      ) : (
                        <Brain className="w-5 h-5 text-white" />
                      )}
                    </div>
                    <div>
                      <div className={cn(
                        "rounded-lg px-4 py-3",
                        message.role === 'user' 
                          ? 'bg-construction-blue text-white' 
                          : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
                      )}>
                        <p className="whitespace-pre-wrap text-sm">
                          {message.content}
                        </p>
                        {message.attachments && message.attachments.length > 0 && (
                          <div className="mt-2 space-y-1">
                            {message.attachments.map((attachment, i) => (
                              <div key={i} className="flex items-center text-xs opacity-80">
                                {attachment.type === 'image' ? (
                                  <ImageIcon className="w-4 h-4 mr-1" />
                                ) : (
                                  <FileText className="w-4 h-4 mr-1" />
                                )}
                                {attachment.name}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                      <p className={cn(
                        "text-xs text-gray-500 dark:text-gray-400 mt-1",
                        message.role === 'user' ? 'text-right' : 'text-left'
                      )}>
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
              
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex items-center text-gray-500 dark:text-gray-400"
                >
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 flex items-center justify-center mr-3">
                    <Brain className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                  </div>
                </motion.div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
          {attachments.length > 0 && (
            <div className="mb-3 flex flex-wrap gap-2">
              {attachments.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-1.5 text-sm"
                >
                  {file.type.startsWith('image/') ? (
                    <ImageIcon className="w-4 h-4 mr-2 text-gray-500" />
                  ) : (
                    <FileText className="w-4 h-4 mr-2 text-gray-500" />
                  )}
                  <span className="text-gray-700 dark:text-gray-300">{file.name}</span>
                  <button
                    onClick={() => setAttachments(prev => prev.filter((_, i) => i !== index))}
                    className="ml-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
          
          <div className="flex items-end space-x-2">
            <div className="flex-1">
              <div className="relative">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSend()}
                  placeholder="Ask me anything about your construction projects..."
                  className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                  <label className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer">
                    <input
                      type="file"
                      multiple
                      onChange={handleFileAttach}
                      className="hidden"
                      accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
                    />
                    <Paperclip className="w-5 h-5" />
                  </label>
                  <button className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <Mic className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleSend}
              disabled={!inputValue.trim() && attachments.length === 0}
              className="px-4 py-3 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg shadow-sm hover:shadow-md transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="w-5 h-5" />
            </motion.button>
          </div>
          
          <div className="mt-2 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <span className="flex items-center">
                <Zap className="w-3 h-3 mr-1" />
                Real-time data
              </span>
              <span className="flex items-center">
                <Shield className="w-3 h-3 mr-1" />
                Secure & private
              </span>
            </div>
            <span>Press Enter to send, Shift+Enter for new line</span>
          </div>
        </div>
      </div>

      {/* History Sidebar */}
      <AnimatePresence>
        {showHistory && (
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: 320 }}
            exit={{ width: 0 }}
            className="bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-hidden"
          >
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Conversation History
              </h3>
            </div>
            <div className="p-4 space-y-3">
              <button className="w-full flex items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
                <Plus className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-3" />
                <span className="text-sm font-medium text-purple-600 dark:text-purple-400">
                  New Conversation
                </span>
              </button>
              
              {mockConversations.map((conv) => (
                <div
                  key={conv.id}
                  className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors"
                >
                  <p className="text-sm font-medium text-gray-900 dark:text-white line-clamp-2">
                    {conv.messages[0]?.content || 'New conversation'}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {new Date(conv.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
