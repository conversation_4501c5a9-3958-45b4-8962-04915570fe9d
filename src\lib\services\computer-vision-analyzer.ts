/**
 * Advanced Computer Vision Analyzer for Construction Drawings
 * Implements symbol detection, object recognition, and spatial analysis
 */

import { GoogleGenerativeAI } from '@google/generative-ai'
import type { DetectedMaterial } from '@/types'
import type { CompanyType } from '@/lib/company-types'
import { takeoffLogger, logProcessingStep, logError } from './logger-wrapper'
import { VisionServiceError } from './errors'

export interface Symbol {
  type: string
  subtype?: string
  location: SymbolLocation
  confidence: number
  attributes: Record<string, any>
  id: string
}

export interface SymbolLocation {
  x: number
  y: number
  width: number
  height: number
  zone?: string
  gridReference?: string
}

export interface SymbolPattern {
  type: string
  variants: string[]
  attributes: string[]
  category: string
  trade: CompanyType[]
}

export interface ComputerVisionResult {
  symbols: Symbol[]
  materials: DetectedMaterial[]
  text: ExtractedText[]
  lines: DetectedLine[]
  measurements: Measurement[]
  confidence: number
  processingTime: number
  symbolCounts: Record<string, number>
}

export interface ExtractedText {
  content: string
  location: SymbolLocation
  type: 'label' | 'dimension' | 'note' | 'schedule' | 'specification'
  associatedSymbol?: string
  confidence: number
}

export interface DetectedLine {
  startPoint: { x: number; y: number }
  endPoint: { x: number; y: number }
  type: 'wall' | 'pipe' | 'wire' | 'duct' | 'dimension' | 'grid' | 'other'
  thickness: number
  style: 'solid' | 'dashed' | 'dotted'
  material?: string
}

export interface Measurement {
  value: number
  unit: string
  type: 'length' | 'area' | 'angle'
  points: Array<{ x: number; y: number }>
  confidence: number
}

// Construction symbol patterns for detection
const SYMBOL_PATTERNS: Record<string, SymbolPattern> = {
  // Electrical symbols
  'duplex_outlet': {
    type: 'electrical_device',
    variants: ['standard', 'gfci', 'waterproof', 'floor'],
    attributes: ['voltage', 'amperage', 'mounting_height'],
    category: 'Devices',
    trade: ['Electrical Contractor']
  },
  'light_fixture': {
    type: 'lighting',
    variants: ['recessed', 'surface', 'pendant', 'track', 'emergency', 'exit'],
    attributes: ['wattage', 'lumens', 'color_temperature', 'control_type'],
    category: 'Lighting',
    trade: ['Electrical Contractor']
  },
  'switch': {
    type: 'electrical_control',
    variants: ['single_pole', 'three_way', 'dimmer', 'occupancy', 'timer'],
    attributes: ['type', 'rating', 'control_zones'],
    category: 'Lighting Controls',
    trade: ['Electrical Contractor']
  },
  'electrical_panel': {
    type: 'distribution',
    variants: ['main', 'sub', 'lighting', 'power'],
    attributes: ['voltage', 'amperage', 'circuits', 'phase'],
    category: 'Service & Distribution',
    trade: ['Electrical Contractor']
  },
  'junction_box': {
    type: 'electrical_device',
    variants: ['standard', 'pull', 'weatherproof'],
    attributes: ['size', 'rating'],
    category: 'Devices',
    trade: ['Electrical Contractor']
  },
  
  // Plumbing symbols
  'water_closet': {
    type: 'plumbing_fixture',
    variants: ['floor_mount', 'wall_mount', 'ada'],
    attributes: ['gpf', 'rough_in'],
    category: 'Fixtures',
    trade: ['Plumbing Contractor']
  },
  'lavatory': {
    type: 'plumbing_fixture',
    variants: ['wall_mount', 'counter_mount', 'pedestal'],
    attributes: ['faucet_type', 'ada_compliant'],
    category: 'Fixtures',
    trade: ['Plumbing Contractor']
  },
  'floor_drain': {
    type: 'drainage',
    variants: ['standard', 'trench', 'area'],
    attributes: ['size', 'grate_type'],
    category: 'Drainage',
    trade: ['Plumbing Contractor']
  },
  
  // HVAC symbols
  'diffuser': {
    type: 'air_distribution',
    variants: ['supply', 'return', 'linear', 'round', 'square'],
    attributes: ['size', 'cfm', 'neck_size'],
    category: 'Diffusers & Grilles',
    trade: ['HVAC Contractor']
  },
  'vav_box': {
    type: 'hvac_equipment',
    variants: ['cooling_only', 'reheat', 'fan_powered'],
    attributes: ['cfm_max', 'cfm_min', 'inlet_size'],
    category: 'Equipment',
    trade: ['HVAC Contractor']
  },
  'thermostat': {
    type: 'hvac_control',
    variants: ['programmable', 'non_programmable', 'smart'],
    attributes: ['type', 'zones_controlled'],
    category: 'Controls',
    trade: ['HVAC Contractor']
  },
  
  // Structural symbols
  'column': {
    type: 'structural',
    variants: ['steel', 'concrete', 'wood'],
    attributes: ['size', 'material', 'load_capacity'],
    category: 'Steel',
    trade: ['Steel/Metal Contractor', 'General Contractor']
  },
  'beam': {
    type: 'structural',
    variants: ['steel', 'concrete', 'wood', 'composite'],
    attributes: ['size', 'span', 'load_capacity'],
    category: 'Steel',
    trade: ['Steel/Metal Contractor', 'General Contractor']
  },
  
  // Doors and windows
  'door': {
    type: 'opening',
    variants: ['single', 'double', 'sliding', 'overhead', 'revolving'],
    attributes: ['width', 'height', 'fire_rating', 'material'],
    category: 'Doors',
    trade: ['General Contractor']
  },
  'window': {
    type: 'opening',
    variants: ['fixed', 'casement', 'double_hung', 'sliding', 'awning'],
    attributes: ['width', 'height', 'glazing', 'operation'],
    category: 'Windows',
    trade: ['General Contractor', 'Glass & Glazing Contractor']
  }
}

// Symbol detection prompts by trade
const TRADE_SPECIFIC_PROMPTS: Record<string, string> = {
  'Electrical Contractor': `
    Detect ALL electrical symbols including:
    - Duplex receptacles (⊕ symbols)
    - Light fixtures (circles, squares with X, rectangles)
    - Switches (S, S3, etc.)
    - Junction boxes (squares, circles)
    - Panels (rectangles with schedules)
    - Emergency/Exit lights (special symbols)
    - Lighting control devices (OS for occupancy sensor, DS for daylight sensor)
    - Low voltage devices (data, fire alarm, etc.)
    
    For each symbol provide:
    - Exact count
    - Grid location (A1, B2, etc.)
    - Associated room/area
    - Any specifications or notes
  `,
  
  'Plumbing Contractor': `
    Detect ALL plumbing symbols including:
    - Water closets (toilet symbols)
    - Lavatories (sink symbols)
    - Floor drains (FD circles)
    - Cleanouts (CO)
    - Water heaters (WH)
    - Valves (various symbols)
    - Pipe runs with sizes
    
    Track pipe sizes and materials when labeled.
  `,
  
  'HVAC Contractor': `
    Detect ALL HVAC symbols including:
    - Supply diffusers (squares with X pattern)
    - Return grilles (squares with diagonal lines)
    - VAV boxes (rectangles with labels)
    - Thermostats (T in circle)
    - Ductwork with sizes
    - Equipment (RTU, AHU, etc.)
  `,
  
  'General Contractor': `
    Detect ALL architectural symbols including:
    - Doors (arc symbols with door number)
    - Windows (parallel lines in walls)
    - Walls (thick lines)
    - Columns (filled circles/squares)
    - Room labels and numbers
    - Dimensions
    - Grid lines
  `
}

export class ComputerVisionAnalyzer {
  private genAI: GoogleGenerativeAI | null = null
  private apiKey: string
  private isInitialized: boolean = false
  
  constructor() {
    if (typeof window === 'undefined') {
      this.apiKey = process.env.GEMINI_API_KEY || ''
      if (this.apiKey) {
        this.genAI = new GoogleGenerativeAI(this.apiKey)
        this.isInitialized = true
        takeoffLogger.info('Computer Vision Analyzer initialized', {
          model: 'gemini-2.5-flash',  // Cost-optimized Flash model
          hasApiKey: true
        })
      }
    } else {
      this.apiKey = ''
    }
  }
  
  /**
   * Analyze construction drawing with advanced computer vision
   */
  async analyzeDrawing(
    imageData: string,
    companyType: CompanyType,
    options: {
      detectSymbols?: boolean
      extractMeasurements?: boolean
      analyzeText?: boolean
      gridSize?: number
    } = {}
  ): Promise<ComputerVisionResult> {
    const startTime = Date.now()
    const analysisId = `cv-analysis-${Date.now()}`
    
    const {
      detectSymbols = true,
      extractMeasurements = true,
      analyzeText = true,
      gridSize = 100
    } = options
    
    takeoffLogger.info('Starting computer vision analysis', {
      analysisId,
      companyType,
      options
    })
    
    try {
      this.checkAvailability()
      
      const model = this.genAI!.getGenerativeModel({
        model: 'gemini-2.5-flash',  // Cost-optimized Flash model
        generationConfig: {
          temperature: 0.3, // Optimized for cost efficiency and consistency
          topK: 10,
          topP: 0.95,
          maxOutputTokens: 8192
        }
      })
      
      // Prepare image
      const image = {
        inlineData: {
          data: imageData.includes('base64,') ? imageData.split(',')[1] : imageData,
          mimeType: 'image/png'
        }
      }
      
      // Build comprehensive prompt
      const prompt = this.buildAnalysisPrompt(companyType, {
        detectSymbols,
        extractMeasurements,
        analyzeText,
        gridSize
      })
      
      logProcessingStep(takeoffLogger, 'Sending to Gemini Vision API', {
        analysisId,
        promptLength: prompt.length
      }, startTime)
      
      // Get AI analysis
      const result = await model.generateContent([prompt, image])
      const response = await result.response
      const text = response.text()
      
      logProcessingStep(takeoffLogger, 'Received Gemini response', {
        analysisId,
        responseLength: text.length
      }, startTime)
      
      // Parse response
      const analysis = this.parseAnalysisResponse(text, companyType)
      
      // Post-process symbols
      if (detectSymbols) {
        analysis.symbols = this.postProcessSymbols(analysis.symbols, companyType)
        analysis.symbolCounts = this.countSymbolsByType(analysis.symbols)
      }
      
      // Convert symbols to materials
      analysis.materials = this.symbolsToMaterials(analysis.symbols, companyType)
      
      // Extract measurements from lines
      if (extractMeasurements) {
        analysis.measurements = this.extractMeasurementsFromLines(analysis.lines)
      }
      
      const processingTime = Date.now() - startTime
      
      takeoffLogger.info('Computer vision analysis completed', {
        analysisId,
        symbolsDetected: analysis.symbols.length,
        materialsGenerated: analysis.materials.length,
        processingTime
      })
      
      return {
        ...analysis,
        processingTime
      }
      
    } catch (error) {
      logError(takeoffLogger, error, 'Computer vision analysis failed', {
        analysisId
      })
      throw new VisionServiceError(
        `Computer vision analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }
  
  /**
   * Build analysis prompt based on company type and options
   */
  private buildAnalysisPrompt(
    companyType: CompanyType,
    options: any
  ): string {
    const tradePrompt = TRADE_SPECIFIC_PROMPTS[companyType] || TRADE_SPECIFIC_PROMPTS['General Contractor']
    
    return `You are an expert construction drawing analyzer specializing in ${companyType} work.

TASK: Perform detailed computer vision analysis of this construction drawing.

${tradePrompt}

SYMBOL DETECTION REQUIREMENTS:
1. Count EVERY instance of each symbol type
2. Provide exact grid coordinates (divide drawing into ${options.gridSize}px grid)
3. Note any labels or specifications near symbols
4. Identify symbol variants (e.g., GFCI outlet vs standard)
5. Group symbols by rooms/zones when possible

LINE DETECTION:
- Identify all lines (walls, pipes, wires, ducts)
- Measure line lengths and angles
- Detect line types (solid, dashed, different thicknesses)
- Trace continuous paths

TEXT EXTRACTION:
- Extract ALL text labels
- Classify text type (dimension, label, note, specification)
- Associate text with nearby symbols
- Extract tabular data from schedules

OUTPUT FORMAT (JSON):
{
  "symbols": [
    {
      "type": "symbol_type",
      "subtype": "variant",
      "location": {"x": 0, "y": 0, "width": 20, "height": 20, "zone": "Room 101"},
      "confidence": 0.95,
      "attributes": {"voltage": "120V", "mounting": "wall"},
      "id": "unique_id"
    }
  ],
  "lines": [
    {
      "startPoint": {"x": 0, "y": 0},
      "endPoint": {"x": 100, "y": 0},
      "type": "wall",
      "thickness": 6,
      "style": "solid"
    }
  ],
  "text": [
    {
      "content": "2x4 TROFFER",
      "location": {"x": 50, "y": 50, "width": 100, "height": 20},
      "type": "specification",
      "associatedSymbol": "light_fixture_001",
      "confidence": 0.9
    }
  ],
  "measurements": [
    {
      "value": 10.5,
      "unit": "ft",
      "type": "length",
      "points": [{"x": 0, "y": 0}, {"x": 126, "y": 0}],
      "confidence": 0.95
    }
  ],
  "confidence": 0.9
}

BE THOROUGH - Count every symbol, don't estimate or summarize.`
  }
  
  /**
   * Parse AI response into structured format
   */
  private parseAnalysisResponse(
    responseText: string,
    companyType: CompanyType
  ): ComputerVisionResult {
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        
        return {
          symbols: this.validateSymbols(parsed.symbols || [], companyType),
          materials: [], // Will be generated from symbols
          text: parsed.text || [],
          lines: parsed.lines || [],
          measurements: parsed.measurements || [],
          confidence: parsed.confidence || 0.85,
          processingTime: 0,
          symbolCounts: {}
        }
      }
    } catch (error) {
      takeoffLogger.warn('Failed to parse JSON response, using fallback', { error })
    }
    
    // Fallback
    return {
      symbols: [],
      materials: [],
      text: [],
      lines: [],
      measurements: [],
      confidence: 0.5,
      processingTime: 0,
      symbolCounts: {}
    }
  }
  
  /**
   * Validate and enrich detected symbols
   */
  private validateSymbols(symbols: any[], companyType: CompanyType): Symbol[] {
    const validatedSymbols: Symbol[] = []
    
    for (const symbol of symbols) {
      // Check if symbol is relevant to trade
      const pattern = this.findSymbolPattern(symbol.type)
      if (pattern && pattern.trade.includes(companyType)) {
        validatedSymbols.push({
          type: symbol.type,
          subtype: symbol.subtype,
          location: this.validateLocation(symbol.location),
          confidence: symbol.confidence || 0.8,
          attributes: symbol.attributes || {},
          id: symbol.id || `symbol-${Date.now()}-${Math.random()}`
        })
      }
    }
    
    return validatedSymbols
  }
  
  /**
   * Find symbol pattern definition
   */
  private findSymbolPattern(symbolType: string): SymbolPattern | null {
    return SYMBOL_PATTERNS[symbolType] || null
  }
  
  /**
   * Validate location data
   */
  private validateLocation(location: any): SymbolLocation {
    return {
      x: Number(location?.x) || 0,
      y: Number(location?.y) || 0,
      width: Number(location?.width) || 20,
      height: Number(location?.height) || 20,
      zone: location?.zone,
      gridReference: location?.gridReference
    }
  }
  
  /**
   * Post-process symbols for accuracy
   */
  private postProcessSymbols(symbols: Symbol[], companyType: CompanyType): Symbol[] {
    // Remove duplicates based on proximity
    const deduplicated = this.deduplicateSymbols(symbols)
    
    // Validate symbol patterns
    const validated = deduplicated.filter(symbol => {
      const pattern = this.findSymbolPattern(symbol.type)
      return pattern && pattern.trade.includes(companyType)
    })
    
    // Enrich with additional attributes
    return validated.map(symbol => this.enrichSymbol(symbol))
  }
  
  /**
   * Remove duplicate symbols that are too close
   */
  private deduplicateSymbols(symbols: Symbol[]): Symbol[] {
    const unique: Symbol[] = []
    const threshold = 10 // pixels
    
    for (const symbol of symbols) {
      const isDuplicate = unique.some(existing => 
        Math.abs(existing.location.x - symbol.location.x) < threshold &&
        Math.abs(existing.location.y - symbol.location.y) < threshold &&
        existing.type === symbol.type
      )
      
      if (!isDuplicate) {
        unique.push(symbol)
      }
    }
    
    return unique
  }
  
  /**
   * Enrich symbol with additional attributes
   */
  private enrichSymbol(symbol: Symbol): Symbol {
    const pattern = this.findSymbolPattern(symbol.type)
    if (!pattern) return symbol
    
    // Add default attributes if missing
    const enriched = { ...symbol }
    
    // Add category
    enriched.attributes.category = pattern.category
    
    // Add default specifications based on type
    switch (symbol.type) {
      case 'duplex_outlet':
        enriched.attributes.voltage = enriched.attributes.voltage || '120V'
        enriched.attributes.amperage = enriched.attributes.amperage || '20A'
        break
      case 'light_fixture':
        enriched.attributes.mounting = enriched.attributes.mounting || 'ceiling'
        enriched.attributes.control_type = enriched.attributes.control_type || 'switch'
        break
      case 'diffuser':
        enriched.attributes.type = enriched.attributes.type || 'supply'
        break
    }
    
    return enriched
  }
  
  /**
   * Count symbols by type
   */
  private countSymbolsByType(symbols: Symbol[]): Record<string, number> {
    const counts: Record<string, number> = {}
    
    for (const symbol of symbols) {
      const key = symbol.subtype ? `${symbol.type}_${symbol.subtype}` : symbol.type
      counts[key] = (counts[key] || 0) + 1
    }
    
    return counts
  }
  
  /**
   * Convert detected symbols to takeoff materials
   */
  private symbolsToMaterials(symbols: Symbol[], companyType: CompanyType): DetectedMaterial[] {
    const materials: DetectedMaterial[] = []
    const grouped = this.groupSymbolsByType(symbols)
    
    for (const [key, group] of Object.entries(grouped)) {
      const pattern = this.findSymbolPattern(group[0].type)
      if (!pattern) continue
      
      const material: DetectedMaterial = {
        name: this.generateMaterialName(group[0], pattern),
        type: pattern.category,
        category: pattern.category,
        quantity: group.length,
        unit: 'EA',
        specifications: this.generateSpecifications(group[0]),
        location: this.aggregateLocations(group),
        confidence: this.calculateAverageConfidence(group),
        source: 'computer-vision',
        pageNumber: 1
      }
      
      materials.push(material)
    }
    
    return materials
  }
  
  /**
   * Group symbols by type and subtype
   */
  private groupSymbolsByType(symbols: Symbol[]): Record<string, Symbol[]> {
    const groups: Record<string, Symbol[]> = {}
    
    for (const symbol of symbols) {
      const key = symbol.subtype ? `${symbol.type}_${symbol.subtype}` : symbol.type
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(symbol)
    }
    
    return groups
  }
  
  /**
   * Generate material name from symbol
   */
  private generateMaterialName(symbol: Symbol, pattern: SymbolPattern): string {
    const baseName = symbol.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    
    if (symbol.subtype) {
      const subtype = symbol.subtype.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      return `${baseName} - ${subtype}`
    }
    
    // Add specifications to name if available
    if (symbol.attributes.size) {
      return `${baseName} ${symbol.attributes.size}`
    }
    
    if (symbol.attributes.voltage && symbol.attributes.amperage) {
      return `${baseName} ${symbol.attributes.voltage} ${symbol.attributes.amperage}`
    }
    
    return baseName
  }
  
  /**
   * Generate specifications string
   */
  private generateSpecifications(symbol: Symbol): string {
    const specs: string[] = []
    
    for (const [key, value] of Object.entries(symbol.attributes)) {
      if (key !== 'category' && value) {
        specs.push(`${key}: ${value}`)
      }
    }
    
    return specs.join(', ')
  }
  
  /**
   * Aggregate locations for grouped symbols
   */
  private aggregateLocations(symbols: Symbol[]): string {
    const zones = new Set<string>()
    
    for (const symbol of symbols) {
      if (symbol.location.zone) {
        zones.add(symbol.location.zone)
      }
    }
    
    if (zones.size > 0) {
      return Array.from(zones).join(', ')
    }
    
    return 'Various locations'
  }
  
  /**
   * Calculate average confidence
   */
  private calculateAverageConfidence(symbols: Symbol[]): number {
    const sum = symbols.reduce((acc, s) => acc + s.confidence, 0)
    return sum / symbols.length
  }
  
  /**
   * Extract measurements from detected lines
   */
  private extractMeasurementsFromLines(lines: DetectedLine[]): Measurement[] {
    const measurements: Measurement[] = []
    
    for (const line of lines) {
      const length = this.calculateLineLength(line)
      if (length > 0) {
        measurements.push({
          value: length,
          unit: 'px', // Will need scale conversion
          type: 'length',
          points: [line.startPoint, line.endPoint],
          confidence: 0.9
        })
      }
    }
    
    return measurements
  }
  
  /**
   * Calculate line length
   */
  private calculateLineLength(line: DetectedLine): number {
    const dx = line.endPoint.x - line.startPoint.x
    const dy = line.endPoint.y - line.startPoint.y
    return Math.sqrt(dx * dx + dy * dy)
  }
  
  /**
   * Check if service is available
   */
  private checkAvailability(): void {
    if (!this.isInitialized || !this.genAI) {
      throw new VisionServiceError(
        'Computer vision service not available',
        'SERVICE_NOT_AVAILABLE'
      )
    }
  }
}

// Export singleton instance
export const computerVisionAnalyzer = new ComputerVisionAnalyzer()