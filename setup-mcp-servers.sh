#!/bin/bash

# MCP Server Setup Script for Claude Code
# This script adds all the commonly used MCP servers

echo "Setting up MCP servers for Claude Code..."

# 1. Puppeteer - Web automation and scraping
echo "Adding Puppeteer server..."
claude mcp add puppeteer npx -y @modelcontextprotocol/server-puppeteer

# 2. Memory - Persistent memory across sessions
echo "Adding Memory server..."
claude mcp add memory npx -y @modelcontextprotocol/server-memory

# 3. Docker - Docker container management
echo "Adding Docker server..."
claude mcp add docker npx -y @modelcontextprotocol/server-docker

# 4. GitHub - GitHub repository interactions
echo "Adding GitHub server..."
# Using the token from .env.local
claude mcp add github env GITHUB_PERSONAL_ACCESS_TOKEN=**************************************** npx -y @modelcontextprotocol/server-github

# 5. PostgreSQL - Database access
echo "Adding PostgreSQL server..."
# Use DATABASE_URL from environment or .env.local file
DATABASE_URL=${DATABASE_URL:-$(grep DATABASE_URL .env.local | cut -d'"' -f2 | sed 's/postgres:postgres/postgres:itsMike818!/g')}
claude mcp add postgres env DATABASE_URL="$DATABASE_URL" npx -y @modelcontextprotocol/server-postgres

# 6. Filesystem - File system access (commonly used)
echo "Adding Filesystem server..."
claude mcp add filesystem npx -y @modelcontextprotocol/server-filesystem /mnt/c/Projects

# 7. Brave Search - Web search capabilities
echo "Adding Brave Search server..."
claude mcp add brave-search npx -y @modelcontextprotocol/server-brave-search

# 8. Fetch - HTTP requests
echo "Adding Fetch server..."
claude mcp add fetch npx -y @modelcontextprotocol/server-fetch

# Optional servers (uncomment to enable):

# # Todoist - Task management
# echo "Adding Todoist server..."
# claude mcp add todoist env TODOIST_API_TOKEN=your-todoist-token npx -y @modelcontextprotocol/server-todoist

# # Sentry - Error tracking
# echo "Adding Sentry server..."
# claude mcp add sentry env SENTRY_AUTH_TOKEN=your-sentry-token env SENTRY_ORG=your-org npx -y @modelcontextprotocol/server-sentry

# # Slack - Team communication
# echo "Adding Slack server..."
# claude mcp add slack env SLACK_BOT_TOKEN=your-slack-token npx -y @modelcontextprotocol/server-slack

# # Google Drive - File storage
# echo "Adding Google Drive server..."
# claude mcp add gdrive env GOOGLE_SERVICE_ACCOUNT_JSON=path/to/credentials.json npx -y @modelcontextprotocol/server-gdrive

echo ""
echo "MCP server setup complete! Run 'claude mcp list' to see all configured servers."
echo ""
echo "You can now use these servers with @ mentions:"
echo "  @puppeteer - for web automation"
echo "  @memory - for persistent memory"
echo "  @docker - for Docker operations"
echo "  @github - for GitHub interactions"
echo "  @postgres - for database queries"
echo "  @filesystem - for file operations"
echo "  @brave-search - for web searches"
echo "  @fetch - for HTTP requests"