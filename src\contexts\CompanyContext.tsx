'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { CompanyType, CompanyTypeDetails, companyTypeDetailsMap } from '@/lib/company-types'

// Define the context shape
interface CompanyContextType {
  companyType: CompanyType | null
  setCompanyType: (type: CompanyType) => void
  companyTypeDetails: CompanyTypeDetails | null
}

// Create the context
const CompanyContext = createContext<CompanyContextType | undefined>(undefined)



// Provider component
export function CompanyProvider({ children }: { children: ReactNode }) {
  const [companyType, setCompanyTypeState] = useState<CompanyType | null>(null)
  const [companyTypeDetails, setCompanyTypeDetails] = useState<CompanyContextType['companyTypeDetails']>(null)

  // Load company type from localStorage on mount
  useEffect(() => {
    const savedCompanyType = localStorage.getItem('aiConstructionCompanyType')
    if (savedCompanyType && savedCompanyType in companyTypeDetailsMap) {
      const type = savedCompanyType as CompanyType
      setCompanyTypeState(type)
      setCompanyTypeDetails(companyTypeDetailsMap[type])
    } else {
      // Default to General Contractor if no company type is set
      const defaultType: CompanyType = 'General Contractor'
      setCompanyTypeState(defaultType)
      setCompanyTypeDetails(companyTypeDetailsMap[defaultType])
      localStorage.setItem('aiConstructionCompanyType', defaultType)
    }
  }, [])

  // Update company type and save to localStorage
  const setCompanyType = (type: CompanyType) => {
    setCompanyTypeState(type)
    setCompanyTypeDetails(companyTypeDetailsMap[type])
    localStorage.setItem('aiConstructionCompanyType', type)
  }

  const value: CompanyContextType = {
    companyType,
    setCompanyType,
    companyTypeDetails
  }

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  )
}

// Custom hook to use the company context
export function useCompany() {
  const context = useContext(CompanyContext)
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider')
  }
  return context
}
