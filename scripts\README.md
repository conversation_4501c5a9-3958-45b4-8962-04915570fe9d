# Git Push Scripts

This folder contains scripts to help you push your code to GitHub using a Personal Access Token stored in `.env.local`.

## Setup

1. **Create a GitHub Personal Access Token:**
   - Go to https://github.com/settings/tokens
   - Click "Generate new token (classic)"
   - Give it a descriptive name (e.g., "AI Construction Push")
   - Select the `repo` scope (for full repository access)
   - Generate the token and copy it

2. **Update `.env.local`:**
   ```env
   GITHUB_TOKEN=your-actual-token-here
   GITHUB_USERNAME=mikeaper323
   GITHUB_EMAIL=<EMAIL>
   ```

## Usage

### PowerShell Script (Recommended for Windows)
```powershell
.\scripts\git-push-with-token.ps1
```

### Node.js Script (Cross-platform)
```bash
node scripts/git-push-node.js
```

## What the Scripts Do

1. Read your GitHub credentials from `.env.local`
2. Configure git with your username and email
3. Initialize git repository if needed
4. Add authentication to the remote URL
5. Add all files and commit with a descriptive message
6. Push to GitHub
7. Clean up the authentication from the remote URL for security

## Security Notes

- Never commit your `.env.local` file (it's already in `.gitignore`)
- The scripts automatically remove the token from the git remote URL after pushing
- Your token is only used temporarily during the push operation
- Make sure to keep your Personal Access Token secure

## Troubleshooting

If you get an authentication error:
1. Make sure your token has the correct permissions (`repo` scope)
2. Check that the token hasn't expired
3. Ensure you've updated `.env.local` with the correct token

If you get a "repository not found" error:
1. Make sure the repository exists at https://github.com/mikeaper323/AI-Construction
2. Check that your GitHub username has access to the repository
