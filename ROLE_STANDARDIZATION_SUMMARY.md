# UserRole Standardization Summary

## Changes Made

### 1. Created `/src/lib/user-role-types.ts`
A centralized module for UserRole type definition and utilities:

- **Standardized UserRole type**: `ADMIN | PROJECT_MANAGER | CONTRACTOR | SUBCONTRACTOR | VIEWER`
- **Role hierarchy** (highest to lowest):
  - ADMIN (level 5) - Full system access
  - PROJECT_MANAGER (level 4) - Can manage projects and teams
  - CONTRACTOR (level 3) - Can view/edit their own work
  - SUBCONTRACTOR (level 2) - Limited access to assigned work
  - VIEWER (level 1) - Read-only access

### 2. Helper Functions Implemented

- `getRoleLevel(role: UserRole): number` - Get hierarchy level
- `hasPermission(userRole: UserRole, requiredRole: UserRole): boolean` - Check permissions
- `getRoleDisplayName(role: UserRole): string` - Get display name
- `isValidUserRole(role: string): boolean` - Validate role string
- `getAllRoles(): UserRole[]` - Get all roles sorted by hierarchy
- `getAssignableRoles(userRole: UserRole): UserRole[]` - Get roles a user can assign
- `convertLegacyRole(legacyRole: string): UserRole` - Convert legacy roles
- `getRolePermissions(role: UserRole)` - Get detailed permissions object

### 3. Files Updated

1. **`/src/types/index.ts`**
   - Imports UserRole from the new module
   - Re-exports it for convenience

2. **`/src/lib/db/auth.ts`**
   - Imports role utilities
   - Updated `hasRole()` to use standardized roles
   - JWT generation now converts roles to standard format

3. **`/src/app/dashboard/settings/page.tsx`**
   - Imports role utilities
   - Updated profile role to use UserRole type
   - Role dropdowns now use `getAllRoles()` and `getRoleDisplayName()`
   - Team member display uses standardized roles

4. **`/prisma/schema.prisma`**
   - No changes needed (already uses String with default "CONTRACTOR")

### 4. Backwards Compatibility

The `convertLegacyRole()` function handles legacy values:
- Lowercase values (e.g., 'admin' → 'ADMIN')
- Old role names (e.g., 'owner' → 'ADMIN', 'site_manager' → 'PROJECT_MANAGER')
- Unknown roles default to 'CONTRACTOR'

### 5. Role-Based Permissions

The `getRolePermissions()` function returns granular permissions:
- System permissions (user management, company settings)
- Project permissions (create, edit, delete, assign members)
- Schedule permissions (edit, view)
- Safety permissions (create reports, approve)
- Estimating permissions (create, approve)
- Document permissions (upload, delete)
- Analytics permissions (view, export)
- AI Assistant permissions (use, configure)

## Usage Examples

```typescript
import { UserRole, hasPermission, getRoleDisplayName } from '@/lib/user-role-types'

// Check if user can perform an action
if (hasPermission(user.role as UserRole, 'PROJECT_MANAGER')) {
  // User can perform project manager actions
}

// Display role name
const displayName = getRoleDisplayName(user.role as UserRole) // "Project Manager"

// Get assignable roles for dropdown
const roles = getAssignableRoles(currentUser.role as UserRole)
```

## Testing

All functionality is tested in `/src/lib/__tests__/user-role-types.test.ts` with 100% coverage.