import { getUserProjects } from '@/lib/db/projects';
import ProjectsClient from './projects-client';
import { getCurrentUser } from '@/lib/db/auth';
import { redirect } from 'next/navigation';
import { ProjectStatus } from '@/types';

export default async function ProjectsPage() {
  const user = await getCurrentUser();
  if (!user) {
    redirect('/login');
  }

  const { projects } = await getUserProjects();

  const typedProjects = projects.map(project => ({
    ...project,
    status: project.status as ProjectStatus,
  }));

  return <ProjectsClient projects={typedProjects} />;
}