// Collaboration toolbar component for BIM viewer

'use client'

import React, { useState } from 'react'
import { useCollaboration } from './CollaborationProvider'
import { useUserPresence } from '../hooks'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { 
  Users, 
  MessageSquare, 
  MousePointer, 
  Eye, 
  EyeOff,
  Wifi,
  WifiOff,
  ChevronRight,
  Circle
} from 'lucide-react'

export function CollaborationToolbar() {
  const {
    isConnected,
    users,
    annotations,
    cursors,
    selections,
    error
  } = useCollaboration()

  const { activeUsers } = useUserPresence(users)
  const [showCursors, setShowCursors] = useState(true)
  const [showSelections, setShowSelections] = useState(true)
  const [isExpanded, setIsExpanded] = useState(true)

  const openAnnotations = annotations.filter(a => a.status === 'open').length

  return (
    <Card className={`absolute top-4 right-4 z-50 transition-all duration-300 ${
      isExpanded ? 'w-80' : 'w-16'
    }`}>
      <div className="p-3">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            {isConnected ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-600" />
            )}
            {isExpanded && (
              <span className="text-sm font-medium">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0"
          >
            <ChevronRight className={`h-4 w-4 transition-transform ${
              isExpanded ? 'rotate-180' : ''
            }`} />
          </Button>
        </div>

        {isExpanded && (
          <>
            {/* Active Users */}
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-4 w-4" />
                <span className="text-sm font-medium">Active Users ({activeUsers.length})</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {activeUsers.map(user => (
                  <div
                    key={user.userId}
                    className="flex items-center gap-1 px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-800"
                  >
                    <Circle 
                      className="h-2 w-2" 
                      style={{ color: user.color, fill: user.color }}
                    />
                    <span className="text-xs">{user.userName}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-2 mb-4">
              <div className="flex items-center gap-2 text-sm">
                <MousePointer className="h-4 w-4" />
                <span>{cursors.length} cursors</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <MessageSquare className="h-4 w-4" />
                <span>{openAnnotations} open</span>
              </div>
            </div>

            {/* Toggle Options */}
            <div className="space-y-2">
              <Button
                variant={showCursors ? 'default' : 'outline'}
                size="sm"
                className="w-full justify-start"
                onClick={() => setShowCursors(!showCursors)}
              >
                {showCursors ? <Eye className="h-4 w-4 mr-2" /> : <EyeOff className="h-4 w-4 mr-2" />}
                Show Cursors
              </Button>
              <Button
                variant={showSelections ? 'default' : 'outline'}
                size="sm"
                className="w-full justify-start"
                onClick={() => setShowSelections(!showSelections)}
              >
                {showSelections ? <Eye className="h-4 w-4 mr-2" /> : <EyeOff className="h-4 w-4 mr-2" />}
                Show Selections
              </Button>
            </div>

            {/* Error Display */}
            {error && (
              <div className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 rounded text-xs text-red-600 dark:text-red-400">
                {error.message}
              </div>
            )}
          </>
        )}

        {/* Collapsed View */}
        {!isExpanded && (
          <div className="space-y-3">
            <Badge variant="outline" className="w-full justify-center">
              {activeUsers.length}
            </Badge>
            <Badge variant="outline" className="w-full justify-center">
              {openAnnotations}
            </Badge>
          </div>
        )}
      </div>
    </Card>
  )
}