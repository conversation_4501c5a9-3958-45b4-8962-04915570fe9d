#!/usr/bin/env pwsh
# Initialize git repository and push to GitHub

Write-Host "🚀 Setting up Git repository..." -ForegroundColor Yellow

# Initialize git repository
Write-Host "`n📁 Initializing git repository..." -ForegroundColor Cyan
git init

# Add remote origin
Write-Host "`n🔗 Adding remote origin..." -ForegroundColor Cyan
git remote add origin https://github.com/mikeaper323/AI-Construction.git

# Check if remote was added
Write-Host "`n📊 Verifying remote..." -ForegroundColor Cyan
git remote -v

# Create main branch
Write-Host "`n🌿 Creating main branch..." -ForegroundColor Cyan
git checkout -b main

# Add all files
Write-Host "`n➕ Adding all files..." -ForegroundColor Cyan
git add .

# Commit
$commitMessage = @"
fix: resolve 404 errors and add comprehensive settings page

- Create settings page at /dashboard/settings with 8 configuration sections
- Add Chrome DevTools configuration file (.well-known/appspecific/com.chrome.devtools.json)
- Implement complete settings UI with profile, notifications, security, API keys, display, organization, integrations, and AI settings
- Clean up test files and temporary files
- Add verification scripts for 404 fixes
- Add Gemini AI integration
- Complete dashboard implementation

Fixes:
- GET /dashboard/settings 404 error
- GET /.well-known/appspecific/com.chrome.devtools.json 404 error
"@

Write-Host "`n💬 Committing changes..." -ForegroundColor Cyan
git commit -m $commitMessage

# Push to GitHub
Write-Host "`n🚀 Pushing to GitHub..." -ForegroundColor Yellow
Write-Host "Note: You'll need to authenticate with GitHub" -ForegroundColor Gray
Write-Host "Use your GitHub username and Personal Access Token" -ForegroundColor Gray

git push -u origin main

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n✅ Successfully pushed to GitHub!" -ForegroundColor Green
    Write-Host "View your repository at: https://github.com/mikeaper323/AI-Construction" -ForegroundColor Cyan
} else {
    Write-Host "`n⚠️ If push failed due to authentication:" -ForegroundColor Yellow
    Write-Host "1. Create a Personal Access Token at: https://github.com/settings/tokens" -ForegroundColor White
    Write-Host "2. Use your GitHub username and the token as password" -ForegroundColor White
    Write-Host "3. Or set up SSH keys for easier authentication" -ForegroundColor White
}
