'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Maximize2, 
  ZoomIn, 
  ZoomOut, 
  RotateCw,
  Home,
  Eye,
  EyeOff,
  Layers,
  Box,
  Grid3x3,
  MousePointer,
  Ruler,
  Scissors,
  MessageSquare,
  Camera
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ViewerTools, SavedView } from '../types/bim.types'

interface ModelControlsProps {
  tools: ViewerTools
  onToolChange: (tool: keyof ViewerTools) => void
  onViewChange: (view: 'top' | 'front' | 'right' | 'iso' | SavedView) => void
  onZoomIn: () => void
  onZoomOut: () => void
  onZoomFit: () => void
  onResetView: () => void
  onToggleFullscreen: () => void
  savedViews?: SavedView[]
  onSaveView?: () => void
  isFullscreen?: boolean
}

export default function ModelControls({
  tools,
  onToolChange,
  onViewChange,
  onZoomIn,
  onZoomOut,
  onZoomFit,
  onResetView,
  onToggleFullscreen,
  savedViews = [],
  onSaveView,
  isFullscreen = false,
}: ModelControlsProps) {
  const [showTooltip, setShowTooltip] = useState<string | null>(null)

  const standardViews = [
    { id: 'top', label: 'Top', icon: '⬆' },
    { id: 'front', label: 'Front', icon: '⬅' },
    { id: 'right', label: 'Right', icon: '➡' },
    { id: 'iso', label: 'Isometric', icon: '◆' },
  ]

  const toolButtons = [
    { key: 'select' as keyof ViewerTools, icon: MousePointer, label: 'Select' },
    { key: 'measure' as keyof ViewerTools, icon: Ruler, label: 'Measure' },
    { key: 'section' as keyof ViewerTools, icon: Scissors, label: 'Section' },
    { key: 'annotate' as keyof ViewerTools, icon: MessageSquare, label: 'Annotate' },
    { key: 'isolate' as keyof ViewerTools, icon: Eye, label: 'Isolate' },
    { key: 'explode' as keyof ViewerTools, icon: Box, label: 'Explode' },
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="absolute top-4 left-4 right-4 flex justify-between items-start pointer-events-none"
    >
      {/* Left side - Tools */}
      <div className="flex flex-col gap-2 pointer-events-auto">
        {/* Tool buttons */}
        <div className="bg-white rounded-lg shadow-lg p-2 flex flex-col gap-1">
          {toolButtons.map(({ key, icon: Icon, label }) => (
            <div key={key} className="relative">
              <Button
                size="sm"
                variant={tools[key] ? 'default' : 'ghost'}
                className="w-10 h-10 p-0"
                onClick={() => onToolChange(key)}
                onMouseEnter={() => setShowTooltip(key)}
                onMouseLeave={() => setShowTooltip(null)}
              >
                <Icon className="h-5 w-5" />
              </Button>
              <AnimatePresence>
                {showTooltip === key && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    className="absolute left-full ml-2 top-1/2 -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap"
                  >
                    {label}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>

        {/* Layers control */}
        <div className="bg-white rounded-lg shadow-lg p-2">
          <Button
            size="sm"
            variant="ghost"
            className="w-10 h-10 p-0"
            onMouseEnter={() => setShowTooltip('layers')}
            onMouseLeave={() => setShowTooltip(null)}
          >
            <Layers className="h-5 w-5" />
          </Button>
          <AnimatePresence>
            {showTooltip === 'layers' && (
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                className="absolute left-full ml-2 top-1/2 -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap"
              >
                Layers
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Right side - View controls */}
      <div className="flex gap-2 pointer-events-auto">
        {/* Views dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="bg-white shadow-lg">
              <Grid3x3 className="h-4 w-4 mr-2" />
              Views
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Standard Views</DropdownMenuLabel>
            {standardViews.map(view => (
              <DropdownMenuItem
                key={view.id}
                onClick={() => onViewChange(view.id as 'top' | 'front' | 'right' | 'iso')}
              >
                <span className="mr-2">{view.icon}</span>
                {view.label}
              </DropdownMenuItem>
            ))}
            {savedViews.length > 0 && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>Saved Views</DropdownMenuLabel>
                {savedViews.map(view => (
                  <DropdownMenuItem
                    key={view.id}
                    onClick={() => onViewChange(view)}
                  >
                    <Camera className="h-4 w-4 mr-2" />
                    {view.name}
                  </DropdownMenuItem>
                ))}
              </>
            )}
            {onSaveView && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onSaveView}>
                  <Camera className="h-4 w-4 mr-2" />
                  Save Current View
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Zoom controls */}
        <div className="bg-white rounded-lg shadow-lg p-1 flex gap-1">
          <Button
            size="sm"
            variant="ghost"
            className="w-9 h-9 p-0"
            onClick={onZoomIn}
            onMouseEnter={() => setShowTooltip('zoomIn')}
            onMouseLeave={() => setShowTooltip(null)}
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="w-9 h-9 p-0"
            onClick={onZoomOut}
            onMouseEnter={() => setShowTooltip('zoomOut')}
            onMouseLeave={() => setShowTooltip(null)}
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="w-9 h-9 p-0"
            onClick={onZoomFit}
            onMouseEnter={() => setShowTooltip('zoomFit')}
            onMouseLeave={() => setShowTooltip(null)}
          >
            <Home className="h-4 w-4" />
          </Button>
          <AnimatePresence>
            {(showTooltip === 'zoomIn' || showTooltip === 'zoomOut' || showTooltip === 'zoomFit') && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute top-full mt-2 right-0 bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap"
              >
                {showTooltip === 'zoomIn' && 'Zoom In'}
                {showTooltip === 'zoomOut' && 'Zoom Out'}
                {showTooltip === 'zoomFit' && 'Fit to View'}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Additional controls */}
        <div className="bg-white rounded-lg shadow-lg p-1 flex gap-1">
          <Button
            size="sm"
            variant="ghost"
            className="w-9 h-9 p-0"
            onClick={onResetView}
            onMouseEnter={() => setShowTooltip('reset')}
            onMouseLeave={() => setShowTooltip(null)}
          >
            <RotateCw className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="w-9 h-9 p-0"
            onClick={onToggleFullscreen}
            onMouseEnter={() => setShowTooltip('fullscreen')}
            onMouseLeave={() => setShowTooltip(null)}
          >
            <Maximize2 className="h-4 w-4" />
          </Button>
          <AnimatePresence>
            {(showTooltip === 'reset' || showTooltip === 'fullscreen') && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute top-full mt-2 right-0 bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap"
              >
                {showTooltip === 'reset' && 'Reset View'}
                {showTooltip === 'fullscreen' && (isFullscreen ? 'Exit Fullscreen' : 'Fullscreen')}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  )
}