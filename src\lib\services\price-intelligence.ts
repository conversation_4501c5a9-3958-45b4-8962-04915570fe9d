/**
 * Price Intelligence Service
 * Provides real-time price monitoring, historical trends, and predictive pricing
 */

import { supplierAPI, type PriceQuote } from './supplier-api'
import { geminiService } from '@/lib/gemini'
import type { CompanyType } from '@/lib/company-types'

// Types for price intelligence
export interface PriceTrend {
  materialId: string
  currentPrice: number
  previousPrice: number
  changePercent: number
  trend: 'increasing' | 'decreasing' | 'stable'
  volatility: 'low' | 'medium' | 'high'
  forecast: PriceForecast
}

export interface PriceForecast {
  next7Days: number
  next30Days: number
  next90Days: number
  confidence: number
  factors: string[]
}

export interface MarketAnalysis {
  category: string
  averagePrice: number
  priceRange: { min: number; max: number }
  supplierComparison: SupplierComparison[]
  seasonalFactors: SeasonalFactor[]
  marketConditions: string[]
}

export interface SupplierComparison {
  supplierId: string
  supplierName: string
  price: number
  availability: string
  reliability: number
  deliveryTime: number
}

export interface SeasonalFactor {
  month: string
  priceFactor: number // 1.0 = baseline, 1.1 = 10% higher
  demandLevel: 'low' | 'medium' | 'high'
}

export interface BulkPricingStrategy {
  materialId: string
  optimalQuantity: number
  pricePerUnit: number
  totalSavings: number
  breakEvenPoint: number
  recommendations: string[]
}

export interface PriceAlert {
  id: string
  materialId: string
  materialName: string
  alertType: 'price_drop' | 'price_increase' | 'availability_change'
  threshold: number
  currentValue: number
  message: string
  timestamp: Date
}

// Historical price data storage
interface PriceHistory {
  [materialId: string]: {
    prices: Array<{
      date: Date
      price: number
      supplierId: string
    }>
    lastUpdated: Date
  }
}

export class PriceIntelligenceService {
  private priceHistory: PriceHistory = {}
  private priceAlerts: PriceAlert[] = []
  private alertThresholds: Map<string, number> = new Map()
  
  /**
   * Monitor price changes for materials
   */
  async monitorPrices(
    materials: Array<{ id: string; sku: string; name: string }>,
    location?: { zipCode: string },
    companyType?: CompanyType | null
  ): Promise<PriceTrend[]> {
    const trends: PriceTrend[] = []
    
    for (const material of materials) {
      try {
        // Get current prices
        const currentQuotes = await supplierAPI.getPricing(material.sku, 1, location)
        
        if (currentQuotes.length === 0) continue
        
        // Get average current price
        const currentPrice = this.calculateAveragePrice(currentQuotes)
        
        // Get historical data or initialize
        const history = this.getOrInitializeHistory(material.id)
        const previousPrice = this.getPreviousPrice(history) || currentPrice
        
        // Update history
        this.updatePriceHistory(material.id, currentPrice, currentQuotes[0].supplierId)
        
        // Calculate trend
        const changePercent = ((currentPrice - previousPrice) / previousPrice) * 100
        const trend = this.determineTrend(changePercent)
        const volatility = this.calculateVolatility(history)
        
        // Generate forecast
        const forecast = await this.generatePriceForecast(material, history, currentPrice, companyType)
        
        trends.push({
          materialId: material.id,
          currentPrice,
          previousPrice,
          changePercent,
          trend,
          volatility,
          forecast
        })
        
        // Check for alerts
        this.checkPriceAlerts(material, currentPrice, changePercent)
      } catch (error) {
        console.error(`Error monitoring price for ${material.name}:`, error)
      }
    }
    
    return trends
  }
  
  /**
   * Analyze market conditions for a category
   */
  async analyzeMarket(
    category: string,
    materials: Array<{ sku: string; name: string }>,
    location?: { zipCode: string },
    companyType?: CompanyType | null
  ): Promise<MarketAnalysis> {
    const allPrices: number[] = []
    const supplierComparisons: SupplierComparison[] = []
    
    // Gather pricing data
    for (const material of materials) {
      try {
        const quotes = await supplierAPI.getPricing(material.sku, 1, location)
        
        quotes.forEach(quote => {
          allPrices.push(quote.price)
          
          const existing = supplierComparisons.find(s => s.supplierId === quote.supplierId)
          if (!existing) {
            supplierComparisons.push({
              supplierId: quote.supplierId,
              supplierName: quote.supplierName,
              price: quote.price,
              availability: quote.availability,
              reliability: this.calculateSupplierReliability(quote.supplierId),
              deliveryTime: quote.deliveryOptions[0]?.estimatedDays || 3
            })
          }
        })
      } catch (error) {
        console.error(`Error getting prices for ${material.name}:`, error)
      }
    }
    
    // Calculate market statistics
    const averagePrice = allPrices.reduce((sum, p) => sum + p, 0) / allPrices.length
    const priceRange = {
      min: Math.min(...allPrices),
      max: Math.max(...allPrices)
    }
    
    // Get seasonal factors
    const seasonalFactors = this.getSeasonalFactors(category)
    
    // Get market conditions from AI
    const marketConditions = await this.analyzeMarketConditionsWithAI(category, averagePrice, priceRange, companyType)
    
    return {
      category,
      averagePrice,
      priceRange,
      supplierComparison: supplierComparisons,
      seasonalFactors,
      marketConditions
    }
  }
  
  /**
   * Calculate optimal bulk purchasing strategy
   */
  async calculateBulkStrategy(
    materialId: string,
    sku: string,
    baseQuantity: number,
    maxBudget: number
  ): Promise<BulkPricingStrategy> {
    // Test different quantity levels
    const quantityLevels = [
      baseQuantity,
      baseQuantity * 2,
      baseQuantity * 5,
      baseQuantity * 10,
      baseQuantity * 20
    ]
    
    const pricingAnalysis: Array<{
      quantity: number
      totalCost: number
      unitCost: number
    }> = []
    
    for (const quantity of quantityLevels) {
      try {
        const quotes = await supplierAPI.getPricing(sku, quantity)
        if (quotes.length > 0) {
          const bestQuote = this.findBestQuote(quotes, quantity)
          const unitCost = this.calculateUnitCost(bestQuote, quantity)
          const totalCost = unitCost * quantity
          
          if (totalCost <= maxBudget) {
            pricingAnalysis.push({ quantity, totalCost, unitCost })
          }
        }
      } catch (error) {
        console.error(`Error calculating bulk price for quantity ${quantity}:`, error)
      }
    }
    
    // Find optimal quantity
    const optimal = this.findOptimalQuantity(pricingAnalysis, baseQuantity)
    
    // Generate recommendations
    const recommendations = await this.generateBulkRecommendations(
      materialId,
      optimal,
      pricingAnalysis
    )
    
    return {
      materialId,
      optimalQuantity: optimal.quantity,
      pricePerUnit: optimal.unitCost,
      totalSavings: (pricingAnalysis[0].unitCost - optimal.unitCost) * optimal.quantity,
      breakEvenPoint: this.calculateBreakEvenPoint(pricingAnalysis),
      recommendations
    }
  }
  
  /**
   * Set price alert threshold
   */
  setPriceAlert(
    materialId: string,
    materialName: string,
    threshold: number,
    type: 'percentage' | 'absolute' = 'percentage'
  ): void {
    this.alertThresholds.set(materialId, threshold)
  }
  
  /**
   * Get active price alerts
   */
  getActiveAlerts(): PriceAlert[] {
    // Return alerts from last 24 hours
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
    return this.priceAlerts.filter(alert => alert.timestamp > oneDayAgo)
  }
  
  /**
   * Generate price forecast using AI
   */
  private async generatePriceForecast(
    material: { id: string; name: string },
    history: any,
    currentPrice: number,
    companyType?: CompanyType | null
  ): Promise<PriceForecast> {
    try {
      const historicalPrices = history.prices.slice(-30) // Last 30 data points
      
      const companyContext = companyType ? `
Company Type: ${companyType}
Consider specific demand patterns and material usage for ${companyType} contractors.
` : ''

      const prompt = `Analyze construction material pricing trends and forecast:
Material: ${material.name}
Current Price: $${currentPrice}
Historical Prices (last 30 days): ${JSON.stringify(historicalPrices.map((p: any) => ({
  date: p.date,
  price: p.price
})))}
${companyContext}
Consider these factors:
1. Seasonal construction patterns
2. Supply chain conditions
3. Raw material costs
4. Labor market conditions
5. Economic indicators
${companyType ? `6. Specific demand for ${companyType} projects` : ''}

Provide price forecasts for:
- Next 7 days
- Next 30 days  
- Next 90 days

Include confidence level (0-1) and key factors affecting the forecast.
Format as JSON.`
      
      const response = await geminiService.sendMessage(prompt, 'price-intelligence', {
        companyType
      })
      
      try {
        const forecast = JSON.parse(response)
        return {
          next7Days: forecast.next7Days || currentPrice,
          next30Days: forecast.next30Days || currentPrice,
          next90Days: forecast.next90Days || currentPrice,
          confidence: forecast.confidence || 0.7,
          factors: forecast.factors || ['Market conditions', 'Seasonal demand']
        }
      } catch {
        // Fallback forecast based on simple trend
        const trend = this.calculateSimpleTrend(historicalPrices)
        return {
          next7Days: currentPrice * (1 + trend * 0.01),
          next30Days: currentPrice * (1 + trend * 0.05),
          next90Days: currentPrice * (1 + trend * 0.15),
          confidence: 0.5,
          factors: ['Historical trend analysis']
        }
      }
    } catch (error) {
      console.error('Forecast generation error:', error)
      return {
        next7Days: currentPrice,
        next30Days: currentPrice,
        next90Days: currentPrice,
        confidence: 0.3,
        factors: ['Insufficient data']
      }
    }
  }
  
  /**
   * Analyze market conditions with AI
   */
  private async analyzeMarketConditionsWithAI(
    category: string,
    averagePrice: number,
    priceRange: { min: number; max: number },
    companyType?: CompanyType | null
  ): Promise<string[]> {
    try {
      const companyContext = companyType ? `
Company Type: ${companyType}
Focus on market conditions specifically relevant to ${companyType} contractors.
` : ''

      const prompt = `Analyze current market conditions for ${category} construction materials:
Average Price: $${averagePrice.toFixed(2)}
Price Range: $${priceRange.min.toFixed(2)} - $${priceRange.max.toFixed(2)}
${companyContext}
Consider:
1. Current supply chain status
2. Seasonal factors
3. Economic conditions
4. Industry trends
5. Regional factors
${companyType ? `6. Specific market dynamics for ${companyType} projects` : ''}

Provide 3-5 key market condition insights as a JSON array of strings.`
      
      const response = await geminiService.sendMessage(prompt, 'market-analysis', {
        companyType
      })
      
      try {
        return JSON.parse(response)
      } catch {
        return [
          'Moderate price stability observed',
          'Supply chain recovering from recent disruptions',
          'Seasonal demand patterns emerging'
        ]
      }
    } catch (error) {
      console.error('Market analysis error:', error)
      return ['Market analysis temporarily unavailable']
    }
  }
  
  /**
   * Generate bulk purchasing recommendations
   */
  private async generateBulkRecommendations(
    materialId: string,
    optimal: any,
    pricingAnalysis: any[]
  ): Promise<string[]> {
    const savingsPercent = ((pricingAnalysis[0].unitCost - optimal.unitCost) / pricingAnalysis[0].unitCost * 100).toFixed(1)
    
    return [
      `Optimal order quantity: ${optimal.quantity} units for ${savingsPercent}% savings`,
      `Consider storage capacity for bulk orders`,
      `Coordinate with other projects for combined purchasing`,
      `Lock in pricing with supplier agreements for stability`,
      `Monitor market trends for optimal timing`
    ]
  }
  
  /**
   * Helper methods
   */
  private calculateAveragePrice(quotes: PriceQuote[]): number {
    return quotes.reduce((sum, q) => sum + q.price, 0) / quotes.length
  }
  
  private getOrInitializeHistory(materialId: string): any {
    if (!this.priceHistory[materialId]) {
      this.priceHistory[materialId] = {
        prices: [],
        lastUpdated: new Date()
      }
    }
    return this.priceHistory[materialId]
  }
  
  private getPreviousPrice(history: any): number | null {
    if (history.prices.length === 0) return null
    return history.prices[history.prices.length - 1].price
  }
  
  private updatePriceHistory(materialId: string, price: number, supplierId: string): void {
    const history = this.priceHistory[materialId]
    history.prices.push({
      date: new Date(),
      price,
      supplierId
    })
    
    // Keep only last 90 days of data
    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
    history.prices = history.prices.filter(p => p.date > ninetyDaysAgo)
    history.lastUpdated = new Date()
  }
  
  private determineTrend(changePercent: number): 'increasing' | 'decreasing' | 'stable' {
    if (changePercent > 2) return 'increasing'
    if (changePercent < -2) return 'decreasing'
    return 'stable'
  }
  
  private calculateVolatility(history: any): 'low' | 'medium' | 'high' {
    if (history.prices.length < 10) return 'low'
    
    const prices = history.prices.map((p: any) => p.price)
    const mean = prices.reduce((sum: number, p: number) => sum + p, 0) / prices.length
    const variance = prices.reduce((sum: number, p: number) => sum + Math.pow(p - mean, 2), 0) / prices.length
    const stdDev = Math.sqrt(variance)
    const coefficientOfVariation = stdDev / mean
    
    if (coefficientOfVariation < 0.1) return 'low'
    if (coefficientOfVariation < 0.2) return 'medium'
    return 'high'
  }
  
  private calculateSupplierReliability(supplierId: string): number {
    // In a real system, this would be based on historical delivery performance
    const reliabilityScores: Record<string, number> = {
      'HOME_DEPOT': 0.95,
      'LOWES': 0.93,
      'FERGUSON': 0.91,
      'GRAINGER': 0.94
    }
    return reliabilityScores[supplierId] || 0.85
  }
  
  private getSeasonalFactors(category: string): SeasonalFactor[] {
    const currentMonth = new Date().getMonth()
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ]
    
    // Construction seasonality patterns
    const patterns: Record<string, number[]> = {
      concrete: [0.8, 0.85, 0.95, 1.1, 1.2, 1.3, 1.3, 1.25, 1.15, 1.05, 0.9, 0.8],
      lumber: [0.9, 0.9, 1.0, 1.1, 1.2, 1.25, 1.3, 1.25, 1.1, 1.0, 0.95, 0.9],
      drywall: [1.0, 1.0, 1.0, 1.05, 1.1, 1.1, 1.05, 1.05, 1.0, 1.0, 1.0, 1.0],
      steel: [0.95, 0.95, 1.0, 1.05, 1.1, 1.15, 1.15, 1.1, 1.05, 1.0, 0.95, 0.95]
    }
    
    const factors = patterns[category] || new Array(12).fill(1.0)
    
    return months.map((month, index) => ({
      month,
      priceFactor: factors[index],
      demandLevel: factors[index] > 1.1 ? 'high' : factors[index] < 0.95 ? 'low' : 'medium'
    }))
  }
  
  private findBestQuote(quotes: PriceQuote[], quantity: number): PriceQuote {
    return quotes.reduce((best, quote) => {
      const currentPrice = this.calculateUnitCost(quote, quantity)
      const bestPrice = this.calculateUnitCost(best, quantity)
      return currentPrice < bestPrice ? quote : best
    })
  }
  
  private calculateUnitCost(quote: PriceQuote, quantity: number): number {
    const applicableBreak = quote.quantityBreaks
      .filter(qb => quantity >= qb.minQuantity && (!qb.maxQuantity || quantity <= qb.maxQuantity))
      .sort((a, b) => b.minQuantity - a.minQuantity)[0]
    
    return applicableBreak?.unitPrice || quote.price
  }
  
  private findOptimalQuantity(
    pricingAnalysis: Array<{ quantity: number; totalCost: number; unitCost: number }>,
    baseQuantity: number
  ): any {
    // Find quantity with lowest unit cost
    return pricingAnalysis.reduce((optimal, current) => 
      current.unitCost < optimal.unitCost ? current : optimal
    )
  }
  
  private calculateBreakEvenPoint(pricingAnalysis: any[]): number {
    if (pricingAnalysis.length < 2) return 0
    
    const baseUnitCost = pricingAnalysis[0].unitCost
    const optimalAnalysis = pricingAnalysis.find(p => p.unitCost < baseUnitCost * 0.95)
    
    if (!optimalAnalysis) return 0
    
    // Calculate how many units at base price equal the bulk purchase cost
    return Math.ceil(optimalAnalysis.totalCost / baseUnitCost)
  }
  
  private calculateSimpleTrend(prices: any[]): number {
    if (prices.length < 2) return 0
    
    const firstPrice = prices[0].price
    const lastPrice = prices[prices.length - 1].price
    return ((lastPrice - firstPrice) / firstPrice) * 100
  }
  
  private checkPriceAlerts(
    material: { id: string; name: string },
    currentPrice: number,
    changePercent: number
  ): void {
    const threshold = this.alertThresholds.get(material.id)
    if (!threshold) return
    
    if (Math.abs(changePercent) >= threshold) {
      const alert: PriceAlert = {
        id: `alert-${Date.now()}`,
        materialId: material.id,
        materialName: material.name,
        alertType: changePercent > 0 ? 'price_increase' : 'price_drop',
        threshold,
        currentValue: changePercent,
        message: `${material.name} price ${changePercent > 0 ? 'increased' : 'decreased'} by ${Math.abs(changePercent).toFixed(1)}%`,
        timestamp: new Date()
      }
      
      this.priceAlerts.push(alert)
      
      // Keep only last 100 alerts
      if (this.priceAlerts.length > 100) {
        this.priceAlerts = this.priceAlerts.slice(-100)
      }
    }
  }
}

// Export singleton instance
export const priceIntelligence = new PriceIntelligenceService()