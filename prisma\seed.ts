import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create demo users
  const demoPassword = await bcrypt.hash('demo123456', 10)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      passwordHash: demoPassword,
      role: 'ADMIN',
      companyType: 'GENERAL',
      companyName: 'AI Construction Corp',
      emailVerified: true,
    },
  })

  const contractorUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON> Contractor',
      passwordHash: demoPassword,
      role: 'CONTRACTOR',
      companyType: 'ELECTRICAL',
      companyName: 'Elite Electrical Solutions',
      emailVerified: true,
    },
  })

  console.log('✅ Created demo users')

  // Create demo projects
  const downtownTower = await prisma.project.create({
    data: {
      name: 'Downtown Tower',
      description: 'A 42-story mixed-use tower in the heart of downtown',
      startDate: new Date('2024-01-15'),
      endDate: new Date('2025-12-31'),
      budget: *********,
      status: 'ACTIVE',
      userId: contractorUser.id,
      projectType: 'Commercial',
      location: 'New York, NY',
      siteArea: 45000,
      constructionType: 'High-Rise',
    },
  })

  const techCampus = await prisma.project.create({
    data: {
      name: 'Tech Campus Expansion',
      description: 'Expanding corporate campus with 3 new buildings',
      startDate: new Date('2024-03-01'),
      endDate: new Date('2025-06-30'),
      budget: 85000000,
      status: 'ACTIVE',
      userId: contractorUser.id,
      projectType: 'Commercial',
      location: 'San Francisco, CA',
      siteArea: 120000,
      constructionType: 'Office Complex',
    },
  })

  console.log('✅ Created demo projects')

  // Create safety reports
  await prisma.safetyReport.createMany({
    data: [
      {
        projectId: downtownTower.id,
        reportDate: new Date('2024-12-20'),
        score: 94,
        violations: '[]',
        incidents: '[]',
        ppeCompliance: 0.96,
        observations: 'All workers following safety protocols. Minor housekeeping issues addressed.',
        recommendations: 'Continue daily safety briefings,Update fall protection equipment',
      },
      {
        projectId: techCampus.id,
        reportDate: new Date('2024-12-19'),
        score: 91,
        violations: '[]',
        incidents: '[]',
        ppeCompliance: 0.93,
        observations: 'Good overall compliance. Need to improve electrical safety practices.',
        recommendations: 'Conduct electrical safety training,Review lockout/tagout procedures',
      },
    ],
  })

  console.log('✅ Created safety reports')

  // Create progress reports
  await prisma.progressReport.createMany({
    data: [
      {
        projectId: downtownTower.id,
        reportDate: new Date('2024-12-22'),
        completion: 67,
        milestones: JSON.stringify([
          { name: 'Foundation Complete', status: 'completed', date: '2024-03-15' },
          { name: 'Structure to Floor 30', status: 'completed', date: '2024-11-30' },
          { name: 'MEP Installation', status: 'in-progress', date: '2024-12-22' },
        ]),
        issues: '[]',
        photoUrls: '',
        notes: 'Project on schedule. Weather has been favorable.',
        weatherConditions: 'Clear, 45°F',
        workersOnSite: 248,
      },
      {
        projectId: techCampus.id,
        reportDate: new Date('2024-12-22'),
        completion: 45,
        milestones: JSON.stringify([
          { name: 'Site Preparation', status: 'completed', date: '2024-04-01' },
          { name: 'Building A Foundation', status: 'completed', date: '2024-08-15' },
          { name: 'Building A Structure', status: 'in-progress', date: '2024-12-22' },
        ]),
        issues: JSON.stringify([
          { description: 'Material delivery delay', impact: 'low', resolution: 'Rescheduled for next week' },
        ]),
        photoUrls: '',
        notes: 'Making good progress despite supply chain challenges.',
        weatherConditions: 'Partly cloudy, 52°F',
        workersOnSite: 186,
      },
    ],
  })

  console.log('✅ Created progress reports')

  // Create schedules
  await prisma.schedule.createMany({
    data: [
      {
        projectId: downtownTower.id,
        taskName: 'Complete MEP Rough-In Floors 25-30',
        description: 'Install all mechanical, electrical, and plumbing systems',
        startDate: new Date('2024-12-23'),
        endDate: new Date('2025-01-15'),
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        dependencies: '',
        assignedTo: 'team-mep-1,team-mep-2',
      },
      {
        projectId: downtownTower.id,
        taskName: 'Interior Framing Floors 20-25',
        description: 'Complete interior wall framing and partitions',
        startDate: new Date('2024-12-26'),
        endDate: new Date('2025-01-10'),
        status: 'NOT_STARTED',
        priority: 'MEDIUM',
        dependencies: 'mep-rough-in',
        assignedTo: 'team-framing-1',
      },
      {
        projectId: techCampus.id,
        taskName: 'Building A - Structural Steel to Level 4',
        description: 'Erect structural steel framework',
        startDate: new Date('2024-12-23'),
        endDate: new Date('2025-01-05'),
        status: 'IN_PROGRESS',
        priority: 'CRITICAL',
        dependencies: '',
        assignedTo: 'team-steel-1,team-steel-2',
      },
    ],
  })

  console.log('✅ Created schedules')

  // Create notifications
  await prisma.notification.createMany({
    data: [
      {
        userId: contractorUser.id,
        title: 'Safety Score Update',
        message: 'Your project "Downtown Tower" achieved a safety score of 94/100 this week.',
        type: 'SUCCESS',
        actionUrl: `/dashboard/projects/${downtownTower.id}/safety`,
      },
      {
        userId: contractorUser.id,
        title: 'Schedule Alert',
        message: 'MEP installation is on critical path. Review schedule to prevent delays.',
        type: 'WARNING',
        actionUrl: `/dashboard/projects/${downtownTower.id}/scheduling`,
      },
    ],
  })

  console.log('✅ Created notifications')

  console.log('🎉 Database seed completed successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('Error seeding database:', e)
    await prisma.$disconnect()
    process.exit(1)
  })