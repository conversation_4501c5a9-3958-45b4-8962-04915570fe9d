const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

// Load environment variables from .env.local
function loadEnvFile() {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const env = {};
    
    envContent.split('\n').forEach(line => {
        // Skip comments and empty lines
        if (line.trim() && !line.trim().startsWith('#')) {
            const [key, ...valueParts] = line.split('=');
            if (key && valueParts.length > 0) {
                env[key.trim()] = valueParts.join('=').trim();
            }
        }
    });
    
    return env;
}

// Execute command and log output
function exec(command, hideOutput = false) {
    try {
        const output = execSync(command, { encoding: 'utf8' });
        if (!hideOutput && output) {
            console.log(output);
        }
        return output;
    } catch (error) {
        if (error.stdout) console.log(error.stdout.toString());
        if (error.stderr) console.error(error.stderr.toString());
        throw error;
    }
}

// Main function
async function main() {
    console.log('🔐 Setting up Git with GitHub token authentication...\n');
    
    // Load environment variables
    const env = loadEnvFile();
    const githubToken = env.GITHUB_TOKEN;
    const githubUsername = env.GITHUB_USERNAME || 'mikeaper323';
    const githubEmail = env.GITHUB_EMAIL;
    
    // Validate token
    if (!githubToken || githubToken === 'your-github-personal-access-token-here') {
        console.error('❌ GitHub token not configured!\n');
        console.log('Please update .env.local with your GitHub Personal Access Token:');
        console.log('1. Go to: https://github.com/settings/tokens');
        console.log('2. Click "Generate new token (classic)"');
        console.log('3. Give it a name (e.g., "AI Construction Push")');
        console.log('4. Select "repo" scope for full access');
        console.log('5. Generate token and copy it');
        console.log('6. Update GITHUB_TOKEN in .env.local');
        console.log('7. Update GITHUB_EMAIL in .env.local with your email');
        process.exit(1);
    }
    
    console.log('✅ Found GitHub configuration');
    console.log(`   Username: ${githubUsername}`);
    console.log(`   Email: ${githubEmail}\n`);
    
    try {
        // Configure git user
        console.log('📝 Configuring git user...');
        exec(`git config user.name "${githubUsername}"`);
        exec(`git config user.email "${githubEmail}"`);
        
        // Check if repository is initialized
        if (!fs.existsSync('.git')) {
            console.log('📁 Initializing git repository...');
            exec('git init');
            exec('git checkout -b main');
        }
        
        // Create authenticated remote URL
        const remoteUrl = `https://${githubUsername}:${githubToken}@github.com/mikeaper323/AI-Construction.git`;
        
        // Check if remote exists
        try {
            const remotes = exec('git remote', true).trim().split('\n');
            if (remotes.includes('origin')) {
                console.log('🔗 Updating remote origin with authentication...');
                exec(`git remote set-url origin "${remoteUrl}"`);
            } else {
                console.log('🔗 Adding remote origin with authentication...');
                exec(`git remote add origin "${remoteUrl}"`);
            }
        } catch {
            console.log('🔗 Adding remote origin with authentication...');
            exec(`git remote add origin "${remoteUrl}"`);
        }
        
        // Add all files
        console.log('\n➕ Adding all files...');
        exec('git add .');
        
        // Show status
        console.log('\n📊 Files to commit:');
        exec('git status --short');
        
        // Commit
        const commitMessage = `fix: resolve 404 errors and add comprehensive settings page

- Create settings page at /dashboard/settings with 8 configuration sections
- Add Chrome DevTools configuration file (.well-known/appspecific/com.chrome.devtools.json)
- Implement complete settings UI with profile, notifications, security, API keys, display, organization, integrations, and AI settings
- Add Gemini AI integration with construction-specific features
- Implement complete dashboard with real-time metrics
- Add verification and utility scripts
- Configure environment variables for all services

Features:
- Full settings management interface
- Google Gemini AI integration
- Construction-specific AI responses
- Real-time project dashboards
- 404 error fixes

Fixes:
- GET /dashboard/settings 404 error
- GET /.well-known/appspecific/com.chrome.devtools.json 404 error`;
        
        console.log('\n💬 Committing changes...');
        exec(`git commit -m "${commitMessage}"`);
        
        // Push to GitHub
        console.log('\n🚀 Pushing to GitHub...');
        exec('git push -u origin main');
        
        console.log('\n✅ Successfully pushed to GitHub!');
        console.log('View your repository at: https://github.com/mikeaper323/AI-Construction');
        
        // Clean up by removing token from remote URL for security
        console.log('\n🔒 Cleaning up authentication...');
        exec('git remote set-url origin https://github.com/mikeaper323/AI-Construction.git');
        
    } catch (error) {
        console.error('\n❌ Operation failed!');
        console.error('Error:', error.message);
        
        // Clean up token from URL for security
        try {
            exec('git remote set-url origin https://github.com/mikeaper323/AI-Construction.git');
        } catch {}
        
        process.exit(1);
    }
}

// Run the script
main().catch(console.error);
