# AI Construction Management Platform

A cutting-edge construction management platform that combines AI-powered analytics, BIM integration, and real-time collaboration to revolutionize how construction projects are planned, executed, and monitored.

## 🚀 Overview

This platform integrates the best features from industry leaders (Procore, OpenSpace, ALICE Technologies, Buildots, Doxel, Togal.AI, Dusty Robotics, Newmetrix/Smartvid) into a unified solution, enhanced with Google Gemini AI for intelligent insights and automation.

## ✨ Key Features

### 🤖 AI-Powered Intelligence
- **Smart Document Analysis**: Automated takeoff and quantity extraction from blueprints with 98% accuracy
- **Predictive Analytics**: Schedule optimization and risk assessment using millions of scenarios
- **Natural Language Assistant**: Construction-specific AI chat interface powered by Google Gemini
- **Company-Specific AI**: Tailored responses for 20+ contractor specializations

### 🏗️ Project Management
- **Multi-Project Dashboard**: Real-time overview of all active projects
- **BIM Integration**: 3D model viewing and collaboration
- **Resource Management**: Equipment, materials, and workforce tracking
- **Document Control**: Version management and approval workflows with AI parsing

### 📊 Progress Tracking & Monitoring
- **360° Reality Capture**: Integration with field scanning devices
- **AI Progress Analysis**: Automated percentage complete calculations
- **BIM Comparison**: Real-time as-built vs. as-planned analysis
- **Deviation Detection**: Automatic identification of schedule and quality issues

### 📅 AI-Powered Scheduling
- **Scenario Generation**: Create millions of optimized schedule alternatives
- **What-If Analysis**: Test impact of changes before implementation
- **Weather Integration**: Automatic schedule adjustments for weather delays
- **Resource Leveling**: AI-optimized resource allocation

### 🛡️ Safety & Compliance
- **Predictive Safety Analytics**: AI-driven incident prevention
- **Real-Time Hazard Detection**: Computer vision for PPE and safety compliance
- **Incident Tracking**: Comprehensive safety management system
- **Behavioral Analytics**: Identify safety trends and risk patterns

### 📐 Takeoff & Estimating
- **AI-Powered Takeoff**: 98% accurate quantity extraction from drawings
- **Multi-Format Support**: PDF, DWG, DXF, and image files
- **Natural Language Queries**: Ask questions about plans in plain English
- **Real-Time Pricing**: Integration with material and labor databases

### 👷 Field Operations
- **Mobile Apps**: iOS/Android apps for field workers
- **Offline Sync**: Work without connectivity, sync when online
- **Digital Work Packages**: Paperless task management
- **IoT Integration**: Real-time sensor data from job sites
- **AR Instructions**: Augmented reality guidance for complex tasks

### 📈 Analytics & Reporting
- **Real-Time KPIs**: Budget, schedule, safety, and quality metrics
- **Predictive Insights**: AI-driven forecasting and recommendations
- **Custom Reports**: Flexible reporting engine with export capabilities
- **Portfolio Analytics**: Cross-project insights and benchmarking

### 💰 Financial Management
- **Budget Tracking**: Real-time cost monitoring and forecasting
- **Change Order Management**: Streamlined approval workflows
- **Invoice Processing**: Automated matching and approval
- **Cash Flow Prediction**: AI-powered financial forecasting

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript 5.x
- **UI Library**: React 18
- **Styling**: Tailwind CSS + shadcn/ui (Radix UI)
- **State Management**: Zustand + React Query
- **Forms**: React Hook Form + Zod validation
- **3D Graphics**: Three.js + React Three Fiber
- **Charts**: Recharts
- **Maps**: Mapbox GL
- **Real-time**: Socket.io Client
- **Animations**: Framer Motion

### Backend
- **Runtime**: Node.js + Next.js API Routes
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with jose library
- **AI Integration**: Google Gemini AI (gemini-2.0-flash)
- **File Storage**: Local + Cloud (S3 compatible)
- **Real-time**: Socket.io Server
- **Queue**: In-memory job processing

### Infrastructure
- **Deployment**: Vercel / Docker
- **CI/CD**: GitHub Actions
- **Monitoring**: Built-in analytics
- **Security**: JWT auth, role-based access control

## 📋 Prerequisites

- Node.js 18.x or higher
- PostgreSQL 14.x or higher
- npm or yarn package manager
- Google Gemini API key

## 🚀 Getting Started

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/ai-construction-management.git
cd ai-construction-management
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
```

### 3. Environment Setup

Create a `.env.local` file in the root directory:

```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/construction_db"

# Authentication
JWT_SECRET="your-super-secret-jwt-key"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# AI Configuration
GEMINI_API_KEY="your-gemini-api-key"
GEMINI_MODEL="gemini-2.0-flash"

# External Services
NEXT_PUBLIC_MAPBOX_TOKEN="your-mapbox-token"

# Application
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### 4. Database Setup

```bash
# Run database migrations
npx prisma migrate dev

# Seed initial data (optional)
npx prisma db seed
```

### 5. Start Development Server

```bash
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:3000`

## 📱 Default Login Credentials

For development/demo purposes:

- **Admin**: <EMAIL> / admin123
- **Project Manager**: <EMAIL> / pm123
- **Field Worker**: <EMAIL> / field123

## 🧪 Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run type checking
npm run type-check

# Run linting
npm run lint
```

## 📦 Building for Production

```bash
# Build the application
npm run build

# Start production server
npm run start
```

## 🗂️ Project Structure

```
ai-construction-management/
├── src/
│   ├── app/                    # Next.js app router pages
│   │   ├── api/               # API routes
│   │   ├── dashboard/         # Main application pages
│   │   │   ├── projects/      # Multi-project management
│   │   │   ├── scheduling/    # AI-powered scheduling
│   │   │   ├── progress/      # Progress tracking with BIM
│   │   │   ├── safety/        # Predictive safety monitoring
│   │   │   ├── estimating/    # AI takeoff & estimating
│   │   │   ├── field/         # Field operations & robotics
│   │   │   ├── analytics/     # KPI & predictive analytics
│   │   │   ├── ai-assistant/  # Gemini-powered chat
│   │   │   └── settings/      # Configuration
│   │   └── (auth)/           # Authentication pages
│   ├── components/            # Reusable UI components
│   ├── features/              # Feature-specific components
│   ├── lib/                   # Core libraries and utilities
│   │   ├── gemini.ts         # AI service singleton
│   │   ├── services/         # Business logic
│   │   └── company-types.ts  # Company configurations
│   ├── hooks/                # Custom React hooks
│   ├── types/                # TypeScript type definitions
│   └── styles/               # Global styles
├── public/                    # Static assets
├── prisma/                    # Database schema and migrations
├── docs/                      # Documentation
└── tests/                     # Test files
```

## 🔧 Essential Commands

```bash
# Development
npm run dev          # Start development server on port 3000

# Code Quality - ALWAYS run before completing tasks
npm run lint         # Run ESLint checks
npm run type-check   # TypeScript type checking

# Testing
npm run test         # Run Jest tests
npm run test:watch   # Run tests in watch mode

# Production
npm run build        # Build for production
npm run start        # Start production server
```

## 🏗️ Architecture Highlights

### AI Integration Pattern
- Singleton Gemini service with company-specific contexts
- 20+ contractor specializations with tailored AI responses
- Structured prompts for construction analysis (progress, safety, schedule, cost, quality)

### Type System
- Comprehensive TypeScript types for all entities
- Company-specific details mapping
- Zod validation schemas for runtime safety

### Component Architecture
- Feature-based organization
- Server/Client component separation
- Compound component patterns for complex UIs

## 🤝 Contributing

Please read our [Contributing Guidelines](CONTRIBUTING.md) before submitting pull requests.

### Development Workflow
1. Always check existing patterns before implementing new features
2. Use path aliases (@/) for imports
3. Follow existing component structure and naming conventions
4. Run lint and type-check before considering tasks complete
5. Test with multiple contractor types to ensure AI responses are appropriate

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Join our community discussions
- **Email**: <EMAIL>

## 🙏 Acknowledgments

Built with inspiration from industry leaders:
- Procore
- OpenSpace
- ALICE Technologies
- Buildots
- Doxel
- Togal.AI
- Dusty Robotics
- Newmetrix/Smartvid

Special thanks to all contributors and the open-source community.
