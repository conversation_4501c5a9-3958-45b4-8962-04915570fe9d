import { NextRequest } from 'next/server';

// Simple in-memory metrics storage
// In production, consider using a proper metrics library like prom-client
class MetricsCollector {
  private metrics: Map<string, any> = new Map();
  private counters: Map<string, number> = new Map();
  private histograms: Map<string, number[]> = new Map();

  constructor() {
    // Initialize basic metrics
    this.setGauge('nodejs_version', process.version);
    this.setGauge('app_start_time', Date.now());
  }

  incrementCounter(name: string, value: number = 1, labels?: Record<string, string>) {
    const key = this.getKey(name, labels);
    const current = this.counters.get(key) || 0;
    this.counters.set(key, current + value);
  }

  setGauge(name: string, value: any, labels?: Record<string, string>) {
    const key = this.getKey(name, labels);
    this.metrics.set(key, value);
  }

  recordHistogram(name: string, value: number, labels?: Record<string, string>) {
    const key = this.getKey(name, labels);
    const values = this.histograms.get(key) || [];
    values.push(value);
    this.histograms.set(key, values);
  }

  private getKey(name: string, labels?: Record<string, string>): string {
    if (!labels) return name;
    const labelStr = Object.entries(labels)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}="${v}"`)
      .join(',');
    return `${name}{${labelStr}}`;
  }

  // Export metrics in Prometheus format
  exportMetrics(): string {
    const lines: string[] = [];
    
    // Export counters
    for (const [key, value] of Array.from(this.counters.entries())) {
      lines.push(`${key} ${value}`);
    }

    // Export gauges
    for (const [key, value] of Array.from(this.metrics.entries())) {
      if (typeof value === 'number') {
        lines.push(`${key} ${value}`);
      }
    }

    // Export histograms (simplified - just count and sum)
    for (const [key, values] of Array.from(this.histograms.entries())) {
      const sum = values.reduce((a: number, b: number) => a + b, 0);
      const count = values.length;
      const baseName = key.replace(/{.*}/, '');
      const labels = key.match(/{.*}/)?.[0] || '';
      
      lines.push(`${baseName}_sum${labels} ${sum}`);
      lines.push(`${baseName}_count${labels} ${count}`);
    }

    // Add process metrics
    const memUsage = process.memoryUsage();
    lines.push(`nodejs_heap_size_total_bytes ${memUsage.heapTotal}`);
    lines.push(`nodejs_heap_size_used_bytes ${memUsage.heapUsed}`);
    lines.push(`nodejs_external_memory_bytes ${memUsage.external}`);
    lines.push(`process_cpu_seconds_total ${process.cpuUsage().user / 1000000}`);
    lines.push(`process_uptime_seconds ${process.uptime()}`);

    return lines.join('\n');
  }
}

// Global metrics instance
export const metrics = new MetricsCollector();

// Middleware helper to track HTTP metrics
export function trackHttpMetrics(
  request: NextRequest,
  response: Response,
  duration: number
) {
  const route = request.nextUrl.pathname;
  const method = request.method;
  const status = response.status;
  const statusClass = `${Math.floor(status / 100)}xx`;

  // Increment request counter
  metrics.incrementCounter('http_requests_total', 1, {
    method,
    route,
    status: status.toString(),
    status_class: statusClass,
  });

  // Record response time
  metrics.recordHistogram('http_request_duration_seconds', duration / 1000, {
    method,
    route,
  });

  // Track error rate
  if (status >= 400) {
    metrics.incrementCounter('http_errors_total', 1, {
      method,
      route,
      status: status.toString(),
    });
  }
}

// Track custom business metrics
export function trackBusinessMetric(name: string, value: number, labels?: Record<string, string>) {
  metrics.incrementCounter(name, value, labels);
}

// Track AI API usage
export function trackAIUsage(provider: string, success: boolean, tokens?: number) {
  metrics.incrementCounter('ai_api_requests_total', 1, {
    provider,
    success: success.toString(),
  });

  if (tokens) {
    metrics.incrementCounter('ai_api_tokens_total', tokens, { provider });
  }

  if (!success) {
    metrics.incrementCounter('ai_api_errors_total', 1, { provider });
  }
}

// Track job processing metrics
export function trackJobMetrics(jobType: string, status: 'started' | 'completed' | 'failed', duration?: number) {
  metrics.incrementCounter('jobs_total', 1, {
    type: jobType,
    status,
  });

  if (duration) {
    metrics.recordHistogram('job_duration_seconds', duration / 1000, {
      type: jobType,
    });
  }

  // Update current job counts
  if (status === 'started') {
    metrics.setGauge('jobs_processing', 
      (metrics as any).metrics.get('jobs_processing') + 1 || 1
    );
  } else {
    metrics.setGauge('jobs_processing', 
      Math.max(0, (metrics as any).metrics.get('jobs_processing') - 1 || 0)
    );
  }
}

// Track database metrics
export function trackDatabaseMetrics(operation: string, duration: number, success: boolean) {
  metrics.recordHistogram('database_operation_duration_seconds', duration / 1000, {
    operation,
    success: success.toString(),
  });

  metrics.incrementCounter('database_operations_total', 1, {
    operation,
    success: success.toString(),
  });
}

// Track WebSocket connections
export function trackWebSocketMetrics(event: 'connect' | 'disconnect') {
  const currentConnections = (metrics as any).metrics.get('websocket_connections_active') || 0;
  
  if (event === 'connect') {
    metrics.setGauge('websocket_connections_active', currentConnections + 1);
    metrics.incrementCounter('websocket_connections_total', 1);
  } else {
    metrics.setGauge('websocket_connections_active', Math.max(0, currentConnections - 1));
  }
}