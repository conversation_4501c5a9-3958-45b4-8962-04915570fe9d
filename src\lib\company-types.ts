// Shared company type definitions and helper functions
// This file can be used on both client and server side

// Project context interface
export interface ProjectContext {
  id: string
  name: string
  type: 'commercial' | 'residential' | 'industrial' | 'institutional'
  location: {
    address: string
    zipCode: string
    coordinates?: { lat: number; lng: number }
  }
  specifications?: Record<string, any>
  companyType?: CompanyType | null
}

export type CompanyType = 
  | 'General Contractor'
  | 'Electrical Contractor'
  | 'Plumbing Contractor'
  | 'HVAC Contractor'
  | 'Roofing Contractor'
  | 'Concrete Contractor'
  | 'Steel/Metal Contractor'
  | 'Masonry Contractor'
  | 'Painting Contractor'
  | 'Flooring Contractor'
  | 'Landscaping Contractor'
  | 'Demolition Contractor'
  | 'Excavation/Earthwork Contractor'
  | 'Glass & Glazing Contractor'
  | 'Insulation Contractor'
  | 'Drywall Contractor'
  | 'Fire Protection Contractor'
  | 'Elevator Contractor'
  | 'Solar/Renewable Energy Contractor'
  | 'Marine/Underwater Contractor'

export interface CompanyTypeDetails {
  specializations: string[]
  commonTools: string[]
  regulations: string[]
  certifications: string[]
}

// Company type details mapping
export const companyTypeDetailsMap: Record<CompanyType, CompanyTypeDetails> = {
  'General Contractor': {
    specializations: ['Project Management', 'Coordination', 'Site Management', 'Subcontractor Management'],
    commonTools: ['Project Management Software', 'Scheduling Tools', 'BIM Software', 'Cost Estimation Tools'],
    regulations: ['OSHA General Industry', 'Building Codes', 'Zoning Laws', 'Environmental Regulations'],
    certifications: ['General Contractor License', 'OSHA 30-Hour', 'LEED AP', 'PMP']
  },
  'Electrical Contractor': {
    specializations: ['Power Distribution', 'Lighting Systems', 'Fire Alarms', 'Data/Communications', 'Solar Installation'],
    commonTools: ['Multimeters', 'Wire Strippers', 'Conduit Benders', 'Voltage Testers', 'CAD Software'],
    regulations: ['NEC (National Electrical Code)', 'NFPA 70E', 'Local Electrical Codes', 'OSHA Electrical Standards'],
    certifications: ['Master Electrician License', 'Journeyman License', 'OSHA Electrical Safety', 'NICET Certification']
  },
  'Plumbing Contractor': {
    specializations: ['Water Supply', 'Drainage Systems', 'Gas Piping', 'Fixture Installation', 'Backflow Prevention'],
    commonTools: ['Pipe Wrenches', 'Drain Snakes', 'Pipe Cutters', 'Soldering Tools', 'Camera Inspection'],
    regulations: ['IPC (International Plumbing Code)', 'UPC (Uniform Plumbing Code)', 'EPA Water Regulations', 'Local Plumbing Codes'],
    certifications: ['Master Plumber License', 'Journeyman License', 'Backflow Prevention Certification', 'Medical Gas Certification']
  },
  'HVAC Contractor': {
    specializations: ['Heating Systems', 'Cooling Systems', 'Ventilation', 'Controls/Automation', 'Energy Efficiency'],
    commonTools: ['Refrigerant Gauges', 'Multimeters', 'Duct Tools', 'Vacuum Pumps', 'Load Calculation Software'],
    regulations: ['EPA Section 608', 'ASHRAE Standards', 'Energy Codes', 'Indoor Air Quality Standards'],
    certifications: ['EPA Certification', 'NATE Certification', 'HVAC Excellence', 'R-410A Certification']
  },
  'Roofing Contractor': {
    specializations: ['Shingle Roofing', 'Metal Roofing', 'Flat/Commercial Roofing', 'Roof Repairs', 'Waterproofing'],
    commonTools: ['Nail Guns', 'Roofing Shovels', 'Seaming Tools', 'Safety Harnesses', 'Moisture Meters'],
    regulations: ['OSHA Fall Protection', 'Building Codes', 'Fire Ratings', 'Wind Uplift Requirements'],
    certifications: ['Roofing License', 'Manufacturer Certifications', 'OSHA Safety', 'Green Roof Professional']
  },
  'Concrete Contractor': {
    specializations: ['Foundations', 'Slabs', 'Decorative Concrete', 'Structural Concrete', 'Concrete Repair'],
    commonTools: ['Concrete Mixers', 'Vibrators', 'Screeds', 'Trowels', 'Concrete Saws'],
    regulations: ['ACI Standards', 'OSHA Concrete/Masonry', 'Environmental Protection', 'Quality Control Standards'],
    certifications: ['ACI Certification', 'NRMCA Certification', 'Concrete Field Testing', 'Flatwork Finisher']
  },
  'Steel/Metal Contractor': {
    specializations: ['Structural Steel', 'Metal Decking', 'Ornamental Metal', 'Steel Erection', 'Welding'],
    commonTools: ['Welding Equipment', 'Cutting Torches', 'Cranes', 'Ironworker Tools', 'Fabrication Software'],
    regulations: ['AISC Standards', 'AWS Welding Codes', 'OSHA Steel Erection', 'Structural Codes'],
    certifications: ['AWS Welding Certification', 'AISC Certification', 'Crane Operator License', 'Rigger Certification']
  },
  'Masonry Contractor': {
    specializations: ['Brick Work', 'Block Work', 'Stone Work', 'Restoration', 'Refractory Work'],
    commonTools: ['Trowels', 'Levels', 'Masonry Saws', 'Mixers', 'Scaffolding'],
    regulations: ['TMS Standards', 'Building Codes', 'OSHA Masonry Standards', 'Historic Preservation Guidelines'],
    certifications: ['Mason Certification', 'Historic Masonry Certification', 'Safety Certifications', 'Restoration Specialist']
  },
  'Painting Contractor': {
    specializations: ['Interior Painting', 'Exterior Painting', 'Industrial Coatings', 'Decorative Finishes', 'Lead Paint Removal'],
    commonTools: ['Sprayers', 'Brushes/Rollers', 'Sanders', 'Pressure Washers', 'Safety Equipment'],
    regulations: ['EPA RRP Rule', 'VOC Regulations', 'Lead Paint Standards', 'OSHA Respiratory Protection'],
    certifications: ['EPA RRP Certification', 'PDCA Certification', 'Industrial Coating Certifications', 'Safety Training']
  },
  'Flooring Contractor': {
    specializations: ['Hardwood', 'Carpet', 'Tile/Stone', 'Vinyl/LVT', 'Polished Concrete'],
    commonTools: ['Floor Sanders', 'Tile Saws', 'Carpet Tools', 'Moisture Meters', 'Leveling Tools'],
    regulations: ['Floor Covering Standards', 'ADA Compliance', 'Slip Resistance Standards', 'VOC Emissions'],
    certifications: ['CFI Certification', 'NWFA Certification', 'Tile Certification', 'Manufacturer Certifications']
  },
  'Landscaping Contractor': {
    specializations: ['Design/Build', 'Irrigation', 'Hardscaping', 'Tree Care', 'Maintenance'],
    commonTools: ['Excavators', 'Mowers', 'Irrigation Tools', 'Design Software', 'Tree Equipment'],
    regulations: ['Water Conservation Laws', 'Pesticide Regulations', 'Environmental Protection', 'Local Ordinances'],
    certifications: ['Landscape Contractor License', 'ISA Arborist', 'Irrigation Certification', 'Pesticide License']
  },
  'Demolition Contractor': {
    specializations: ['Structural Demolition', 'Interior Demolition', 'Selective Demolition', 'Implosion', 'Hazardous Material Removal'],
    commonTools: ['Excavators', 'Wrecking Balls', 'Concrete Crushers', 'Cutting Equipment', 'Safety Gear'],
    regulations: ['OSHA Demolition Standards', 'EPA Regulations', 'Asbestos/Lead Rules', 'Waste Disposal Laws'],
    certifications: ['Demolition License', 'Asbestos Supervisor', 'Lead RRP', 'Explosive Handler License']
  },
  'Excavation/Earthwork Contractor': {
    specializations: ['Site Preparation', 'Grading', 'Trenching', 'Foundation Excavation', 'Earth Moving'],
    commonTools: ['Excavators', 'Bulldozers', 'Compactors', 'GPS Systems', 'Laser Levels'],
    regulations: ['OSHA Excavation Standards', 'Erosion Control', 'Underground Utility Protection', 'Environmental Protection'],
    certifications: ['Heavy Equipment Operator', 'Competent Person Training', 'Erosion Control Certification', 'CDL License']
  },
  'Glass & Glazing Contractor': {
    specializations: ['Storefronts', 'Curtain Walls', 'Skylights', 'Mirrors', 'Safety Glass'],
    commonTools: ['Glass Cutters', 'Suction Cups', 'Glazing Tools', 'Sealant Guns', 'Measuring Equipment'],
    regulations: ['Safety Glazing Standards', 'Energy Codes', 'Wind Load Requirements', 'Building Codes'],
    certifications: ['Glazier Certification', 'Manufacturer Certifications', 'Safety Training', 'Architectural Glass Certification']
  },
  'Insulation Contractor': {
    specializations: ['Thermal Insulation', 'Sound Insulation', 'Fireproofing', 'Spray Foam', 'Mechanical Insulation'],
    commonTools: ['Spray Equipment', 'Cutting Tools', 'Blowers', 'Safety Equipment', 'Thermal Cameras'],
    regulations: ['Energy Codes', 'Fire Codes', 'Environmental Regulations', 'Health and Safety Standards'],
    certifications: ['Insulation Contractor License', 'SPFA Certification', 'Mechanical Insulation Certification', 'Energy Auditor']
  },
  'Drywall Contractor': {
    specializations: ['Hanging', 'Taping/Finishing', 'Texturing', 'Metal Framing', 'Acoustic Ceilings'],
    commonTools: ['Screw Guns', 'Taping Tools', 'Sanders', 'Texture Sprayers', 'Lifts'],
    regulations: ['Fire-Rated Assembly Standards', 'Sound Transmission Standards', 'Building Codes', 'Safety Standards'],
    certifications: ['Drywall Certification', 'AWCI Certification', 'Safety Training', 'Acoustic Ceiling Certification']
  },
  'Fire Protection Contractor': {
    specializations: ['Sprinkler Systems', 'Fire Alarms', 'Fire Suppression', 'Fire Pumps', 'Emergency Lighting'],
    commonTools: ['Pipe Threading Tools', 'Testing Equipment', 'Alarm Programming Tools', 'Hydrostatic Test Pumps'],
    regulations: ['NFPA Standards', 'Fire Codes', 'Insurance Requirements', 'AHJ Requirements'],
    certifications: ['NICET Certification', 'Fire Protection License', 'Backflow Prevention', 'Alarm System Certification']
  },
  'Elevator Contractor': {
    specializations: ['Installation', 'Modernization', 'Maintenance', 'Repairs', 'Escalators'],
    commonTools: ['Specialized Elevator Tools', 'Testing Equipment', 'Control System Tools', 'Safety Equipment'],
    regulations: ['ASME A17.1', 'ADA Requirements', 'State Elevator Codes', 'Safety Standards'],
    certifications: ['Elevator Mechanic License', 'QEI Certification', 'Manufacturer Training', 'Safety Certifications']
  },
  'Solar/Renewable Energy Contractor': {
    specializations: ['Solar PV', 'Solar Thermal', 'Wind Systems', 'Battery Storage', 'Energy Management'],
    commonTools: ['Solar Testing Equipment', 'DC Tools', 'Racking Systems', 'Inverters', 'Design Software'],
    regulations: ['NEC Article 690', 'Interconnection Standards', 'Building Codes', 'Utility Requirements'],
    certifications: ['NABCEP Certification', 'Electrical License', 'Manufacturer Certifications', 'Safety Training']
  },
  'Marine/Underwater Contractor': {
    specializations: ['Underwater Construction', 'Marine Structures', 'Dredging', 'Pile Driving', 'Salvage Operations'],
    commonTools: ['Diving Equipment', 'Underwater Tools', 'Marine Construction Equipment', 'ROVs', 'Sonar Equipment'],
    regulations: ['OSHA Commercial Diving', 'Coast Guard Regulations', 'Environmental Protection', 'Maritime Laws'],
    certifications: ['Commercial Diving Certification', 'Marine Construction Certification', 'Captain\'s License', 'Underwater Welding']
  }
}

// Helper function to get company-specific AI context
export function getCompanyAIContext(companyType: CompanyType | null): string {
  if (!companyType) return ''

  const details = companyTypeDetailsMap[companyType]
  if (!details) return ''

  return `
Company Type: ${companyType}
Specializations: ${details.specializations.join(', ')}
Common Tools & Equipment: ${details.commonTools.join(', ')}
Key Regulations: ${details.regulations.join(', ')}
Required Certifications: ${details.certifications.join(', ')}

Please tailor your responses specifically for a ${companyType} business, focusing on:
- Industry-specific terminology and practices
- Relevant regulations and compliance requirements
- Common challenges faced by ${companyType}s
- Best practices specific to this trade
- Tools and technologies commonly used in this field
`
}
