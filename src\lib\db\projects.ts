import prisma from '@/lib/prisma'
import { getCurrentUser } from './auth'
import { Project, Prisma } from '@prisma/client'

export interface CreateProjectInput {
  name: string
  description?: string
  startDate: Date
  endDate: Date
  budget: number
  projectType: string
  location: string
  siteArea?: number
  constructionType?: string
}

export interface UpdateProjectInput extends Partial<CreateProjectInput> {
  status?: 'PLANNING' | 'ACTIVE' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
}

// Create a new project
export async function createProject(data: CreateProjectInput): Promise<Project> {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  const project = await prisma.project.create({
    data: {
      ...data,
      userId: user.id,
    },
  })

  // Log activity
  await prisma.activity.create({
    data: {
      userId: user.id,
      projectId: project.id,
      action: 'PROJECT_CREATED',
      description: `Created project: ${project.name}`,
    },
  })

  return project
}

// Get all projects for current user
export async function getUserProjects(filters?: {
  status?: string
  search?: string
  limit?: number
  offset?: number
}) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  const where: Prisma.ProjectWhereInput = {
    userId: user.id,
  }

  if (filters?.status) {
    where.status = filters.status as any
  }

  if (filters?.search) {
    where.OR = [
      { name: { contains: filters.search } },
      { description: { contains: filters.search } },
      { location: { contains: filters.search } },
    ]
  }

  const [projects, total] = await Promise.all([
    prisma.project.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: filters?.limit || 10,
      skip: filters?.offset || 0,
      select: {
        id: true,
        name: true,
        description: true,
        status: true,
      },
    }),
    prisma.project.count({ where }),
  ])

  return { projects, total }
}

// Get single project with details
export async function getProject(projectId: string) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  const project = await prisma.project.findFirst({
    where: {
      id: projectId,
      userId: user.id,
    },
    include: {
      schedules: {
        where: { status: { not: 'COMPLETED' } },
        orderBy: { startDate: 'asc' },
        take: 5,
      },
      safety: {
        orderBy: { reportDate: 'desc' },
        take: 1,
      },
      progress: {
        orderBy: { reportDate: 'desc' },
        take: 1,
      },
      takeoffs: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
      documents: {
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
      _count: {
        select: {
          schedules: true,
          safety: true,
          progress: true,
          takeoffs: true,
          documents: true,
        },
      },
    },
  })

  if (!project) {
    throw new Error('Project not found')
  }

  return project
}

// Update project
export async function updateProject(projectId: string, data: UpdateProjectInput) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  const project = await prisma.project.update({
    where: {
      id: projectId,
      userId: user.id,
    },
    data,
  })

  // Log activity
  await prisma.activity.create({
    data: {
      userId: user.id,
      projectId: project.id,
      action: 'PROJECT_UPDATED',
      description: `Updated project: ${project.name}`,
      metadata: data as any,
    },
  })

  return project
}

// Delete project
export async function deleteProject(projectId: string) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  await prisma.project.delete({
    where: {
      id: projectId,
      userId: user.id,
    },
  })

  // Log activity
  await prisma.activity.create({
    data: {
      userId: user.id,
      action: 'PROJECT_DELETED',
      description: `Deleted project ID: ${projectId}`,
    },
  })
}

// Get project statistics
export async function getProjectStats(projectId: string) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  const project = await getProject(projectId)

  // Get latest progress
  const latestProgress = await prisma.progressReport.findFirst({
    where: { projectId },
    orderBy: { reportDate: 'desc' },
  })

  // Get safety metrics
  const safetyReports = await prisma.safetyReport.findMany({
    where: { projectId },
    orderBy: { reportDate: 'desc' },
    take: 30,
  })

  const avgSafetyScore = safetyReports.length > 0
    ? safetyReports.reduce((sum, report) => sum + report.score, 0) / safetyReports.length
    : 0

  // Get schedule metrics
  const schedules = await prisma.schedule.findMany({
    where: { projectId },
  })

  const completedTasks = schedules.filter(s => s.status === 'COMPLETED').length
  const totalTasks = schedules.length
  const delayedTasks = schedules.filter(s => s.status === 'DELAYED').length

  // Get budget metrics
  const takeoffs = await prisma.takeoff.findMany({
    where: { projectId },
    select: { totalCost: true },
  })

  const totalSpent = takeoffs.reduce((sum, t) => sum + (t.totalCost || 0), 0)
  const budgetUtilization = project.budget > 0 ? (totalSpent / project.budget) * 100 : 0

  return {
    progress: latestProgress?.completion || 0,
    safetyScore: avgSafetyScore,
    tasksCompleted: completedTasks,
    totalTasks,
    delayedTasks,
    budgetUtilization,
    totalSpent,
    daysRemaining: Math.ceil((new Date(project.endDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
  }
}

// Get projects summary for dashboard
export async function getProjectsSummary() {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }

  const projects = await prisma.project.findMany({
    where: { userId: user.id },
    include: {
      progress: {
        orderBy: { reportDate: 'desc' },
        take: 1,
      },
      _count: {
        select: {
          schedules: true,
          safety: true,
        },
      },
    },
  })

  const summary = {
    total: projects.length,
    active: projects.filter(p => p.status === 'ACTIVE').length,
    completed: projects.filter(p => p.status === 'COMPLETED').length,
    onHold: projects.filter(p => p.status === 'ON_HOLD').length,
    totalBudget: projects.reduce((sum, p) => sum + p.budget, 0),
    avgProgress: projects.length > 0
      ? projects.reduce((sum, p) => sum + (p.progress[0]?.completion || 0), 0) / projects.length
      : 0,
  }

  return { projects, summary }
}