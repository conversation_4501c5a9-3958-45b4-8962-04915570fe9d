import { NextRequest, NextResponse } from 'next/server'
import type { CompanyType } from '@/lib/company-types'
import type { DetectedMaterial, TakeoffItem } from '@/types'

// Enhanced console logger with emojis and colors
function log(level: string, message: string, data?: any) {
  const timestamp = new Date().toISOString()
  const levelEmoji = {
    error: '❌',
    warn: '⚠️',
    info: '✅',
    debug: '🔍',
    http: '🌐'
  }[level.toLowerCase()] || '📝'
  
  const prefix = `${levelEmoji} [${timestamp}] [TAKEOFF API] ${level}:`
  
  if (data) {
    console.log(prefix, message, JSON.stringify(data, null, 2))
  } else {
    console.log(prefix, message)
  }
}

// Test route
export async function GET(request: NextRequest) {
  log('info', 'GET /api/takeoff called')
  return NextResponse.json({ 
    message: 'Takeoff API is working!',
    timestamp: new Date().toISOString(),
    enhanced: true
  })
}

// Generate takeoff items with comprehensive logging
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  const requestId = `req-${Date.now()}`
  
  log('info', '🚀 POST /api/takeoff - Starting request', { requestId })
  
  try {
    const formData = await request.formData()
    const projectContextStr = formData.get('projectContext') as string
    const drawingFileBlob = formData.get('drawingFile') as Blob | null
    const isMultipleFiles = formData.get('multipleFiles') === 'true'
    const sessionId = formData.get('sessionId') as string | null
    
    // For multiple files, get all file entries
    const drawingFiles: Blob[] = []
    if (isMultipleFiles) {
      formData.forEach((value, key) => {
        if (key.startsWith('drawingFile_') && value instanceof Blob) {
          drawingFiles.push(value)
        }
      })
    } else if (drawingFileBlob) {
      drawingFiles.push(drawingFileBlob)
    }
    
    log('debug', '📋 Request details', {
      requestId,
      hasProjectContext: !!projectContextStr,
      hasDrawingFile: !!drawingFileBlob,
      isMultipleFiles,
      fileCount: drawingFiles.length,
      fileSizes: drawingFiles.map(f => f.size),
      fileTypes: drawingFiles.map(f => f.type)
    })

    if (!projectContextStr) {
      log('error', '❌ Missing project context', { requestId })
      return NextResponse.json(
        { error: 'Project context is required' },
        { status: 400 }
      )
    }

    const projectContext = JSON.parse(projectContextStr)
    
    log('info', '🏗️ Project context parsed', {
      requestId,
      projectId: projectContext.id,
      projectName: projectContext.name,
      projectType: projectContext.type,
      companyType: projectContext.companyType,
      location: projectContext.location
    })

    // For now, use the enhanced service directly
    try {
      const { enhancedTakeoffService } = await import('../../../lib/services/takeoff-service-v2')
      
      // Handle multiple files if needed
      if (isMultipleFiles && drawingFiles.length > 1) {
        log('info', '🔧 Using batch processor for multiple files', { requestId, fileCount: drawingFiles.length })
        
        const { batchProcessor } = await import('../../../lib/services/batch-processor')
        
        // Convert files to batch drawings
        const batchDrawings: any[] = []
        const fileNames = formData.getAll('fileNames') as string[]
        
        for (let i = 0; i < drawingFiles.length; i++) {
          const blob = drawingFiles[i]
          const fileName = fileNames[i] || `drawing-${i}.${blob.type.split('/')[1]}`
          const arrayBuffer = await blob.arrayBuffer()
          const base64 = Buffer.from(arrayBuffer).toString('base64')
          
          batchDrawings.push({
            id: `file-${i}`,
            fileName: fileName,
            imageData: base64,
            mimeType: blob.type
          })
        }
        
        // Process batch
        const batchJob = await batchProcessor.processBatch(batchDrawings, {
          maxConcurrency: 3,
          batchSize: 5,
          priorityMode: 'accuracy'
        })
        
        // Aggregate all items from all files
        const allItems: any[] = []
        
        for (let i = 0; i < batchJob.results.length; i++) {
          const result = batchJob.results[i]
          const fileName = fileNames[i] || result.fileName
          
          // Progressive callback for batch processing
          const batchProgressCallback = async (step: string, progress: number, data?: any) => {
            const batchProgress = Math.round((i / batchJob.results.length) * 100 + (progress / batchJob.results.length))

            log('info', `📊 Batch Progress [File ${i+1}/${batchJob.results.length}]: ${step} (${progress}%)`, {
              requestId,
              sessionId,
              fileIndex: i,
              fileName,
              step,
              progress,
              batchProgress,
              data
            })

            // Send batch progress update to API if session ID provided
            if (sessionId) {
              try {
                await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/takeoff/progress`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    sessionId,
                    step: `File ${i+1}/${batchJob.results.length}: ${step}`,
                    progress: batchProgress,
                    data: { ...data, fileIndex: i, fileName }
                  })
                })
              } catch (error) {
                log('warn', '⚠️ Failed to send batch progress update', { requestId, sessionId, error })
              }
            }
          }

          // Generate takeoff items for each processed drawing
          const items = await enhancedTakeoffService.generateTakeoffItems(
            {
              ...projectContext,
              id: `${projectContext.id}-file-${i}`,
              name: fileName.replace(/\.[^/.]+$/, '')
            },
            result,
            projectContext.companyType,
            batchProgressCallback
          )
          
          // Add source file information
          const itemsWithSource = items.map((item: any) => ({
            ...item,
            sourceFile: fileName,
            sourceFileIndex: i
          }))
          
          allItems.push(...itemsWithSource)
        }
        
        const totalCost = allItems.reduce((sum: number, item: any) => sum + item.totalCost, 0)
        const categories = Array.from(new Set(allItems.map((item: any) => item.category)))
        
        log('info', '✨ Batch takeoff generation completed!', {
          requestId,
          filesProcessed: batchJob.results.length,
          totalItemsGenerated: allItems.length,
          totalCost: totalCost.toLocaleString('en-US', { style: 'currency', currency: 'USD' }),
          categories,
          duration: `${Date.now() - startTime}ms`,
          averageCostPerItem: Math.round(totalCost / allItems.length)
        })
        
        return NextResponse.json({
          success: true,
          data: allItems,
          metadata: {
            requestId,
            processingTime: `${Date.now() - startTime}ms`,
            itemCount: allItems.length,
            totalCost,
            filesProcessed: batchJob.results.length,
            isBatch: true
          }
        })
      }
      
      // Single file processing (existing logic)
      log('info', '🔧 Using enhanced takeoff service', { requestId })
      
      // Create a file-like object from the blob without using File constructor
      let drawingFile: any = undefined
      if (drawingFiles.length > 0) {
        const drawingFileBlob = drawingFiles[0]
        const fileNames = formData.getAll('fileNames') as string[]
        const fileName = fileNames[0] || 'uploaded-drawing'
        
        // Determine file extension from MIME type
        const mimeToExt: Record<string, string> = {
          'application/pdf': 'pdf',
          'image/png': 'png',
          'image/jpeg': 'jpg',
          'image/jpg': 'jpg',
          'image/webp': 'webp'
        }
        const ext = mimeToExt[drawingFileBlob.type] || 'png'
        const fullFileName = fileName.includes('.') ? fileName : `${fileName}.${ext}`
        
        // Create a file-like object that the service can work with
        let fileBuffer: Buffer | null = null
        
        // If it's a PDF, we need to read the buffer for the PDF analyzer
        if (drawingFileBlob.type === 'application/pdf' || fullFileName.endsWith('.pdf')) {
          const arrayBuffer = await drawingFileBlob.arrayBuffer()
          fileBuffer = Buffer.from(arrayBuffer)
        }
        
        drawingFile = {
          name: fullFileName,
          type: drawingFileBlob.type,
          size: drawingFileBlob.size,
          // Add arrayBuffer method that returns the blob's array buffer
          arrayBuffer: async () => drawingFileBlob.arrayBuffer(),
          // Add buffer property that can be read
          buffer: fileBuffer
        }
        
        log('info', '📄 Drawing file prepared', {
          requestId,
          fileName: drawingFile.name,
          fileSize: drawingFile.size,
          mimeType: drawingFile.type
        })
      }
      
      log('info', '⚡ Generating takeoff items...', { requestId })

      // Progressive callback for real-time updates
      const progressCallback = async (step: string, progress: number, data?: any) => {
        log('info', `📊 Progress: ${step} (${progress}%)`, {
          requestId,
          sessionId,
          step,
          progress,
          data
        })

        // Send progress update to API if session ID provided
        if (sessionId) {
          try {
            await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/takeoff/progress`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ sessionId, step, progress, data })
            })
          } catch (error) {
            log('warn', '⚠️ Failed to send progress update', { requestId, sessionId, error })
          }
        }
      }

      const result = await enhancedTakeoffService.generateTakeoffItems(
        projectContext,
        drawingFile,
        projectContext.companyType,
        progressCallback
      )
      
      // Handle both array and object responses (for cross-trade situations)
      const items = Array.isArray(result) ? result : (result as any).items
      const crossTradeInfo = Array.isArray(result) ? null : (result as any).crossTradeInfo
      
      const totalCost = items.reduce((sum: number, item: any) => sum + item.totalCost, 0)
      const categories = Array.from(new Set(items.map((item: any) => item.category)))
      
      log('info', '✨ Takeoff generation completed!', {
        requestId,
        itemsGenerated: items.length,
        totalCost: totalCost.toLocaleString('en-US', { style: 'currency', currency: 'USD' }),
        categories,
        duration: `${Date.now() - startTime}ms`,
        averageCostPerItem: Math.round(totalCost / items.length)
      })
      
      // Log top 3 most expensive items
      const topItems = items
        .sort((a: any, b: any) => b.totalCost - a.totalCost)
        .slice(0, 3)
        .map((item: any) => ({
          description: item.description,
          cost: item.totalCost.toLocaleString('en-US', { style: 'currency', currency: 'USD' })
        }))
      
      log('info', '💰 Top 3 most expensive items', { requestId, topItems })
      
      return NextResponse.json({
        success: true,
        data: items,
        metadata: {
          requestId,
          processingTime: `${Date.now() - startTime}ms`,
          itemCount: items.length,
          totalCost,
          crossTradeInfo: crossTradeInfo || undefined
        }
      })
    } catch (serviceError) {
      log('error', '❌ Service error', {
        requestId,
        error: serviceError instanceof Error ? serviceError.message : 'Unknown service error',
        type: 'service_error',
        stack: serviceError instanceof Error ? serviceError.stack : undefined
      })
      
      // Special handling for PDF-related errors
      if (serviceError instanceof Error && serviceError.message.includes('pdf')) {
        log('warn', '⚠️ PDF processing issue detected, using AI-based estimation', { requestId })
      }
      
      // Re-throw the error so it properly returns a 500 error
      throw serviceError
    }
  } catch (error) {
    log('error', '❌ Request failed', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      type: 'request_error'
    })
    
    return NextResponse.json(
      { 
        error: 'Failed to generate takeoff items',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId
      },
      { status: 500 }
    )
  }
}

// Handle PUT requests for takeoff operations
export async function PUT(request: NextRequest) {
  const startTime = Date.now()
  const requestId = `req-${Date.now()}`
  
  log('info', '🔄 PUT /api/takeoff - Starting request', { requestId })
  
  try {
    const body = await request.json()
    const { operation, data, companyType } = body
    
    log('debug', '📊 Operation details', {
      requestId,
      operationType: operation,
      companyType,
      dataItems: data?.length
    })
    
    switch (operation) {
      case 'insights':
        log('info', '🤖 Generating AI insights', { requestId })
        const { getAIInsights } = await import('../../../lib/services/takeoff-service-server')
        const insights = await getAIInsights(data, companyType)
        log('info', '✅ Operation completed successfully', {
          requestId,
          operationType: operation,
          duration: `${Date.now() - startTime}ms`
        })
        return NextResponse.json({ data: insights })
        
      case 'categoryStats':
        log('info', '📈 Calculating category statistics', { requestId })
        const { getCategoryStats } = await import('../../../lib/services/takeoff-service-server')
        const stats = await getCategoryStats(data)
        log('info', '✅ Operation completed successfully', {
          requestId,
          operationType: operation,
          duration: `${Date.now() - startTime}ms`
        })
        return NextResponse.json({ data: stats })
        
      case 'costBreakdown':
        log('info', '💵 Generating cost breakdown', { requestId })
        const { getCostBreakdown } = await import('../../../lib/services/takeoff-service-server')
        const breakdown = await getCostBreakdown(data)
        log('info', '✅ Operation completed successfully', {
          requestId,
          operationType: operation,
          duration: `${Date.now() - startTime}ms`
        })
        return NextResponse.json({ data: breakdown })
        
      case 'estimate':
        log('info', '📊 Generating estimate', { requestId })
        const { generateEstimate } = await import('../../../lib/services/takeoff-service-server')
        const estimate = await generateEstimate(data.items, data.projectDetails, companyType)
        log('info', '✅ Operation completed successfully', {
          requestId,
          operationType: operation,
          duration: `${Date.now() - startTime}ms`
        })
        return NextResponse.json({ data: estimate })
        
      default:
        log('error', '❌ Unknown operation', { requestId, operation })
        return NextResponse.json(
          { error: 'Unknown operation' },
          { status: 400 }
        )
    }
  } catch (error) {
    log('error', '❌ Operation failed', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    
    return NextResponse.json(
      { 
        error: 'Failed to process takeoff operation',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId
      },
      { status: 500 }
    )
  }
}

// Process drawing
export async function PATCH(request: NextRequest) {
  const startTime = Date.now()
  const requestId = `req-${Date.now()}`
  
  log('info', '🎨 PATCH /api/takeoff - Processing drawing', { requestId })
  
  try {
    const formData = await request.formData()
    const drawingFileBlob = formData.get('drawingFile') as Blob
    const companyTypeStr = formData.get('companyType') as string | null
    const companyType = companyTypeStr as CompanyType | null
    
    log('debug', '📐 Drawing details', {
      requestId,
      fileSize: drawingFileBlob?.size,
      fileType: drawingFileBlob?.type,
      companyType
    })

    if (!drawingFileBlob) {
      log('error', '❌ Missing drawing file', { requestId })
      return NextResponse.json(
        { error: 'Drawing file is required' },
        { status: 400 }
      )
    }

    // Check if it's a PDF file
    const arrayBuffer = await drawingFileBlob.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    const base64 = buffer.toString('base64')
    
    // Detect PDF by checking the file signature
    const isPDF = base64.startsWith('JVBERi0') || drawingFileBlob.type === 'application/pdf'
    
    log('debug', '📄 File type detection', {
      requestId,
      mimeType: drawingFileBlob.type,
      isPDF,
      base64Preview: base64.substring(0, 10)
    })
    
    let result: { materials: DetectedMaterial[]; text: any[]; dimensions: any[]; annotations: any[]; confidence: number; processingTime: number; pdfProcessed: boolean; pageCount: number; } | { materials: any; confidence: any; processingTime: any; }
    
    if (isPDF) {
      // Use the enhanced takeoff service for PDF processing
      log('info', '📑 Processing PDF with enhanced takeoff service', { requestId })
      
      const { enhancedTakeoffService } = await import('../../../lib/services/takeoff-service-v2')
      const { pdfAnalyzer } = await import('../../../lib/services/pdf-analyzer')
      
      // Analyze PDF
      const pdfAnalysis = await pdfAnalyzer.analyzePDF(buffer, 'drawing.pdf', companyType)
      
      // Convert to a result format similar to vision service
      result = {
        materials: pdfAnalysis.extractedMaterials as DetectedMaterial[] || [],
        text: [],
        dimensions: [],
        annotations: [],
        confidence: 0.85,
        processingTime: Date.now() - startTime,
        pdfProcessed: true,
        pageCount: pdfAnalysis.pageCount
      }
      
      log('info', '✨ PDF processing completed', {
        requestId,
        materialsDetected: result.materials.length,
        pageCount: pdfAnalysis.pageCount,
        duration: `${Date.now() - startTime}ms`
      })
    } else {
      // Use vision service for image files
      log('info', '🖼️ Processing image with vision service', { requestId })
      
      const { visionService } = await import('../../../lib/services/vision-service')
      result = await visionService.analyzeDrawing(base64, companyType)
      
      log('info', '✨ Image processing completed', {
        requestId,
        materialsDetected: result.materials.length,
        confidence: result.confidence,
        duration: `${Date.now() - startTime}ms`
      })
    }
    
    return NextResponse.json({
      success: true,
      data: result,
      metadata: {
        requestId,
        processingTime: `${Date.now() - startTime}ms`,
        fileType: isPDF ? 'pdf' : 'image'
      }
    })
  } catch (error) {
    log('error', '❌ Drawing processing failed', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { 
        error: 'Failed to process drawing',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId
      },
      { status: 500 }
    )
  }
}